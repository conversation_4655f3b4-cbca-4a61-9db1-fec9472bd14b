<template>
    <div style="padding: 24px;">
        <div style="font-size: 14px; color: rgba(0,0,0,.85);">FSU主要信息：{{ fsumsg }}</div>
        <el-button style="margin: 12px 0;" type="primary" size="small" @click="queryData"
            :loading="refreshLoading">刷新</el-button>

        <el-table :data="fsuTableData" header-row-class-name="myHeaderClass" size="small" v-loading="tableLoading">

            <el-table-column prop="suId" align="center" label="mac地址" show-overflow-tooltip></el-table-column>
            <el-table-column prop="cpuUsage" align="center" label="CPU使用率"></el-table-column>
            <el-table-column prop="cpuUsage" align="center" label="内存利用率" show-overflow-tooltip></el-table-column>
            <el-table-column prop="registTime" align="center" label="最近注册时间"></el-table-column>
            <el-table-column prop="resetTime" align="center" label="最近重启时间" show-overflow-tooltip></el-table-column>
            <el-table-column prop="" label="操作" width="100">
                <template slot-scope="scope">
                    <div class="ad">
                        <!-- 操作日志 -->
                        <el-tooltip effect="dark" content="操作日志" placement="top">
                            <i @click="operateHistory(scope.row)" class="el-icon-tickets" style="font-size: 18px;"></i>
                        </el-tooltip>
                        <!--重启设备-->
                        <el-tooltip effect="dark" content="重启设备" placement="top" :disabled="disabledReset">
                            <i @click="restartDialog(scope.row, '确认重启FSU设备吗？')"
                                :class="['el-icon-refresh', disabledReset ? 'disabledReset' : '']"
                                style="font-size: 18px;"></i>
                        </el-tooltip>
                    </div>
                </template>
            </el-table-column>
        </el-table>

        <!-- 操作日志 -->
        <el-dialog :title="dialogTitle" :visible.sync="dialogTableVisible">
            <el-table size='small' :data="oprTableData" :height="250">
                <el-table-column property="userName" label="操作人"></el-table-column>
                <el-table-column property="oper" label="操作动作">
                    <template slot-scope="scope">
                        <div >重启FSU</div>
                    </template>
                </el-table-column>
                <el-table-column property="operStatus" label="操作状态">
                    <template slot-scope="scope">
                        <div v-if="scope.row.operStatus == 'SUCCESS'" style="color: green;">成功</div>
                        <div v-if="scope.row.operStatus == 'FAILURE'" style="color: red;">失败</div>
                    </template>
                </el-table-column>

                <el-table-column property="operateTime" label="操作时间"></el-table-column>
            </el-table>
            <div class="pag">
                <el-pagination @size-change="oprhandleSizeChange" background @current-change="oprhandleCurrentChange"
                    :current-page="oprcurrentPage" :page-sizes="[10, 20, 50]" :page-size="oprpageSize"
                    layout="total, sizes, prev, pager, next, jumper" :total="oprtotal">
                </el-pagination>
            </div>
        </el-dialog>
    </div>
</template>

<script>
export default {
    props: {
        fsuId: String,
    },
    watch: {
        fsuId() {
            //   this.currentPage = 1
            //   this.queryForm.signalName = ''
            //   this.goback()
            //   this.tableLoading = true

            this.queryData()
        },
        '$route.query': {
            immediate: true, // 如果需要在组件创建时立即触发，设置为true
            handler(newVal, oldVal) {
                // 当路由参数变化时，这里会被调用
                // newVal 是新的参数，oldVal 是旧的参数
                // console.log('Route parameter changed:', newVal);
                // 你可以在这里根据新的参数执行相应的操作
                //  this.fsumsg = newVal.fsumsg
            }
        }
    },
    data() {
        return {
            fsumsg: '',
            refreshLoading: false,

            fsuTableData: [],
            pageSize: 10,
            currentPage: 1,
            total: 0,
            // 操作日志
            dialogTitle: '',
            oprTableData: [],
            oprpageSize: 10,
            oprcurrentPage: 1,
            oprtotal: 0,
            dialogTableVisible: false,

            disabledReset: false,
        }
    },
    created() {
        this.queryData()
    },
    methods: {
        queryData(pageNum, pageSize) {
            this.tableLoading = true
            // if (pageNum) this.currentPage = pageNum
            // if (pageSize) this.pageSize = pageSize
            let params = {
                suId: this.fsuId,
                // current: pageNum || this.currentPage,
                // size: pageSize || this.pageSize
            }
            this.$api.getFsuInfo(params).then(res => {
                if (res.code == 200) {
                    this.fsumsg = res.data.roomInfo
                    this.fsuTableData = [
                        {
                            registTime: res.data.registTime.replace('T', ' '),
                            cpuUsage: `${res.data.cpuUsage}%`,
                            memUsage: `${res.data.memUsage}%`,
                            resetTime: res.data?.resetTime?.replace('T', ' '),
                            suId: res.data.suId
                        }
                    ]
                    this.tableLoading = false
                    this.total = res.data.total
                } else {
                    this.tableLoading = false
                    this.$message.error(res.msg)
                }
            })
        },


        restartDialog(row, title) {
            this.$confirm(title, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.restartFsu()
            }).catch(() => {

            });

        },
        async restartFsu() {
            let params = {
                suId: this.fsuId,
            }
            let res = await this.$api.fsuReset(params)

            if (res.code == 200) {
                this.disabledReset = true
                setTimeout(v => {
                    this.disabledReset = false
                }, 10 * 60 * 1000)
                this.$message.success('操作成功')

            } else if (res.code == 500) {


                this.$message.error(res.msg)
            }
        },
        // 操作日志
        async operateHistory() {
            let params = {
                suId: this.fsuId,
                current: this.oprcurrentPage,
                size: this.oprpageSize
            }
            let res = await this.$api.getFsuResetHostory(params)
            if (res.code == 200) {
                this.dialogTitle = `操作日志`
                this.oprTableData = res.data.records.map(v => {
                    v.operateTime = v.operateTime.replace('T', ' ')
                    return v
                })
                this.dialogTableVisible = true
                this.oprtotal = res.data.total
            } else {
                this.$message.error(res.msg)
            }
        },
        oprhandleSizeChange(val) {
            this.oprpageSize = val
            this.operateHistory(this.currentRow)
        },
        oprhandleCurrentChange(val) {
            this.oprcurrentPage = val
            this.operateHistory(this.currentRow)
        },
    }
}
</script>

<style lang="scss" scoped>
.ad {
    display: flex;
    gap: 6px;

    i {
        cursor: pointer;
    }

    .disabledReset {
        cursor: not-allowed;
        color: darkgray;
    }
}


.pag {
    display: flex;
    flex-direction: row-reverse;
    margin-top: 12px;
}
</style>