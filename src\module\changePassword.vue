<template>

    <div class="logotop">


        <div class="fomcont">
            <el-card style="padding: 12px 0;">
                <h2 style="text-align: center;margin-bottom: 18px;">修改密码</h2>
                <el-form :model="listform" :rules="rules" ref="listform" label-width="100px">
                    <el-form-item label="账户" prop="username">
                        <el-input :disabled="true" v-model.trim="listform.username" placeholder="请输入用户名"></el-input>
                    </el-form-item>
                    <el-form-item label="旧密码" placeholder="请输入密码" prop="password">
                        <el-input type="text" v-model="listform.password"></el-input>
                    </el-form-item>
                    <el-form-item label="新密码" placeholder="新密码" prop="newpassword">
                        <el-input type="text" clearable v-model="listform.newpassword"></el-input>
                    </el-form-item>
                    <el-form-item label="确认新密码" placeholder="确认新密码" prop="newpasswords">
                        <el-input type="text" clearable v-model="listform.newpasswords"></el-input>
                    </el-form-item>
                    <el-form-item label="" placeholder="" prop="" style="margin-bottom: 0;"> 
                        <el-button type="primary" style="width: 100%;" @click="submit('form')">确 定</el-button>
                        <!-- <div style="color: #F56C6C;">注：首次登录需要重置密码</div> -->
                    </el-form-item>

                </el-form>
            </el-card>

        </div>


    </div>
</template>

<script>


import {
    mapGetters
} from 'vuex';
import {
    updatePassword,
} from '@/api/login';
import { getUserId, removelocalStorage } from '@/utils/auth';
import aes from "@/utils/aes";
export default {
    components: {

    },
    data() {
        return {
            userName: '',
            listform: {
                username: getUserId(),
                password: '',
                newpassword: '',
                newpasswords: '',

            },
            rules: {
                username: [{ required: true, message: '请输入账户', trigger: 'blur' },
                { min: 3, max: 20, message: '长度在 3 到 20 个字符', trigger: 'blur' }
                ],
                password: [{ required: true, message: '请输入密码', trigger: 'blur' },],
                newpassword: [{ required: true, message: '请输入新密码', trigger: 'blur' },
                { pattern: /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{5,20}$/, message: '密码为数字，小写字母，大写字母，特殊符号 至少包含三种，长度为5-20位' }
                ],
                newpasswords: [{ required: true, message: '请输入新密码', trigger: 'blur' },
                { pattern: /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{5,20}$/, message: '密码为数字，小写字母，大写字母，特殊符号 至少包含三种，长度为5-20位' }
                ],
            },
            formload: false,
        }
    },
    computed: {
        ...mapGetters(['sidebar', 'name', 'avatar'])
    },
    watch: {

    },
    created() {
        console.log(this.$store.state.user.name)
        // console.log(this.defaultt);
    },
    methods: {

        cancel() {//修改密码取消
            let that = this;
            this.$refs["listform"].resetFields();
        },
        submit() {//修改密码确定
            let that = this;
            this.$refs.listform.validate(valid => {
                if (valid) {

                    if (that.listform.newpassword == that.listform.password) {
                        this.$message({
                            showClose: true,
                            message: "新密码与旧密码重复",
                            type: "warning",
                            duration: 1500

                        });
                        return;
                    };
                    if (that.listform.newpassword != that.listform.newpasswords) {
                        this.$message({
                            showClose: true,
                            message: "新密码和确认新密码不一致",
                            type: "warning",
                            duration: 1500

                        });
                        return;
                    };
                    that.formload = true;
                    let pass = that.listform.password.toString();
                    let newpassfis = that.listform.newpassword.toString();
                    let newpasssecond = that.listform.newpasswords.toString();
                    let oldpass = aes.encrypt(pass);
                    let newpass = aes.encrypt(newpassfis);
                    let newpasswords = aes.encrypt(newpasssecond);
                    let objs = {
                        'userName': that.listform.username,
                        'password': oldpass,
                        'newpass': newpass,
                        'qrpass': newpasswords,
                    }
                    //  console.log(objs);
                    //    return;
                    updatePassword(objs).then(data => {
                        if (data.flag == '1') {
                            that.$message({
                                showClose: true,
                                message: "密码修改成功" + '-请重新登录',
                                type: "success",
                                duration: 1500
                            });

                            setTimeout(() => {
                                this.$router.push({
                                    path: '/login'
                                });
                            }, 1000)

                        } else {
                            that.$message({
                                showClose: true,
                                message: data.msg,
                                type: "warning",
                                duration: 1500
                            });
                        };
                        that.formload = false;
                        that.dialogFormVisible = false;

                    }).catch((err) => {
                        that.formload = false;
                        that.$message({
                            showClose: true,
                            message: "网络错误",
                            type: "warning",
                            duration: 1500
                        });
                    })
                } else {
                    console.log('error submit!!');
                    return false;
                }
            });
        }
    }
}
</script>

<style rel="stylesheet/scss" lang="scss" scoped>
$padlef50: 30px;

.logotop {
    width: 100%;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
}

.fomcont {
    width: 600px;
    margin-top: 48px;
    border-radius: 15px;
    padding: 24px 24px 0;
}
</style>