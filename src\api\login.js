import http from '@/api/http'
import {
  getUserIds,
} from '@/utils/auth';
export function loginByEmail(username, password) {
  const data = {
    username,
    password
  };
  return http({
    url: '/api/auth/jwt/token',//'/api/admin/auths/getToken','/api/auth/jwt/token'
    method: 'post',
    data
  });
}

export function logout(token) {
  return http({
    url: '/api/auth/jwt/invalid',
    method: 'get',
    params: { token }
  });
}

export function getInfo(token) {
  return http({
    url: '/api/admin/user/front/info',
    method: 'get',
   // params: { token }
   params: { 'token':token,'id':getUserIds() }
  });
}

export function getMenus(token) {
  return http({
    url: '/api/admin/user/front/menus',
 //  url: '/api/admin/user/front/resourceMenus', //获取资源菜单
    method: 'get',
    params: { 'token':token,'id':getUserIds() }
  //  params: { token }
  });
}

export function getAllMenus() {
  return http({
    url: '/api/admin/user/front/menu/all',
    method: 'get'
  });
}
//获取一级菜单
export function getfirstTopMenus(token) {
  return http({
    url: '/api/admin/user/front/firstTopMenus',
 //  url: '/api/admin/user/front/resourceMenus', //获取资源菜单
    method: 'get',
    params: { 'token':token,'id':getUserIds() }
   // params: { token }
  });
}
//个人信息回显
export function getUserRouter(token) {
  return http({
    url: '/api/admin/user/front/router',
    method: 'get',
    params: { 'token':token,'id':getUserIds() }
   // params: { token }
  })
}
//修改密码 updatePassword  admin/user/updatePassword
export function updatePassword(dat) {
  return http({
    url: '/api/admin/user/updatePassword',
    method: 'post',
    data:dat,
  });
}


