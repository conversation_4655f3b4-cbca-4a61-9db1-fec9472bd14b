<!-- 定制任务页面 -->
<template>
  <div class="calendar-list-container disposefiles handFilter">
    <div class="filter-container" :style="{ paddingBottom: rowtyp == 'toview' ? '50px' : '' }">
      <el-form label-position="right" size="small" :model="listQuery" label-width="80px" label-suffix=":" class="demo-form-inline">
        <el-row>
          <el-col :span="12">
            <el-form-item label="标题">
              <div class="grid-content bg-purple">
                <el-input class="filter-item" placeholder="请输入标题" :readonly="rowtyp == 'toview'"
                  v-model="listQuery.titleName" clearable>
                </el-input>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="专业">
              <el-select v-model="listQuery.typecode" class="filter-item" placeholder="专业">
                <el-option v-for="(item, inde) in typelist" :key="inde" :label="item.data_id"
                  :value="item.data_id"></el-option>
              </el-select>
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
      <div style="height:300px">
        <quill-editor class="editor" ref="myTextEditor" v-model="content" :options="editorOption"
          @blur="onEditorBlur($event)" @focus="onEditorFocus($event)" @ready="onEditorReady($event)"
          @change="onEditorChange($event)">
        </quill-editor>
      </div>
      <div class="martop10">
        <uplodFile ref="uploadFiles" :rowtyp='rowtyp' :fileOrderNu='fileOrderNum'></uplodFile>
      </div>
      <div slot="footer" class="dialog-footer dialogFoot" style="margin-top: 15px;" v-if="rowtyp != 'toview'">
        <el-button type="primary" size="mini" :loading="btnload" @click="onSubmit">提交</el-button>
        <el-button size="mini" @click="cancel">重置</el-button>

      </div>
    </div>
  </div>
</template>

<script>

import {
  getUserId,
  getSysUserId,
} from '@/utils/auth';
import 'quill/dist/quill.core.css';
import 'quill/dist/quill.snow.css';
import 'quill/dist/quill.bubble.css';
import { uploadFile, saveOrderAddFile, deleteFile } from '@/api/require/sgzl/upload_file';
import {
  insertNotice,//新增
  updateNotice,//修改updateNotice
  selNoticeById,//查看
  getnewNotice,
} from '@/api/admin/user/index';
import uplodFile from './uplodfile.vue'
import qs from "qs";

export default {
  components: {
    "uplodFile": uplodFile,
  },
  props: {
    closeDiadl: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      content: null,
      listQuery: {
        titleName: '',
        typecode: '',//专业
        createUserId: getSysUserId(),
      },
      typelist: [],//专业下拉
      editorOption: {
        modules: {
          toolbar: [
            [ "bold", "italic", "underline", "strike"], // 加粗 斜体 下划线 删除线
            ["blockquote", "code-block"], // 引用  代码块
            [{ header: 1 }, { header: 2 }], // 1、2 级标题
            [{ list: "ordered" }, { list: "bullet" }], // 有序、无序列表
            [{ script: "sub" }, { script: "super" }], // 上标/下标
            [{ indent: "-1" }, { indent: "+1" }], // 缩进
            // [{'direction': 'rtl'}],                         // 文本方向
            [{ size: ["small", false, "large", "huge"] }], // 字体大小
            [{ header: [1, 2, 3, 4, 5, 6, false] }], // 标题
            [{ color: [] }, { background: [] }], // 字体颜色、字体背景颜色
            [{ font: [] }], // 字体种类
            [{ align: [] }], // 对齐方式
            ["clean"], // 清除文本格式
            // [] // 链接、图片、视频
          ], //工具菜单栏配置
        },
        placeholder: '请在这里添加发布内容', //提示
        readOnly: false, //是否只读
        theme: 'snow', //主题 snow/bubble
        syntax: true, //语法检测
      },
      btnload: false,
      rowId: '',
      rowtyp: '',
      tableData: [],
      fileList1: [],
      upload1: false,
      color: '#1890ff',
      headers: {},
      fileOrderNum: '',

    }
  },

  created() {

  },
  mounted() {
    // this.queryDateInit()
    // this.queryData()


  },
  computed: {
    //  editor() {
    //       return this.$refs.myTextEditor.quillEditor;
    //     }

  },
  methods: {
    getrowsId(id, typ) {
      this.rowId = id;
      this.rowtyp = typ;
      //console.log(typ)
      if (typ == 'toview') {//查看
        this.$refs.myTextEditor.quill.enable(false);
        this.toviewlist()

      } else if (typ == 'modify') {//修改
        this.toviewlist();
        this.$refs.myTextEditor.quill.enable(true);
        this.getnewNoticelist();
      } else if (typ == 'add') {//新增
        this.cancel();
        this.$refs.myTextEditor.quill.enable(true);
        this.getnewNoticelist();

      };


    },
    getnewNoticelist() {
      let that = this;
      that.typelist = [];
      that.$nextTick(() => {
        that.$refs.uploadFiles.getFilesTabeData();
      });
      getnewNotice().then(res => {
        //  console.log(res);
        that.typelist = res.typeList;
        that.fileOrderNum = res.id;
        if (this.rowtyp == 'add') {
          that.listQuery.typecode = that.typelist.length ? that.typelist[0].data_id : '';
        }

      }).catch(err => {

      });

    },
    onSubmit() {//提交
      //  console.log(this.rowtyp)
      if (this.rowtyp == 'add') {
        //  console.log(1111)
        this.insertNoticepost();

      } else if (this.rowtyp == 'modify') {
        this.updateNoticelist();
      }


    },
    cancel() {//取消
      let that = this;
      that.content = null;
      that.listQuery.titleName = '';
      that.listQuery.typecode = that.typelist.length ? that.typelist[0].data_id : '';
      that.btnload = false;
      // this.rowId = '';
      // this.rowtyp = '';

    },
    toviewlist() {
      let that = this;
      that.fileOrderNum = that.rowId;
      let pasmt = {
        id: that.rowId
      }
      selNoticeById(pasmt).then((data) => {
        that.listQuery.titleName = data.noticeTitle;
        that.content = data.noticeInfo;
        that.listQuery.typecode = data.noticeType;

        //    that.$message({
        //       showClose: true,
        //       message: '无数据',
        //       type: "warning",
        //       duration: 1500
        //  });

      }).catch((error) => {


      });
      if (that.rowtyp != 'modify') {
        that.$nextTick(() => {
          that.$refs.uploadFiles.getFilesTabeData();
        });
      }

    },
    // 失去焦点
    onEditorBlur(editor) { },
    // 获得焦点
    onEditorFocus(editor) {
      let that = this;


    },
    // 开始
    onEditorReady(editor) { },
    // 值发生变化
    onEditorChange(editor) {
      this.content = editor.html;
      // console.log(editor);
    },
    insertNoticepost() {//新增
      let that = this;
      if (!that.listQuery.titleName || !that.content) {
        that.$message({
          showClose: true,
          message: '标题和标题内容请输入',
          type: "warning",
          duration: 1500
        });
        return;
      }
      let pasmt = {
        'createBy': getSysUserId(),//发布人
        'noticeTitle': that.listQuery.titleName,//通知标题
        'noticeInfo': that.content,//通知内容
        'noticeType': that.listQuery.typecode,
        id: that.fileOrderNum,
      }
      that.btnload = true;
      insertNotice(pasmt).then((data) => {
        that.btnload = false;
        if (data.flag == '0') {
          that.$message({
            showClose: true,
            message: '新增成功',
            type: "success",
            duration: 1500
          });
          that.closeDiadl('1');
          that.cancel()
        } else {
          that.$message({
            showClose: true,
            message: '新增失败',
            type: "warning",
            duration: 1500
          });
        };
      }).catch((error) => {
        that.btnload = false;

      })

    },
    updateNoticelist() {
      //updateNotice
      let that = this;
      if (!that.listQuery.titleName || !that.content) {
        that.$message({
          showClose: true,
          message: '标题和标题内容请输入',
          type: "warning",
          duration: 1500
        });
        return;
      }
      let pasmt = {
        'createBy': getSysUserId(),//发布人
        'noticeTitle': that.listQuery.titleName,//通知标题
        'noticeInfo': that.content,//通知内容
        'id': that.rowId,
      }
      that.btnload = true;
      updateNotice(pasmt).then((data) => {
        that.btnload = false;
        if (data.flag == '0') {
          that.$message({
            showClose: true,
            message: '修改成功',
            type: "success",
            duration: 1500
          });
          that.closeDiadl('1');
          that.cancel()
        } else {
          that.$message({
            showClose: true,
            message: '修改失败',
            type: "warning",
            duration: 1500
          });
        };
      }).catch((error) => {
        that.btnload = false;

      })

    },



  }
}
</script>

<style>
.editor {
  line-height: normal !important;
  height: 255px;
}

.ql-snow .ql-tooltip[data-mode=link]::before {
  content: "请输入链接地址:";
}

.ql-snow .ql-tooltip.ql-editing a.ql-action::after {
  border-right: 0px;
  content: '保存';
  padding-right: 0px;
}

.ql-snow .ql-tooltip[data-mode=video]::before {
  content: "请输入视频地址:";
}

.ql-snow .ql-picker.ql-size .ql-picker-label::before,
.ql-snow .ql-picker.ql-size .ql-picker-item::before {
  content: '14px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=small]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=small]::before {
  content: '10px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=large]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=large]::before {
  content: '18px';
}

.ql-snow .ql-picker.ql-size .ql-picker-label[data-value=huge]::before,
.ql-snow .ql-picker.ql-size .ql-picker-item[data-value=huge]::before {
  content: '32px';
}

.ql-snow .ql-picker.ql-header .ql-picker-label::before,
.ql-snow .ql-picker.ql-header .ql-picker-item::before {
  content: '文本';
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="1"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="1"]::before {
  content: '标题1';
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="2"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="2"]::before {
  content: '标题2';
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="3"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="3"]::before {
  content: '标题3';
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="4"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="4"]::before {
  content: '标题4';
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="5"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="5"]::before {
  content: '标题5';
}

.ql-snow .ql-picker.ql-header .ql-picker-label[data-value="6"]::before,
.ql-snow .ql-picker.ql-header .ql-picker-item[data-value="6"]::before {
  content: '标题6';
}

.ql-snow .ql-picker.ql-font .ql-picker-label::before,
.ql-snow .ql-picker.ql-font .ql-picker-item::before {
  content: '标准字体';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=serif]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=serif]::before {
  content: '衬线字体';
}

.ql-snow .ql-picker.ql-font .ql-picker-label[data-value=monospace]::before,
.ql-snow .ql-picker.ql-font .ql-picker-item[data-value=monospace]::before {
  content: '等宽字体';
}
</style>
<style lang="scss">
// @import "src/styles/common.scss";</style>