@import './iconfont/iconfont.css';


@font-face {
  font-family: 'YouSheBiaoTiHei';
  src: url('./YouSheBiaoTiHei.ttf') format('truetype'), /* 对于老版本的iOS */
       url('./YouSheBiaoTiHei.ttf') format('woff'); /* 现代浏览器 */
  font-weight: normal;
  font-style: normal;
}


.my_checkbox {
  margin-right: 0 !important;
}

.el-menu--popup-bottom-start {
  margin-top: 0;
}

.el-tooltip__popper {
  max-width: 300px !important;
  line-height: 20px;
}

.el-tooltip__popper.is-light {
  line-height: 22px;
  color: rgba(0, 0, 0, 0.65) !important;
  border: none !important;
  box-shadow: 0px 9px 28px 8px rgba(0, 0, 0, 0.05), 0px 6px 16px 0px rgba(0, 0, 0, 0.08),
    0px 3px 6px -4px rgba(0, 0, 0, 0.12);
  font-size: 14px !important;

  &[x-placement^='right'] {
    max-width: 350px !important;

    .popper__arrow {
      border-right-color: #fff !important;
    }
  }

  &[x-placement^='left'] {
    max-width: 350px !important;

    .popper__arrow {
      border-left-color: #fff !important;
    }
  }

  &[x-placement^='top'] {
    max-width: 200px !important;

    .popper__arrow {
      border-top-color: #fff !important;
    }
  }

  &[x-placement^='bottom'] {
    max-width: 200px !important;

    .popper__arrow {
      border-bottom-color: #fff !important;
    }
  }
}

.my_pop {
  padding: 0 !important;
}

.el-carousel {
  .el-carousel__indicators {
    .el-carousel__button {
      margin-bottom: 20px;
      height: 10px;
      border-radius: 10px;
      width: 32px;
    }
  }
}

.el-table__empty-block {
  background: url('~@/assets/images/page-no-data-s.svg') center 50px no-repeat;
  padding-top: 160px;
  height: auto !important;
  padding-bottom: 50px;
}

.radio-view {
  .el-radio-group {
    line-height: 44px;
    display: block;
    font-size: 14px;
  }
}

.login-box {
  input::-webkit-input-placeholder {
    color: rgba(0, 0, 0, 0.25) !important;
    font-size: 16px !important;
    font-weight: 400 !important;
  }

  input::-moz-input-placeholder {
    color: rgba(0, 0, 0, 0.25) !important;
    font-size: 16px !important;
    font-weight: 400 !important;
  }

  input::-ms-input-placeholder {
    color: rgba(0, 0, 0, 0.25) !important;
    font-size: 16px !important;
    font-weight: 400 !important;
  }
}

.my-input {
  input::-webkit-input-placeholder {
    color: rgba(0, 0, 0, 0.45) !important;
    font-size: 16px !important;
    // font-weight: 400 !important;
  }

  input::-moz-input-placeholder {
    color: rgba(0, 0, 0, 0.45) !important;
    font-size: 16px !important;
    // font-weight: 400 !important;
  }

  input::-ms-input-placeholder {
    color: rgba(0, 0, 0, 0.45) !important;
    font-size: 16px !important;
    // font-weight: 400 !important;
  }

  .el-input__inner {
    font-size: 16px !important;
  }
}

input::-webkit-input-placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
  font-size: 14px !important;
}

input::-moz-input-placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
  font-size: 14px !important;
}

input::-ms-input-placeholder {
  color: rgba(0, 0, 0, 0.25) !important;
  font-size: 14px !important;
}

.BMap_bubble_title {
  padding: 6px 0 0 0px;
  font-weight: bold;
  color: rgba(0, 0, 0, .85);
  font-size: 16px;
}

.BMap_bubble_pop {
  border-radius: 2px !important;

}

/deep/.markdown-body img {
  width: auto;
  height: auto;
}

.el-step__title {
  font-size: 14px;
}

.el-tabs__item {
  font-size: 14px;
}

.myinput {
  .el-input__inner {
    background-color: #101C40;
    color: #fff;
    border-radius: 4px;
    border-color: #101C40;
    &::placeholder {
      font-size: 12px;
      color: rgba(255, 255, 255, .85) !important;
    }
  }
  .el-autocomplete-suggestion {
    background-color: rgba(50,53,68,.9);
    color: #727C94;
  }
  .el-autocomplete-suggestion li {
    color: #fff;
  }
  .el-autocomplete-suggestion li:hover {
    background-color: transparent;
    color: #409EFF;
  }
}

.tree-box {
 
   background: transparent;
   color: #fff;
  font-size: 14px;
 }

 .el-tree-node:focus>.el-tree-node__content {
  background-color: transparent;
   color: #409EFF;
 }

 .el-tree-node__content:hover {
   background-color: transparent;
   color: #409EFF;
 }

 .el-tree--highlight-current .el-tree-node.is-current>.el-tree-node__content {
  background-color: transparent;
  color: #409EFF;
 }
 .el-tree-node>.el-tree-node__children {
  overflow: visible;
 }

 .app-container {
  padding: 18px;
  border-top: 1px solid #dcdfe6;
 }

 .pag {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 12px;
}

.el-table {
  color: #2D3E53;
}

.el-table th.el-table__cell {
  background-color: #F9F9F9;
  color: #A3AAB3;
}

// .myWarnTable .el-table__body tr.el-table__row:hover {
//   background-color: red !important ;
// }





.warn1 {
  background: #FF4343 !important;
  color: #000;
}
.warn2 {
  background: #FF8842 !important;
  color: #000;
}
.warn3 {
  background: #FFB800 !important;
  color: #000;
}
.el-table .el-table__body tbody .el-table__row:hover {
  background-color: none  ;
}
  