import http from '@/api/http'

export function page(query) {
  return http({
    url: '/api/flowable/flow/page',
    method: 'get',
    params: query
  });
}

export function getJobs(obj) {
  return http({
    url: '/api/flowable/flow/getJobs',
    method: 'post',
    data: obj
  });
}

export function saveObj(obj) {
  return http({
    url: '/api/flowable/flow/saveObj',
    method: 'post',
    data: obj
  });
}

export function getObjFormMaking(obj) {
  return http({
    url: '/api/flowable/flow/getObj',
    method: 'post',
    data: obj
  });
}

export function updObj(obj) {
  return http({
    url: '/api/flowable/flow/updObj',
    method: 'post',
    data: obj
  });
}

export function getOptions(obj) {
  return http({
    url: '/api/flowable/flow/getOptions',
    method: 'post',
    data: obj
  });
}

export function relePre(obj) {
  return http({
    url: '/api/flowable/flow/relePre',
    method: 'post',
    data: obj
  });
}

export function releFormal(obj) {
  return http({
    url: '/api/flowable/flow/releFormal',
    method: 'post',
    data: obj
  });
}

export function loadObj(obj) {
  return http({
    url: '/api/flowable/flow/loadObj',
    method: 'post',
    data: obj
  });
}

export function submitObj(obj) {
  return http({
    url: '/api/flowable/flow/submitObj',
    method: 'post',
    data: obj
  });
}

export function isRelaAttr(obj) {
  return http({
    url: '/api/flowable/flow/isRelaAttr',
    method: 'post',
    data: obj
  });
}

export function getObj(obj) {
  return http({
    url: '/api/flowable/orderInfo/showWorkInfos',
    method: 'post',
    data: obj
  });
}

export function getBussObj(obj) {
  return http({
    url: '/api/flowable/orderInfo/showBusinessInfos',
    method: 'post',
    data: obj
  });
}

export function getTabObj(obj) {
  return http({
    url: '/api/flowable/orderInfo/selectTableData',
    method: 'post',
    data: obj
  });
}

export function getFilesTabObj(obj) {
  return http({
    url: '/api/admin/noticeCon/file/list',
    method: 'post',
    data: obj
  });
}

export function saveWorkInfos(obj) {
  return http({
    url: '/api/flowable/orderInfo/saveWorkInfos',
    method: 'post',
    data: obj
  });
}
export function batchSaveWorkInfos(obj) {
  return http({
    url: '/api/flowable/orderInfo/batchSaveWorkInfos',
    method: 'post',
    data: obj
  });
}
export function saveTabObj(obj) {
  return http({
    url: '/api/flowable/orderInfo/saveTableData',
    method: 'post',
    data: obj
  });
}

export function showImg(obj) {
  return http({
    url: '/api/flowable/processInstanceImage/image',
    method: 'get',
    params: obj,
    responseType: 'blob'
  })
}


export function qryZw(obj) {
  return http({
    url: '/api/watchme/requireKhgcCon/qryZw',
    method: 'post',
    data: obj
  });
}

export function saveBuildModel(obj) {
  return http({
    url: '/api/watchme/requireKhgcCon/saveBuildModel',
    method: 'post',
    data: obj
  });
}
//流程历史查询
export function showWorkHandleInfos(obj) {
  return http({
    url: '/api/flowable/orderInfo/showWorkHandleInfos',
    method: 'post',
    data: obj
  });
}
// showJobInfo  查询历史记录 新
export function showJobInfo(obj) {
  return http({
    url: '/api/flowable/orderInfo/showJobInfo',
    method: 'post',
    data: obj
  });
}
// showWorkInfoRun  查询必填信息 新
export function showWorkInfoRun(obj) {
  return http({
    url: '/api/flowable/orderInfo/showWorkInfoRun',
    method: 'post',
    data: obj
  });
}
