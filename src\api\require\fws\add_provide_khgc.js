import http from '@/api/http'

export function showAddProvider(obj) {
  return http({
    url: '/api/watchme/requireProviderCon/showAddProvider',
    method: 'post',
    data: obj
  });
}

export function qryProvider(obj) {
  return http({
    url: '/api/watchme/requireProviderCon/qryProvider',
    method: 'post',
    data: obj
  });
}

export function selectUserBidding(obj) {
  return http({
    url: '/api/watchme/requireProviderCon/selectUserBidding',
    method: 'post',
    data: obj
  });
}

export function addProvider(obj) {
  return http({
    url: '/api/watchme/requireProviderCon/addProvider',
    method: 'post',
    data: obj
  });
}

export function delTable(url, obj) {
  return http({
    url: url,
    method: 'post',
    data: obj
  });
}

