<template>
    <div class="app-container calendar-list-container disposefiles  usercontainer">
        <div class="filter-container">
            <div id="sizeForms" class="search-box borderBottom paddbom5" ref="sizeForms">
                <el-form :model="listQuery" ref="sizeForm" label-position="right" size="small" label-width="80px"
                    label-suffix=":" class="demo-form-inline">
                    <el-row :gutter="10">
                        <el-col :xs="8" :sm="8" :md="6" :lg="6" :xl="4">
                            <el-form-item label="地市">
                                <el-select clearable class="filter-item" style="width: 100%;" v-model="listQuery.area"
                                    placeholder="请选择" @change="getpostcounuty1">
                                    <el-option v-for="(item, inde) in citieCounty" :key="inde" :label="item.VALUE"
                                        :value="item.KEYSS"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="8" :sm="8" :md="6" :lg="6" :xl="4">
                            <el-form-item label="区县">
                                <el-select clearable class="filter-item" style="width: 100%;"
                                    v-model="listQuery.cityArea" placeholder="请选择" >
                                    <el-option v-for="(item, inde) in countydata1" :key="inde" :label="item.VALUE"
                                        :value="item.KEYSS"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="8" :sm="8" :md="6" :lg="6" :xl="4">
                            <el-form-item label="账号">
                                <el-input clearable class="filter-item" placeholder="账号"
                                    v-model.trim="listQuery.username"> </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="8" :sm="8" :md="6" :lg="6" :xl="4">
                            <el-form-item label="姓名">
                                <el-input clearable class="filter-item" placeholder="姓名" v-model.trim="listQuery.name">
                                </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="8" :sm="8" :md="6" :lg="6" :xl="4">
                            <el-form-item label="电话">
                                <el-input clearable class="filter-item" placeholder="电话"
                                    v-model.trim="listQuery.mobile_phone"> </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="8" :sm="8" :md="6" :lg="6" :xl="4">
                            <el-form-item label="身份证号">
                                <el-input clearable class="filter-item" placeholder="身份证号"
                                    v-model.trim="listQuery.user_sfz"> </el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :xs="8" :sm="8" :md="6" :lg="6" :xl="4">
                            <el-form-item label="所属角色">
                                <el-select clearable class="filter-item" style="width: 100%;"
                                    v-model="listQuery.role_name" placeholder="请选择所属角色">
                                    <el-option v-for="(item, inde) in rolelist" :key="inde" :label="item.label"
                                        :value="item.label"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <!--<el-col :xs="8" :sm="8" :md="6" :lg="6" :xl="4">
                        <el-form-item label="人员归属">
                            <el-select class="filter-item" v-model="listQuery.user_type" placeholder="请选择人员归属">
                                <el-option v-for="(item,inde) in poptypelist" :key="inde" :label="item.VALUE" :value="item.KEYSS"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>-->
                        <el-col :xs="8" :sm="8" :md="6" :lg="6" :xl="4">
                            <el-form-item class="formbtn text-right">
                                <el-button style="margin-left: 24px;" type="primary" v-waves icon="search"
                                    @click="handleFilter">查询</el-button>
                                <el-button v-waves @click="btnresetTemp">重置</el-button>
                                <!--<el-button :loading="btnloading" type="primary" @click="handlEexport">导出</el-button>-->
                                <el-button v-if="userManager_btn_add" @click="handleCreate" type="primary"
                                    icon="edit">添加</el-button>
                            </el-form-item>
                        </el-col>
                    </el-row>
                </el-form>
            </div>
            <div class="listTables martop10">
                <el-table :key='tableKey' size="small" :data="list" v-loading.body="listLoading" fit
                    highlight-current-row style="width: 100%">
                    <el-table-column align="left" label="序号" width="50" type="index"> </el-table-column>
                    <el-table-column width="100" align="left" label="姓名"> <template slot-scope="scope">
                            <span>{{ scope.row.name }}</span>
                        </template> </el-table-column>
                    <el-table-column width="120" align="left" label="账户"> <template slot-scope="scope">
                            <span>{{ scope.row.username }}</span>
                        </template> </el-table-column>
                    <el-table-column width="130" align="left" label="电话"> <template slot-scope="scope">
                            <span>{{ scope.row.mobile_phone }}</span>
                        </template> </el-table-column>

                    <el-table-column width="100" align="left" label="地市"> <template slot-scope="scope">
                            <span>{{ scope.row.area_name }}</span>
                        </template> </el-table-column>
                    <el-table-column width="100" align="left" label="区县"> <template slot-scope="scope">
                            <span>{{ scope.row.city_name }}</span>
                        </template> </el-table-column>
                    <!--<el-table-column width="130" align="left" label="部门"> <template slot-scope="scope">
                        <span>{{scope.row.depart_name}}</span>
                    </template> </el-table-column>-->

                    <el-table-column width="70" align="left" label="性别"> <template slot-scope="scope">
                            <span>{{ scope.row.sex }}</span>
                        </template> </el-table-column>
                    <el-table-column width="150" align="left" label="所属角色"> <template slot-scope="scope">
                            <span>{{ scope.row.role_name }}</span>
                        </template> </el-table-column>
                    <!--<el-table-column width="150" align="left" label="人员归属"> <template slot-scope="scope">
                        <span>{{scope.row.user_typename}}</span>
                    </template> </el-table-column>-->
                    <el-table-column width="160" align="left" label="身份证号"> <template slot-scope="scope">
                            <span>{{ scope.row.user_sfz }}</span>
                        </template> </el-table-column>
                    <el-table-column min-width="120" align="left" label="备注"> <template slot-scope="scope">
                            <span>{{ scope.row.description }}</span>
                        </template> </el-table-column>
                    <el-table-column min-width="170" align="left" label="创建时间"> <template slot-scope="scope">
                            <span>{{ scope.row.crt_time }}</span>
                        </template> </el-table-column>

                    <!--<el-table-column width="100" align="left" label="最后更新人"> <template slot-scope="scope" >
              <span>{{scope.row.updName}}</span>
            </template> </el-table-column>-->

                    <el-table-column align="center" label="操作" fixed="right" width="330"> <template slot-scope="scope">
                            <el-button v-if="userManager_btn_edit" size="small" type="text"
                                @click="handleUpdate(scope.row)">编辑
                            </el-button>
                            <el-button v-if="userManager_btn_resetpass" size="small" type="text"
                                @click="handleResetPwd(scope.row)">重置密码
                            </el-button>
                            <!-- <el-button v-if="userManager_btn_resetpass" size="small" type="success"
                                @click="handleunbund(scope.row)">手机IMEI解绑
                            </el-button> -->
                            <el-button v-if="userManager_btn_del" size="small" style="color: #f56c6c;" type="text"
                                @click="handleDelete(scope.row)">删除
                            </el-button>
                        </template> </el-table-column>
                </el-table>
            </div>
            <div v-show="!listLoading" class="pag">
                <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
                    :current-page.sync="listQuery.page" :page-sizes="[10, 15, 20, 30, 50]" :page-size="listQuery.limit"
                    layout="total, sizes, prev, pager, next, jumper" :total="total"> </el-pagination>
            </div>
            <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="1000px"
                :close-on-click-modal="false">
                <el-row class="userrows">
                    <el-form :model="form" :rules="rules" ref="form" label-width="90px">
                        <el-col :span="12">
                            <el-form-item label="姓名" prop="name">
                                <el-input class="filter-item" v-model="form.name" placeholder="请输入姓名"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="账户" prop="username">
                                <el-input class="filter-item" v-if="dialogStatus == 'create'" v-model="form.username"
                                    placeholder="请输入账户"></el-input>
                                <el-input class="filter-item" v-else v-model="form.username" placeholder="请输入账户"
                                    readonly></el-input>
                            </el-form-item>
                        </el-col>
                        <!-- <el-col :span="12">
                            <el-form-item v-if="dialogStatus == 'create'" label="密码" placeholder="请输入密码"
                                prop="password">
                                <el-input class="filter-item" type="text" v-model="form.password"></el-input>
                            </el-form-item>
                        </el-col> -->
                        <!--                    <el-col :span="12">
                        <el-form-item label="人员归属" prop="user_type">
                            <el-select class="filter-item" v-model="form.user_type" placeholder="请选择">
                                <el-option v-for="(item,inde) in poptypelist" :key="inde" :label="item.VALUE" :value="item.KEYSS"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>-->
                        <el-col :span="12">
                            <el-form-item label="所属角色" prop="role_id">
                                <el-select class="filter-item" v-model="form.role_id" multiple collapse-tags
                                    placeholder="请选择所属角色">
                                    <el-option v-for="(item, inde) in rolelist" :key="inde" :label="item.label"
                                        :value="item.id"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="地市" prop='cityCode'>
                                <el-select class="filter-item" multiple :multiple-limit="1" v-model="form.cityCode"
                                    placeholder="请选择" @change="getpostcounuty">
                                    <el-option v-for="(item, inde) in citieCounty" :key="inde" :label="item.VALUE"
                                        :value="item.KEYSS"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="区县">
                                <el-select class="filter-item" v-model="form.areaCode" :multiple-limit="1" multiple
                                    collapse-tags placeholder="请选择">
                                    <el-option v-for="(item, inde) in countydata" :key="inde" :label="item.VALUE"
                                        :value="item.KEYSS"></el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <!--                    <el-col :span="12">
                        <el-form-item label="部门" prop="buCode">
                            &lt;!&ndash; <el-select class="filter-item" v-model="form.buCode" placeholder="请选择部门">
          <el-option v-for="(item,inde) in buMdata" :key="inde" :label="item.VALUE" :value="item.KEY"></el-option>
        </el-select> &ndash;&gt;
                            <el-cascader class="filter-item" v-model="form.buCode" :options="buMdata" :show-all-levels="false" :props="{checkStrictly: true ,value:'DEPART_NO',label:'DEPART_NAME'}"></el-cascader>
                        </el-form-item>
                    </el-col>-->
                        <el-col :span="12">
                            <el-form-item label="性别">
                                <el-select class="filter-item" v-model="form.sex" placeholder="请选择">
                                    <el-option v-for="item in  sexOptions" :key="item" :label="item" :value="item">
                                    </el-option>
                                </el-select>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="邮箱" prop="email">
                                <el-input class="filter-item" v-model="form.email" placeholder="请输入账户"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="手机号" prop="mobilePhone">
                                <el-input class="filter-item" v-model="form.mobilePhone"
                                    placeholder="请输入手机号"></el-input>
                            </el-form-item>
                        </el-col>
                        <el-col :span="12">
                            <el-form-item label="身份证号">
                                <el-input class="filter-item" v-model="form.user_sfz" placeholder="请输入身份证号码"
                                    clearable></el-input>
                            </el-form-item>
                        </el-col>
                        <!-- <el-col :span="12" v-else>
      <el-form-item label="身份证号" required>
        <el-input class="filter-item" v-model="form.user_sfz" placeholder="请输入身份证号码" clearable></el-input>
      </el-form-item>
      </el-col> -->
                        <el-col :span="12">
                            <el-form-item label="描述">
                                <el-input class="filter-item" type="textarea" :autosize="{ minRows: 3, maxRows: 5 }"
                                    placeholder="请输入内容" v-model="form.description"> </el-input>
                            </el-form-item>
                        </el-col>
                    </el-form>
                </el-row>
                <div slot="footer" class="dialog-footer">
                    <el-button @click="cancel('form')">取 消</el-button>
                    <el-button v-if="dialogStatus == 'create'" type="primary" @click="create('form')">确 定</el-button>
                    <el-button v-else type="primary" @click="update('form')">确 定</el-button>
                </div>
            </el-dialog>
        </div>
    </div>
</template>

<script>
import {
    page,
    addObj,
    getObj,
    delObj,
    updateUser,
    resetPassword,
    getDepartAll,
    getUserInfo,
    getComCityList,
    getcounuty,
    postFoundedUnbund,//创立解绑
} from '@/api/admin/user/index';
import {
    fetchTree,
} from '@/api/admin/group/index';

import {
    getUserId,
    getSysUserId,
} from '@/utils/auth';
import aes from "@/utils/aes";
import { downloadfilter } from '@/api/report/index'
import {
    mapGetters
} from 'vuex';
const idCardValidity = (rule, code, callback) => {
    var city = {
        11: "北京",
        12: "天津",
        13: "河北",
        14: "山西",
        15: "内蒙古",
        21: "辽宁",
        22: "吉林",
        23: "黑龙江 ",
        31: "上海",
        32: "江苏",
        33: "浙江",
        34: "安徽",
        35: "福建",
        36: "江西",
        37: "山东",
        41: "河南",
        42: "湖北 ",
        43: "湖南",
        44: "广东",
        45: "广西",
        46: "海南",
        50: "重庆",
        51: "四川",
        52: "贵州",
        53: "云南",
        54: "西藏 ",
        61: "陕西",
        62: "甘肃",
        63: "青海",
        64: "宁夏",
        65: "新疆",
        71: "台湾",
        81: "香港",
        82: "澳门",
        91: "国外 "
    };
    var tip = ""
    var pass = true

    if (!code || !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(code)) {
        tip = "身份证号格式错误"
        pass = false;
    } else if (!city[code.substr(0, 2)]) {
        tip = "地址编码错误"
        pass = false
    } else {
        // 18位身份证需要验证最后一位校验位
        if (code.length === 18) {
            code = code.split('')
            // ∑(ai×Wi)(mod 11)
            // 加权因子
            var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
            // 校验位
            var parity = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
            var sum = 0
            var ai = 0
            var wi = 0
            for (var i = 0; i < 17; i++) {
                ai = code[i]
                wi = factor[i]
                sum += ai * wi
            }
            var last = parity[sum % 11];
            if (parity[sum % 11] != code[17]) {
                tip = "校验位错误"
                pass = false
            }
        }
    }
    if (!pass) {
        callback(new Error(tip))
    } else {
        callback()
    }
    // if (!pass) alert(tip)
    // return pass
}
export default {
    name: 'user',
    data() {
        return {
            form: {
                username: undefined,
                name: undefined,
                sex: '男',
                password: undefined,
                description: undefined,
                cityCode: undefined,
                areaCode: [],
                buCode: [], //部门
                mobilePhone: undefined,
                email: undefined,
                user_type: null, //归属人员
                user_sfz: null,
                user_sfz1: null,
                role_id: [], //所属角色
            },
            rules: {
                name: [{
                    required: true,
                    message: '请输入用户',
                    trigger: 'blur'
                },
                {
                    min: 1,
                    max: 20,
                    message: '长度在 1 到 20 个字符',
                    trigger: 'blur'
                }
                ],
                username: [{
                    required: true,
                    message: '请输入账户',
                    trigger: 'blur'
                },
                {
                    min: 3,
                    max: 20,
                    message: '长度在 3 到 20 个字符',
                    trigger: 'blur'
                }
                ],
                password: [{
                    required: true,
                    message: '请输入密码',
                    trigger: 'blur'
                },
                {
                    min: 5,
                    max: 20,
                    message: '长度在 5 到 20 个字符',
                    trigger: 'blur'
                },
                {
                    pattern: /^(?![a-zA-Z]+$)(?![A-Z0-9]+$)(?![A-Z\W_]+$)(?![a-z0-9]+$)(?![a-z\W_]+$)(?![0-9\W_]+$)[a-zA-Z0-9\W_]{5,20}$/,
                    message: '密码为数字,小写字母,大写字母,特殊符号 至少包含三种,长度为5-20位'
                }

                ],
                user_type: [{
                    required: true,
                    message: '请选择',
                    trigger: 'change'
                },],
                role_id: [{
                    required: true,
                    message: '请选择',
                    trigger: 'change'
                },],
                cityCode: [{
                    required: true,
                    message: '请选择',
                    trigger: 'change'
                },],
                email: [{
                    required: true,
                    pattern: /^([a-zA-Z0-9_-])+@([a-zA-Z0-9_-])+(.[a-zA-Z0-9_-])+/,
                    message: '邮箱格式错误'
                }],
                areaCode: [{
                    required: true,
                    message: '请选择区县',
                    trigger: 'change'
                }],
                mobilePhone: [{
                    required: true,
                    pattern: /^((13|14|15|16|17|18|19)[0-9]{1}\d{8})$/,
                    message: '手机号校验失败'
                }],
                user_sfz: [{
                    required: true,
                    message: '请输入身份证件号',
                    trigger: 'blur'
                },
                    // {
                    //   pattern: /(^\d{8}(0\d|10|11|12)([0-2]\d|30|31)\d{3}$)|(^\d{6}(18|19|20)\d{2}(0\d|10|11|12)([0-2]\d|30|31)\d{3}(\d|X|x)$)/,
                    //   message: '请输入正确的身份证件号',
                    //   trigger: 'blur'
                    // },
                    // { validator: idCardValidity, trigger: 'blur' }
                ],
                buCode: [{
                    required: true,
                    message: '请选择部门',
                    trigger: 'change'
                },],
            },
            list: null,
            total: null,
            listLoading: true,
            listQuery: {
                page: 1,
                limit: 10,
                username: undefined,
                role_name: '',
                user_type: '', //人员归属
                user_sfz: '', //身份证
                area: '',
                cityArea: '',
            },
            countydata1: [],
            sexOptions: ['男', '女'],
            dialogFormVisible: false,
            dialogStatus: '',
            userManager_btn_edit: false,
            userManager_btn_del: false,
            userManager_btn_add: false,
            userManager_btn_resetpass: false,
            textMap: {
                update: '编辑',
                create: '创建'
            },
            tableKey: 0,
            citieCounty: [], //地市
            city_id: '330100',
            countydata: [], //区县
            buMdata: [], //部门
            tableheight: 400,
            pageSize: 10,
            poptypelist: [{
                'VALUE': '铁塔公司自有人员',
                'KEYSS': '01'
            }, {
                'VALUE': '铁塔公司非自有人员',
                'KEYSS': '02'
            }, {
                'VALUE': '运营商客户',
                'KEYSS': '03'
            }],
            rolelist: [],
            userId: getSysUserId(),
            btnloading: false,

        }
    },
    created() {

        // console.log(JSON.parse(sessionStorage.getItem('elements')))

        // let bodyheight = document.documentElement.clientHeight;
        // this.tableheight = parseInt(bodyheight) > 800 ? 570 : 400;
        // this.listQuery.limit = parseInt(bodyheight) > 800 ? 15 : 10;
        // this.pageSize = this.listQuery.limit;
        this.getList();
        this.userManager_btn_edit = this.elements['userManager:btn_edit'];
        this.userManager_btn_del = this.elements['userManager:btn_del'];
        this.userManager_btn_add = this.elements['userManager:btn_add'];
        this.userManager_btn_resetpass = this.elements['userManager:btn_pass']

    },
    mounted() {
        //获取地市
        this.getCitiList();
        // console.log(2222)
        this.getList1();
    },
    computed: {
        ...mapGetters([
            'elements'
        ])
    },
    methods: {
        getList1() {
            let pams = {
                groupType: 1,
            }
            fetchTree(pams).then(data => {
                //  console.log(data)
                let dat = data;
                dat.forEach((item) => {
                    item.id = item.id + "";
                })
                this.rolelist = dat
                // this.treeData = data;
            });
        },
        getList() {
            this.listLoading = true;
            let pams = this.listQuery;
            pams['user_id'] = this.userId;
            page(this.listQuery)
                .then(response => {
                    this.list = response.data.rows;
                    this.total = response.data.total;
                    this.listLoading = false;
                })
        },
        handleFilter() {
            this.listQuery.limit = this.pageSize;
            this.listQuery.page = 1;
            this.getList();
        },
        btnresetTemp() { //重置
            this.listQuery.limit = this.pageSize;
            this.listQuery.page = 1;
            this.listQuery.username = null;
            this.listQuery.name = null;
            this.listQuery.mobile_phone = null;
            this.listQuery.role_name = null;
            this.listQuery.user_type = null;
            this.listQuery.user_sfz = null;
            this.listQuery.area = null;
            this.listQuery.cityArea = null;
            this.countydata1 = [];
            this.getList();

        },
        handleSizeChange(val) {
            this.listQuery.limit = val;
            this.getList();
        },
        handleCurrentChange(val) {
            this.listQuery.page = val;
            this.getList();
        },
        handleCreate() {
            let that = this;

            that.dialogStatus = 'create';
            that.dialogFormVisible = true;
            that.resetTemp();

            //查询地市
            that.getpostcounuty();

            //查询部门
            /*that.getDepartList();*/
        },
        handleUpdate(row) { //修改
            // debugger;
            let that = this;

            let obj = {
                userId: row.user_id
            };
            // console.log(row)

            getUserInfo(obj).then(response => {
                that.form = {
                    id: response.id,
                    userid: response.user_id,
                    username: response.username,
                    name: response.name,
                    sex: response.sex,
                    //password: response.password,
                    description: response.description,
                    email: response.email,
                    mobilePhone: response.mobile_phone,
                    areaCode: [],
                    buCode: [], //部门
                    user_type: row.user_type,
                    'user_sfz': row.user_sfz,
                    'role_id': row.role_id ? row.role_id.split(",") : [],
                    cityCode: response.city_code ? response.city_code.split(",") : []
                };
                // console.log(that.form)
                //that.form.cityCode = ['330100'];//response.city_code ? response.city_code : '330100';

                this.dialogFormVisible = true;

                this.dialogStatus = 'update';
                that.getpostcounuty(2, response.area_code);

                that.getDepartList(response.depart_no);
            });
        },
        handleDelete(row) {
            this.$confirm('此操作将永久删除, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                let pams = {
                    'create_by': this.userId,
                    'id': row.id
                };
                delObj(pams).then(res => {

                    if (res.flag == '1') {
                        this.$notify({
                            title: '成功',
                            message: '删除成功',
                            type: 'success',
                            duration: 2000
                        });
                        const index = this.list.indexOf(row);
                        this.list.splice(index, 1);
                    } else {
                        this.$notify({
                            title: '警告',
                            message: res.msg,
                            type: 'warning',
                            duration: 2000
                        });
                    }

                });
            }).catch(() => {

            });
        },
        create(formName) {
            let that = this;
            const set = this.$refs;
            set[formName].validate(valid => {
                if (valid) {

                    /*let departNo = that.form.buCode[that.form.buCode.length - 1];*/
                    /*that.form.departNo = departNo;*/
                    // console.log(this.form);
                    /*if (that.form.user_type == '01') {
                        if (this.form.user_sfz) {
                            let sfzflg = that.flgshenfen(this.form.user_sfz);
                            if (!sfzflg.pass) {
                                this.$message({
                                    message: sfzflg.tip,
                                    type: 'warning',
                                    duration: 2000
                                });
                                return;
                            }

                        }

                    } else {
                        if (this.form.user_sfz) {
                            let sfzflg = that.flgshenfen(this.form.user_sfz);
                            if (!sfzflg.pass) {
                                this.$message({
                                    message: sfzflg.tip,
                                    type: 'warning',
                                    duration: 2000
                                });
                                return;

                            }

                        } else {

                            this.$message({
                                message: '请输入身份证号',
                                type: 'warning',
                                duration: 2000
                            });
                            return;
                        }
                    }*/
                    // return;
                    let formobj = this.form;
                    this.form.role_id = this.form.role_id ? this.form.role_id.join(',') : '';
                    this.form.cityCode = this.form.cityCode ? this.form.cityCode.join(',') : '';
                    formobj.areaCode = this.form.areaCode ? this.form.areaCode.join(',') : '';
                    // console.log(this.form)
                    addObj(formobj).then(res => {
                        if (res.flag == '1') {
                            this.dialogFormVisible = false;
                            this.getList();
                            this.$notify({
                                title: '成功',
                                message: '创建成功',
                                type: 'success',
                                duration: 2000
                            });

                            this.resetTemp();
                        } else {
                            this.$notify({
                                title: '警告',
                                message: res.msg,
                                type: 'warning',
                                duration: 2000
                            });
                        }
                    })
                } else {
                    return false;
                }
            });
        },
        cancel(formName) {

            this.$refs[formName].resetFields();
            this.dialogFormVisible = false;
            this.resetTemp();
        },
        update(formName) {
            const set = this.$refs;
            let that = this;
            set[formName].validate(valid => {
                if (valid) {
                    /*if (that.form.user_type == '01') {
                        if (this.form.user_sfz) {
                            let sfzflg = that.flgshenfen(this.form.user_sfz);
                            if (!sfzflg.pass) {
                                this.$message({
                                    message: sfzflg.tip,
                                    type: 'warning',
                                    duration: 2000
                                });
                                return;
                            }

                        }

                    } else {
                        if (this.form.user_sfz) {
                            let sfzflg = that.flgshenfen(this.form.user_sfz);
                            // console.log(sfzflg)
                            if (!sfzflg.pass) {
                                this.$message({
                                    message: sfzflg.tip,
                                    type: 'warning',
                                    duration: 2000
                                });
                                return;

                            }

                        } else {

                            this.$message({
                                message: '请输入身份证号',
                                type: 'warning',
                                duration: 2000
                            });
                            return;
                        }
                    }*/
                    this.dialogFormVisible = false;
                   // this.form.password = undefined;
                    this.form.updUserId = localStorage.getItem('dlUserId');
                    let formdepart = this.form.buCode;
                    let arrflg = Array.isArray(formdepart);
                    let departNo = arrflg ? this.form.buCode[this.form.buCode.length - 1] : this.form.buCode;
                    this.form.departNo = departNo;
                    this.form.role_id = this.form.role_id ? this.form.role_id.join(',') : '';
                    this.form.cityCode = this.form.cityCode ? this.form.cityCode.join(',') : '';
                    this.form.areaCode = this.form.areaCode ? this.form.areaCode.join(',') : '';
                    updateUser(this.form).then(res => {
                        if (res.flag == '1') {
                            this.dialogFormVisible = false;
                            this.getList();
                            this.$notify({
                                title: '成功',
                                message: '修改信息成功！',
                                type: 'success',
                                duration: 2000
                            });

                            that.resetTemp();
                        } else {
                            this.$notify({
                                title: '失败',
                                message: '修改信息失败...',
                                type: 'error',
                                duration: 2000
                            });
                        }
                    });
                } else {
                    return false;
                }
            });
        },
        resetTemp() {
            let that = this;

            this.form = {
                username: undefined,
                name: undefined,
                sex: '男',
               // password: undefined,
                description: undefined,
                cityCode: undefined,
                areaCode: undefined,
                email: undefined,
                mobilePhone: undefined,
                buCode: [],
                user_type: null, //归属人员
                user_sfz: null,
                role_id: [], //所属角色
            };
            this.countydata = [];
            this.$nextTick(() => {
                that.$refs['form'].resetFields();
            })
        },
        //获取地市
        getCitiList() {
            let that = this;
            getComCityList().then(data => {
                //  console.log(data);
                // data.unshift({
                //     KEYSS: '80',
                //     VALUE: "陕西省"
                // })
                that.citieCounty = data;
                that.form.cityCode = that.city_id;

                //that.getpostcounuty();

            }).catch(err => { })
        },
        getpostcounuty1(type, areaCode) {
            let that = this;
            let params = {
                dataSqlId: 'WH_SOURCE_1',
                OBJECT_ID: '60000053',
                VALUE: that.listQuery.area,
                staffAuthDistrictId: '80223',
            }
            that.listQuery.cityArea = '';
            that.countydata1 = [];
            getcounuty(params).then(data => {
                that.countydata1 = data;

            }).catch(err => { })

        },
        getpostcounuty(type, areaCode) {
            let areaCodes = this.form.cityCode ? this.form.cityCode.join(',') : '';
            let that = this;
            let params = {
                dataSqlId: 'WH_SOURCE_1',
                OBJECT_ID: '60000053',
                VALUE: areaCodes,
                staffAuthDistrictId: '80223',
            }
            that.form.areaCode = [];
            that.countydata = [];
            //  console.log(params);
            getcounuty(params).then(data => {
                //debugger;
                that.countydata = data;

                if (type == 2 && areaCode) {
                    that.form.areaCode = areaCode.split(',');
                }
            }).catch(err => { })
        },
        handleResetPwd(obj) {
            this.$confirm('将要重置密码,是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            
          
            }).then((va) => {
                // console.log(value)
                let pams = {
                    'username': obj.username, //obj.upd_user,
                }
                //   console.log(pams)
                resetPassword(pams).then(res => {
                    if (res.flag == '1') {
                        this.$message({
                            title: '成功',
                            message: res.msg,
                            type: 'success',
                            duration: 2000
                        });
                    } else {
                        this.$message({
                            title: '失败',
                            message: res.msg,
                            type: 'error',
                            duration: 2000
                        });
                    }
                })
            }).catch(() => {

            });
        },
        handleunbund(obj) {
            let that = this;
            this.$confirm('将要手机IMEI解绑,是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                // console.log(1111)
                let tim = new Date().getTime();// Date.parse(new Date());
                let pams = {
                    // "timestamp":tim,
                    //   "userId":obj.user_id,
                    //   "operationUserId":that.userId,
                    'create_by': that.userId,
                    'user_id': obj.user_id,
                }

                // let  pams1 = JSON.stringify(pams);
                // pams1 = aes.encrypt(pams1);
                // console.log(pams1)
                //  let pams2 ={
                //      objkey:pams1
                //  }
                postFoundedUnbund(pams).then(res => {
                    if (res.flag == '0') {
                        this.$message({
                            message: res.msg,
                            type: 'success',
                            duration: 2000
                        });
                    } else {
                        this.$message({
                            message: res.msg,
                            type: 'error',
                            duration: 2000
                        });
                    }
                })
            }).catch(() => {

            });

        },
        getDepartList(departNo) { //获取部门
            let that = this;
            getDepartAll({}).then(res => {
                that.buMdata = res;

                if (departNo) {
                    that.form.buCode = departNo;
                }
            })
        },
        handlEexport() {//导出
            let that = this;
            //导出
            that.btnloading = true
            let fileName = '用户列表'
            let pams = this.listQuery;
            pams['user_id'] = this.userId;
            pams['fileName'] = fileName;

            downloadfilter(pams, '2021122300').then((dat) => {
                that.btnloading = false
            }).catch((err) => {
                that.btnloading = false
            });

        },
        flgshenfen(code) {
            let city = {
                11: "北京",
                12: "天津",
                13: "河北",
                14: "山西",
                15: "内蒙古",
                21: "辽宁",
                22: "吉林",
                23: "黑龙江 ",
                31: "上海",
                32: "江苏",
                33: "浙江",
                34: "安徽",
                35: "福建",
                36: "江西",
                37: "山东",
                41: "河南",
                42: "湖北 ",
                43: "湖南",
                44: "广东",
                45: "广西",
                46: "海南",
                50: "重庆",
                51: "四川",
                52: "贵州",
                53: "云南",
                54: "西藏 ",
                61: "陕西",
                62: "甘肃",
                63: "青海",
                64: "宁夏",
                65: "新疆",
                71: "台湾",
                81: "香港",
                82: "澳门",
                91: "国外 "
            };
            let tip = ""
            let pass = true

            if (!code || !/^\d{6}(18|19|20)?\d{2}(0[1-9]|1[012])(0[1-9]|[12]\d|3[01])\d{3}(\d|X)$/i.test(code)) {
                tip = "身份证号格式错误"
                pass = false;
            } else if (!city[code.substr(0, 2)]) {
                tip = "地址编码错误"
                pass = false
            } else {
                // 18位身份证需要验证最后一位校验位
                if (code.length === 18) {
                    code = code.split('')
                    // ∑(ai×Wi)(mod 11)
                    // 加权因子
                    var factor = [7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2]
                    // 校验位
                    var parity = ['1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2']
                    var sum = 0
                    var ai = 0
                    var wi = 0
                    for (var i = 0; i < 17; i++) {
                        ai = code[i]
                        wi = factor[i]
                        sum += ai * wi
                    }
                    var last = parity[sum % 11];
                    //console.log(last)
                    if (parity[sum % 11] != code[17]) {
                        tip = "校验位错误"
                        pass = false
                    }
                }
            }
            return {
                'pass': pass,
                'tip': tip
            };
        }
    }
}
</script>

<style scoped lang="scss"></style>
<style lang="scss">
// 

.usercontainer {
    .userrows {
        .filter-item {
            width: 100%;
            margin-bottom: 0px;
        }
    }

    .formbtn {
        .el-form-item__content {
            margin-left: 0px !important;
        }
    }
}
</style>
