import http from '@/api/http'

export function getProInit(obj) {
  return http({
    url: '/api/watchme/myProjectCon/proInit',
    method: 'post',
    data: obj
  });
}

export function qryCityList(obj) {
  return http({
    url: '/api/watchme/myProjectCon/qryCityList',
    method: 'post',
    data: obj
  });
}

export function qryPmsList(obj) {
  return http({
    url: '/api/watchme/myProjectCon/pmsList',
    method: 'post',
    data: obj
  });
}

export function addProRela(obj) {
  return http({
    url: '/api/watchme/myProjectCon/addProRela',
    method: 'post',
    data: obj
  });
}

export function addZwReal(obj) {
  return http({
    url: '/api/watchme/requireKhgcCon/addZwReal',
    method: 'post',
    data: obj
  });
}

export function initZw(obj) {
  return http({
    url: '/api/watchme/requireKhgcCon/initZw',
    method: 'post',
    data: obj
  });
}

export function delZw(obj) {
  return http({
    url: '/api/watchme/requireKhgcCon/delZw',
    method: 'post',
    data: obj
  });
}

export function qryProRela(obj) {
  return http({
    url: '/api/watchme/myProjectCon/qryProRela',
    method: 'post',
    data: obj
  });
}
