<template>
    <div>
      <template v-for="item in menuList">
          <el-submenu v-if="item.children.length" :index="`${item.id}`" :key="item.id">
              <template slot="title">{{ item.title }}</template>
              <menutree :menuList="item.children"></menutree> 
           </el-submenu>
          <el-menu-item v-else :key="item.id" :index="item.href">{{ item.title }}</el-menu-item>
      </template>
    </div>
  </template>
  <script>
  export default {
    name: "menutree",
    props: {
      menuList: [Object, Array]
    }
  }
  </script>