import http from '@/api/http'

export function page(query) {
  return http({
    url: '/api/form/form/page',
    method: 'get',
    params: query
  });
}

export function addObj(obj) {
  return http({
    url: '/api/form/form/add',
    method: 'post',
    data: obj
  });
}

export function delObj(obj) {
  return http({
    url: '/api/form/form/del',
    method: 'post',
    data: obj
  });
}

export function getObj(obj) {
  return http({
    url: '/api/form/form/get',
    method: 'post',
    data: obj
  });
}

export function putObj(obj) {
  return http({
    url: '/api/form/form/update',
    method: 'post',
    data: obj
  });
}

export function createObj(obj) {
  return http({
    url: '/api/form/form/create',
    method: 'post',
    data: obj
  });
}

export function alertObj(obj) {
  return http({
    url: '/api/form/form/alert',
    method: 'post',
    data: obj
  });
}

export function dropObj(obj) {
  return http({
    url: '/api/form/form/drop',
    method: 'post',
    data: obj
  });
}
