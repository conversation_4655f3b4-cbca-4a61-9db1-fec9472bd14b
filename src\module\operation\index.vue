<template>
  <el-container style="height: 100%;">

    <el-container>

      <Transition name="bounce">
        <div v-show="treeIsShow" v-drag class="el-aside" style="">
          <div style="padding: 24px 12px; display: flex;flex-direction: column;">
            <el-autocomplete v-model="sitename" :popper-append-to-body="false" value-key="site_name"
              @select="handleSiteNameSelect" :fetch-suggestions="querySearchAsync" placeholder="请输入名称搜索" size="small"
              prefix-icon="el-icon-search" class="myinput"
              style="margin-bottom: 18px;border-color: #313C57;max-width: 300px;">
            </el-autocomplete>
            <div class="tree-wrap">
              <!-- <el-tree class="tree-box" style="height: auto;font-size: 14px;" @node-click="menuNodeClick"
                            :data="menulist" :props="menusProps"></el-tree> -->
              <!-- ['area', 'site', 'room', 'fsu', 'device'] -->
              <el-tree @node-click="handleNodeClick" class="tree-box" :highlight-current="true" node-key="id"
                :props="defaultProps" lazy :load="loadNode" ref="tree1">

                <div class="custom-tree-node" slot-scope="{ node, data }"
                     @click="handleTreeNodeClick(node)"
                     :class="{ 'disabled-node': isDeviceDisabled(node) }">
                  <div class="icon-container">
                    <img :src="cityicon" v-if="node.data.type == 'city'" alt="" class="tree-icon">
                    <el-badge v-if="node.data.alarmState == 'true' && node.data.type == 'city'" class="error-indicator">
                      <img :src="alarmIcon" alt="">
                    </el-badge>
                    <el-badge v-if="node.data.fsuState == 'true' && node.data.type == 'city'" class="error-indicator">
                      <img :src="deviceIcon" alt="">
                    </el-badge>
                  </div>
                  <div class="icon-container">
                    <img :src="cityicon" v-if="node.data.type == 'area'" alt="" class="tree-icon">
                    <el-badge v-if="node.data.alarmState == 'true' && node.data.type == 'area'" class="error-indicator">
                      <img :src="alarmIcon" alt="">
                    </el-badge>
                    <el-badge v-if="node.data.fsuState == 'true' && node.data.type == 'area'" class="error-indicator">
                      <img :src="deviceIcon" alt="">
                    </el-badge>
                  </div>
                  <div class="icon-container">
                    <img :src="siteicon" v-if="node.data.type == 'site'" alt="" class="tree-icon">
                    <el-badge v-if="node.data.alarmState == 'true' && node.data.type == 'site'" class="error-indicator">
                      <img :src="alarmIcon" alt="">
                    </el-badge>
                    <el-badge v-if="node.data.fsuState == 'true' && node.data.type == 'site'" class="error-indicator">
                      <img :src="deviceIcon" alt="">
                    </el-badge>
                  </div>
                  <div class="icon-container">
                    <img :src="roomicon" v-if="node.data.type == 'room'" alt="" class="tree-icon">
                    <el-badge v-if="node.data.alarmState == 'true' && node.data.type == 'room'" class="error-indicator">
                      <img :src="alarmIcon" alt="">
                    </el-badge>
                    <el-badge v-if="node.data.fsuState == 'true' && node.data.type == 'room'" class="error-indicator">
                      <img :src="deviceIcon" alt="">
                    </el-badge>
                  </div>
                  <div class="icon-container">
                    <img :src="fsuicon" v-if="node.data.type == 'fsu'" alt="" class="tree-icon"
                         :class="{ 'disabled-device': isDeviceDisabled(node) }">
                    <el-badge v-if="node.data.alarmState == 'true' && node.data.type == 'fsu'" class="error-indicator">
                      <img :src="alarmIcon" alt="">
                    </el-badge>
                    <el-badge v-if="node.data.fsuState == 'true' && node.data.type == 'fsu'" class="error-indicator">
                      <img :src="deviceIcon" alt="">
                    </el-badge>
                  </div>
                  <div class="icon-container">
                    <img :src="require(`./views/img/${'物联网设备.svg' || data.icon}`)" v-if="node.data.type == 'device'"
                      alt="" class="tree-icon" :class="{ 'disabled-device': isDeviceDisabled(node) }">
                    <el-badge v-if="node.data.alarmState == 'true' && node.data.type == 'device'"
                      class="error-indicator">
                      <img :src="alarmIcon" alt="">
                    </el-badge>
                    <el-badge v-if="isDeviceDisabled(node) && node.data.type == 'device'" class="error-indicator">
                      <img :src="deviceIcon" alt="">
                    </el-badge>
                  </div>

                  <!-- <span v-if="node.level == 6">{{ data.icon }}</span> -->
                  <span> {{ node.label }}</span>

                </div>
              </el-tree>
            </div>
          </div>
        </div>
      </Transition>
      <el-main>
        <div class="wrap">
          <div class="collapse_tree" @click="treeIsShow = !treeIsShow">
            <i v-show="treeIsShow" class="el-icon-d-arrow-left"></i>
            <i v-show="!treeIsShow" class="el-icon-d-arrow-right"></i>
          </div>
          <router-view></router-view>
        </div>
      </el-main>
    </el-container>
  </el-container>
</template>

<script>
import deviceIcon from './views/deviceIcon.js'
import { mapGetters } from 'vuex';
export default {
  data() {
    return {
      treeIsShow: true,
      userInfo: '',
      openFlag: false,
      menulist: [],
      menusProps: {
        children: 'children',
        label: 'title',
      },
      deviceForm: {
        level: '', //city：市；area：区；site：站点；room：机房；su：su；device：设备）
        cityId: '',
        cityName: '',
        areaId: '',
        areaName: '',
        siteId: '',
        siteName: '',
        roomId: '',
        roomName: '',
        suId: '',
        suName: '',
        deviceId: '',
        deviceName: '',
      },
      filterText: '',
      treedata: [],
      defaultProps: {
        children: 'children',
        label: 'label',
        isLeaf: 'isLeaf'
      },
      parentIds: [],
      sitename: '',
      cityicon: require('./views/img/地市区县.svg'),
      siteicon: require('./views/img/局站.svg'),
      roomicon: require('./views/img/机房.svg'),
      fsuicon: require('./views/img/路由器.svg'),
      alarmIcon: require('./views/img/告警.svg'),
      deviceIcon: require('./views/img/告警1.svg'),
      roleType: [
        { value: 1, leave: 'city', }, // 超级管理 和 省级
        { value: 94, leave: 'city', }, // 地市
        { value: 95, leave: 'area', }, // 一般
      ],
      levelList: [],
      rolelevel: 'city',
      allParent: [],

      cityId: '',
      areaId: '',
      siteId: '',
      roomId: '',
    };
  },
  watch: {

  },
  directives: {
    drag: {
      inserted: function (el) {
        const dragDom = el;
        dragDom.style.cursor = "col-resize";
        dragDom.onmousedown = (e) => {
          // 鼠标按下，计算当前元素距离可视区的距离
          const disX = e.clientX;
          const w = dragDom.clientWidth;
          const minW = 300;
          const maxW = 500;
          var nw;
          document.onmousemove = function (e) {
            // 通过事件委托，计算移动的距离
            const l = e.clientX - disX;
            // 改变当前元素宽度，不可超过最小最大值
            nw = w + l;
            nw = nw < minW ? minW : nw;
            nw = nw > maxW ? maxW : nw;
            dragDom.style.width = `${nw}px`;
          };

          document.onmouseup = function (e) {
            document.onmousemove = null;
            document.onmouseup = null;
          };
        };
      },
    },
  },
  computed: {
    ...mapGetters([
      'name'
    ])
  },
  created() {
    this.menulist = JSON.parse(sessionStorage.getItem('permissionMenus')) || []


  },
  methods: {
    // 检查节点是否应该被禁用
    isDeviceDisabled(node) {
      // 如果是设备节点，检查父FSU的fsuState
      if (node.data.type === 'device') {
        if (node.parent && node.parent.data.type === 'fsu') {
          return node.parent.data.fsuState === 'true';
        }
      }
      // 如果是FSU节点，检查自身的fsuState
      if (node.data.type === 'fsu') {
        return node.data.fsuState === 'true';
      }
      return false;
    },

    // 处理树节点点击事件
    handleTreeNodeClick(node) {
      // 如果是设备节点且被禁用，则不执行任何操作
      if (this.isDeviceDisabled(node)) {
        return;
      }
      // 否则调用原来的toPath方法
      this.toPath(node);
    },

    menuNodeClick(node) {
      if (node?.href && node.type == 'menu') {
        this.$router.push({
          path: node.href
        })
      }
    },
    getParentName(node) {
      if (!node.data) return
      // console.log(node.data.label)
      this.allParent.push(node.data.label)
      this.getParentName(node.parent)
    },
    toPath(node) {
      console.log(node);

      const uniqueDataObjects = this.getAllDataObjects(node);
      let roleId = sessionStorage.getItem('role')
      if (node.data.type == 'device') {
        // 检查设备是否被禁用（父FSU的fsuState为true）
        if (this.isDeviceDisabled(node)) {
          return; // 如果设备被禁用，直接返回，不执行跳转
        }
        let devId = node.data.id
        let suId = node.parent.data.id
        this.$router.push({
          path: `/operation/deviceDetail/${suId}/${devId}`
        })
      } else if (node.data.type == 'fsu' && ['1', '94'].includes(roleId)) {
        // 检查FSU是否被禁用（fsuState为true）
        if (this.isDeviceDisabled(node)) {
          return; // 如果FSU被禁用，直接返回，不执行跳转
        }
        let fsuId = node.data.id
        this.getParentName(node.parent)
        let fsumsg = this.allParent.reverse().join('/')
        this.$router.push({
          path: `/operation/fsu/${fsuId}?fsumsg=${fsumsg}`
        })
      }
      else {
        this.cityId = '';
        this.areaId = '';
        this.siteId = '';
        this.roomId = '';
        // console.log(uniqueDataObjects);
        uniqueDataObjects.map(item => {
          if (item) {
            if (item.type == 'city') {
              this.cityId = item.id
            }
            if (item.type == 'area') {
              this.areaId = item.id
            }
            if (item.type == 'site') {

              this.siteId = item.id
            }
            if (item.type == 'room') {
              this.roomId = item.id
            }
          }

        })


        let id = node.data.id
        let type = node.data.type
        this.$router.push({
          path: `/operation/home?type=${type}&cityId=${this.cityId}&areaId=${this.areaId}&siteId=${this.siteId}&roomId=${this.roomId}`
        })
      }


    },
    updateIds(item) {
      const types = ['city', 'area', 'site', 'room'];

      // 重置所有 ID
      types.forEach(type => {
        this[`${type}Id`] = '';
      });

      // 根据 item.type 更新相应的 ID
      if (types.includes(item.type)) {
        this[`${item.type}Id`] = item.id;
      }
    },
    getAllDataObjects(node) {
      // 如果节点为空，返回空数组
      if (!node) return [];

      // 将当前节点的 data 加入结果数组
      let dataObjects = [node.data];

      // 如果有父节点，递归获取父节点的 data
      if (node.parent) {
        dataObjects = dataObjects.concat(this.getAllDataObjects(node.parent));
      }

      return dataObjects;
    },
    async querySearchAsync(queryString, cb) {
      if (!queryString) return
      let res = await this.$api.siteInfo({ siteName: queryString })
      if (res.code == 200) {
        cb(res.data)
      } else {
        this.$message.error(res.msg)
      }
    },
    handleSiteNameSelect(item) {
      // console.log(item);
      this.$refs['tree1'].setCurrentKey([])

      if (this.rolelevel == 'city') {
        this.$nextTick(async () => {
          let cityNode = await this.$refs['tree1'].getNode(item.cityId)
          cityNode.expanded = true
          await cityNode.loadData()
          setTimeout(async () => {
            let areaNode = await this.$refs['tree1'].getNode(item.areaId)
            areaNode.expanded = true
            await areaNode.loadData()
            setTimeout(async () => {
              let siteNode = await this.$refs['tree1'].getNode(item.site_id)
              siteNode.expanded = true
              await siteNode.loadData()
              this.$refs['tree1'].setCurrentKey(item.site_id);
              setTimeout(() => {
                const dom1 = document.getElementsByClassName("el-tree-node is-expanded is-current is-focusable")[0];
                dom1.scrollIntoView({ block: 'center', behavior: 'smooth' })
              }, 300)
            }, 200)
          }, 100)
        })
      }

      if (this.rolelevel == 'area') {
        this.$nextTick(async () => {
          let areaNode = await this.$refs['tree1'].getNode(item.areaId)
          areaNode.expanded = true
          await areaNode.loadData()
          setTimeout(async () => {
            let siteNode = await this.$refs['tree1'].getNode(item.site_id)
            siteNode.expanded = true
            await siteNode.loadData()
            this.$refs['tree1'].setCurrentKey(item.site_id);
            setTimeout(() => {
              const dom1 = document.getElementsByClassName("el-tree-node is-expanded is-current is-focusable")[0];
              dom1.scrollIntoView({ block: 'center', behavior: 'smooth' })
            }, 300)
          }, 200)
        })
      }
      this.cityId = item.cityId;
      this.areaId = item.areaId;
      this.siteId = item.site_id;
      this.$router.push({
        path: `/operation/home?type=site&cityId=${this.cityId}&areaId=${this.areaId}&siteId=${this.siteId}&roomId=${''}`
      })

    },

    async loadNode(node, resolve) {
      if (node.level === 0) {
        let role = sessionStorage.getItem('role').split(',')
        let list = ['1', '94', '95']
        let a = list.find(v => role.includes(v))
        if (!a) return this.$message.error('无数据查看权限')
        this.rolelevel = this.roleType.find(v => v.value == a)?.leave || ''
        if (this.rolelevel == 'city') {
          this.levelList = ['area', 'site', 'room', 'fsu', 'device']
        }
        if (this.rolelevel == 'area') {
          this.levelList = ['site', 'room', 'fsu', 'device']
        }
        let data = await this.monitorDevice('', this.rolelevel)
        return resolve(data)
      }
      if (node.level >= 1) {
        this.parentIds = []
        this.getAllParentNodes(node)
        let id = node.data.id
        let data = await this.monitorDevice(id, '')
        return resolve(data)
      }
    },
    handleNodeClick(data, node, self) {

    },
    getAllParentNodes(node, data, self) {
      this.parentIds.unshift(node.data.id)
      if (node?.parent?.data) {
        this.getAllParentNodes(node.parent) // 递归
      }
      // console.log(this.parentIds)
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },



    // 资源结构化查询
    async monitorDevice(id, lel) {
      // let level = ['area', 'site', 'room', 'fsu', 'device']
      //console.log('this.parentIds', level, this.parentIds, level[this.parentIds.length - 1])
      let params = {
        level: id ? this.levelList[this.parentIds.length - 1] : lel, //city：市；area：区；site：站点；room：机房；su：su；device：设备）
        pid: id || ''
      }
      let res = await this.$api.monitorDevice(params)
      if (res.code == 200) {
        let data = res.data.map(v => {
          // console.log(deviceIcon.find(item => item.type == v.type)?.icon)
          if (params.level == 'device') {
            v.isLeaf = true  // 设备为最后一级节点 不展示箭头
            v.icon = deviceIcon.find(item => item.type == v.type)?.icon || '物联网设备.svg'
          }
          return v
        })
        return data
      } else {
        this.$message.error(res.msg)
      }

    },
  },


};
</script>

<style lang="scss" scoped>
.bounce-enter-active {
  animation: bounce-in 0.3s cubic-bezier(0.450, 0.140, 0.060, 0.900);
}

.bounce-leave-active {
  animation: bounce-in 0.3s cubic-bezier(0.450, 0.140, 0.060, 0.900) reverse;
}

@keyframes bounce-in {
  from {
    width: 0;
  }

  to {
    width: 300px;
  }
}

@keyframes collFalse {
  from {
    width: 300px;
  }

  to {
    width: 0;
  }
}

@keyframes collTrue {
  from {
    width: 0;
  }

  to {
    width: 300px;
  }
}

.collapseTreClose {
  animation: collFalse 0.5s ease-in-out;
}

.collapseTreOpen {
  animation: collTrue 0.5s ease-in-out;
}

.el-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: flex-end;

}

.top-right {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.custom-tree-node {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.tree-icon {
  width: 1.25rem;
  height: 1.25rem;
  margin-right: 4px;
}

.el-aside {
  width: 300px;
  background-color: #272845;
  text-align: center;
  box-sizing: border-box;
  overflow: hidden;
}

.tree-wrap {
  height: calc(100vh - 180px);
  overflow: scroll;

}

:deep(.el-tree) {
  margin-top: 5px;
}

.el-main {
  width: 0;
  background-color: #E9EEF3;
  padding: 12px 20px;
  box-sizing: border-box;

  .wrap {
    background: #fff;
    padding: 18px;
    width: 100%;
    min-width: fit-content;
    min-height: 100%;
    border-radius: 4px;
    position: relative;

    .collapse_tree {
      position: absolute;
      top: 50%;
      left: -20px;
      width: 16px;
      height: 30px;
      border-radius: 0 4px 4px 0;
      background-color: #272845;
      transform: translateY(-15px);
      color: #fff;
      line-height: 30px;
      text-align: center;
    }
  }
}

.error-indicator {
  font-size: 10px;
}

.icon-container {
  position: relative;
}

.disabled-device {
  filter: grayscale(100%);
  opacity: 0.5;
  cursor: not-allowed !important;
}

.disabled-node {
  cursor: not-allowed !important;
  pointer-events: none;
}

.disabled-node .tree-icon {
  filter: grayscale(100%);
  opacity: 0.5;
}
</style>