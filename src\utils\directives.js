import Vue from 'vue'
import moment from 'moment'
/**
 * 自定义格式化显示时间指令
 * 将接口返回的时间20211030224848000
 * 转换成2021-10-30 22:48:48
 * 可传入时间格式动态指令参数
 * 不传默认YYYY-MM-DD HH:mm:ss
 */
Vue.directive('time',
  function (el, binding) {
    if (!binding.value) {
      el.innerHTML = ''
      return
    }
    const customFormat = binding.arg || 'YYYY-MM-DD HH:mm:ss'
    el.innerHTML = moment(binding.value, 'YYYYMMDDHHmmss').format(customFormat)
  }
)
