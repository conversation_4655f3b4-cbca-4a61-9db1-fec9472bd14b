<template>
  <div>
    <el-input v-model="userId" style="display: none;"></el-input>
    <el-form :model="form" :rules="rules">
      <el-form-item label="姓名" prop="username" :class="clsUser">
        <el-input v-model="form.username" :disabled="disabled" ></el-input>
      </el-form-item>
      <el-form-item label="用户类型" prop="user_type" :class="clsUser">
        <el-input v-model="form.user_type" :disabled="disabled" ></el-input>
      </el-form-item>
      <el-form-item label="地市" prop="area_name" :class="clsUser">
        <el-input v-model="form.area_name" :disabled="disabled"></el-input>
      </el-form-item>
      <el-form-item label="区县" prop="city_name" :class="clsUser">
        <el-input v-model="form.city_name" :disabled="disabled"></el-input>
      </el-form-item>
      <el-form-item label="部门" prop="depart_name" :class="clsUser">
        <el-input v-model="form.depart_name" :disabled="disabled"></el-input>
      </el-form-item>
      <el-form-item label="电话" prop="mobile_phone" :class="clsUser">
        <el-input v-model="form.mobile_phone" :disabled="disabled"></el-input>
      </el-form-item>
      <el-form-item label="身份证" prop="user_sfz" :class="clsUser">
        <el-input v-model="form.user_sfz" :disabled="disabled"></el-input>
      </el-form-item>
      <el-form-item label="邮箱" prop="email" :class="clsUser">
        <el-input v-model="form.email" :disabled="disabled"></el-input>
      </el-form-item>

    </el-form>

  </div>

</template>

<script>

    export default {
        name: "persional",
      data() {
          return {
            disabled: true,
            userId:localStorage.getItem('sysUserId'),
            clsUser: 'clsUsers',
            form: {
              userId:undefined,
              username:undefined,
              user_type:undefined,
              area_name:undefined,
              city_name:undefined,
              mobile_phone:undefined,
              user_sfz:undefined,
              email:undefined

            },
            rules:{

            }
          }
      },
      created(){
          this.$http({
            url: '/api/admin/user/queryUserDetails' ,
            method: 'post',
            data:{'userId':this.userId}
          }).then(res => {
            console.log(res)
            this.form = res
          })
      },
      methods: {

      }
    }
</script>

<style scoped>
.clsUsers {
  padding-left: 3px;
  width: 30%;
  float: left;
  border: 0px;

}
</style>
