const routes = [
  {
    path: "/",
    // 二级目录为/home，自服务为/newhome，可在打包时自行修改
    // redirect: process.env.NODE_ENV === 'production' ? '/home' : '/newhome'
    redirect: "/login",
  },
  {
    path: "/screen",
    name: "screen",
    component: () => import("@/module/screen.vue"),
  },
  {
    path: "/login",
    name: "Login",
    component: () => import("@/module/loginContainer"),
  },
  {
    path: "/changeThePassword",
    name: "changeThePassword",
    component: () => import("@/module/changePassword"),
    
  },
  {
    path: "/home",
    name: "Home",
    component: () => import("@/module/Container.vue"),
    children: [
      // {
      //   path: '',
      //   redirect: "/operation"
      // },
      {
        path: "/operation",
        // name: "operation",
        component: () => import("@/module/operation/index.vue"),
        children: [
          {
            path: '',
            //component: () => import("@/module/operation/views/home.vue"),
           redirect: "/operation/home"
          },
          {
            path: '/operation/home',
            component: () => import("@/module/operation/views/components/homeWarnDetail.vue"),
            
          },

          {
            path: '/operation/deviceDetail/:suId/:devId',
            component: () => import("@/module/operation/views/deviceDetail.vue"),
            props: true,
          },
          {
            path: '/operation/fsu/:fsuId',
            component: () => import("@/module/operation/views/fsu.vue"),
            props: true,
          },
        ]
      },
      // 设备运行一览表 
      {
        path: '/operation/deviceRunningData',
        component: () => import("@/module/operation/views/deviceRunningData.vue"),
        meta: {
          title: ['报表统计', '设备运行一览表']
        }
      },
      // 设备统计
      {
        path: '/operation/deviceStatistics',
        component: () => import("@/module/operation/views/deviceStatistics.vue"),
        meta: {
          title: ['报表统计', '设备统计']
        }
      },
      // 告警统计
      {
        path: '/operation/warningStatistics',
        component: () => import("@/module/operation/views/warningStatistics.vue"),
        meta: {
          title: ['报表统计', '告警统计']
        }
      },
      // 性能统计
      {
        path: '/operation/performanceStatistics',
        component: () => import("@/module/operation/views/performanceStatistics.vue"),
        meta: {
          title: ['报表统计', '性能统计']
        }
      },
      // table
      {
        path: '/operation/table',
        component: () => import("@/module/operation/views/table.vue"),
        meta: {
          title: ['报表统计', '表格']
        }
      },
      // 批量阈值配置
      {
        path: '/operation/batchThresholdConfig',
        component: () => import("@/module/operation/views/batchThresholdConfig.vue"),
        meta: {
          title: ['管理配置', '批量设备阈值配置']
        }
      },
      // 批量告警配置
      {
        path: '/operation/batchWarningConfig',
        component: () => import("@/module/operation/views/batchWarningConfig.vue"),
        meta: {
          title: ['管理配置', '批量设备告警配置']
        }
      },
      // 中资资源核查 resourceChecked
      {
        path: '/operation/resourceChecked',
        component: () => import("@/module/operation/views/resourceChecked.vue"),
        meta: {
          title: ['资源核查', '中资资源核查']
        }
      },
      // 未注册资资源核查 resourceChecked
      {
        path: '/operation/noRegisterDevice',
        component: () => import("@/module/operation/views/noRegisterDevice.vue"),
        meta: {
          title: ['资源核查', '未注册资源核查']
        }
      },
      // FSU资源核查（疑似退网）
      {
        path: '/operation/FSUResource',
        component: () => import("@/module/operation/views/FSUResource.vue"),
        meta: {
          title: ['资源核查', 'FSU疑似退网']
        }
      },
      // 设备资源核查（疑似退网）
      {
        path: '/operation/deviceResource',
        component: () => import("@/module/operation/views/deviceResource.vue"),
        meta: {
          title: ['资源核查', '设备疑似退网']
        }
      },
      {
        path: '/operation/approvalProcess',
        component: () => import("@/module/operation/views/approvalProcess.vue"),
        meta: {
          title: ['系统管理', '审批管理', '审批流程配置']
        }
      },
      {
        path: '/operation/warkingOrderInfo',
        component: () => import("@/module/operation/views/warkingOrderInfo.vue"),
        props: true,
        meta: {
          title: ['工单管理', '待办工单']
        }
      },
      {
        path: '/operation/warkedOrderInfo',
        component: () => import("@/module/operation/views/warkedOrderInfo.vue"),
        props: true,
        meta: {
          title: ['工单管理', '已办工单']
        }
      },
      {
        path: '/baseManager/userManager',
        component: () => import("@/module/admin/user/index.vue"),
        meta: {
          title: ['系统管理', '基础配置', '用户管理']
        },
        props: true
      },
      { ///baseManager/menuManager 菜单管理
        path: '/baseManager/menuManager',
        component: () => import("@/module/admin/menu/index.vue"),
        props: true,
        meta: {
          title: ['系统管理', '基础配置', '菜单管理']
        },
      },
      { //  角色管理
        path: '/baseManager/groupManager',
        component: () => import("@/module/admin/group/index.vue"),
        props: true,
        meta: {
          title: ['系统管理', '基础配置', '角色管理']
        },
      },
      { //  角色类型管理
        path: '/baseManager/groupTypeManager',
        component: () => import("@/module/admin/groupType/index.vue"),
        meta: {
          title: ['系统管理', '基础配置', '角色类型管理']
        },
        props: true
      },
      { //  登陆记录
        path: '/baseManager/loginRecord',
        component: () => import("@/module/admin/loginRecord/index.vue"),
        meta: {
          title: ['系统管理', '基础配置', '登陆记录']
        },
        props: true
      },
      { //  区域管理
        path: '/baseManager/regionalManag',
        component: () => import("@/module/admin/regional/index.vue"),
        props: true,
        meta: {
          title: ['系统管理', '基础配置', '区域管理']
        },
      },
      { //  公告管理
        path: '/baseManager/noticeIssued',
        component: () => import("@/module/admin/noticeIssued/index.vue"),
        props: true,
        meta: {
          title: ['系统管理', '基础配置', '公告管理']
        },
      },
      { //  问题反馈
        path: '/baseManager/problemOFeedback',
        component: () => import("@/module/admin/account/problemOFeedback"),
        props: true,
        meta: {
          title: ['系统管理', '基础配置', '问题反馈']
        },
      },
    ]
  },


  {
    path: "*",
    component: () => import("@/module/404.vue"),
  },
];

export default routes;
