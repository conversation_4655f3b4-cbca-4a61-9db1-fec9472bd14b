import http from '@/api/http'

export function page(query) {
  return http({
    url: '/api/admin/element/list',
    method: 'get',
    params: query
  });
}

export function addObj(obj) {
  return http({
    url: '/api/admin/element',
    method: 'post',
    data: obj
  });
}

export function getObj(id) {
  return http({
    url: '/api/admin/element/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return http({
    url: '/api/admin/element/' + id,
    method: 'delete'
  })
}

export function putObj(id, obj) {
  return http({
    url: '/api/admin/element/' + id,
    method: 'put',
    data: obj
  })
}
