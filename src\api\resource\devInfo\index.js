import http from '@/api/http'

export function fetchDevInfo(query) {
  return http({
    url: '/api/resource/resourceController/getResDevInfo',
    method: 'get',
    params: query
  });
}

export function fetchProvince(obj) {
  return http({
    url: '/api/dispose/provinceCity/getProvince',
    method: 'get',
    params: obj
  });
}
export function fetchCity(obj) {
  return http({
    url: '/api/dispose/provinceCity/getCity',
    method: 'get',
    params: obj
  });
}

export function fetchgetVendor(query) {
  return http({
    url: '/api/dispose/accessRingController/getVendorList',
    method: 'get',
    params: query
  });
}

export function fetchaddAccess(query) {
  return http({
    url: '/api/dispose/accessRingController/addAccessRingConf',
    method: 'post',
    data: query
  });
}
//
export function fetchOneAccess(query) {
  return http({
    url: '/api/resource/resourceController/getResDevInfo',
    method: 'get',
    params: query
  });
}

export function fetchLinkInfo(query) {
  return http({
    url: '/api/resource/resourceController/getResDevPortLinkInfo',
    method: 'get',
    params: query
  });
}

export function updPortInfo(query) {
  return http({
    contentType: "application/json;charsetset=UTF-8",//必须
    url: '/api/resource/resourceController/updPortInfo',
    method: 'post',
    data: query
  });
}

export function fetchDevices(query) {
  return http({
    url: '/api/resource/resourceController/getDevicesData',
    method: 'post',
    data: query
  });
}

export function fetchOamInfo(query) {
  return http({
    url: '/api/resource/oam/getOamInfo',
    method: 'post',
    data: query
  });
}

export function updOamInfo(query) {
  return http({
    url: '/api/resource/oam/updOamInfo',
    method: 'post',
    data: query
  });
}

export function delOamInfo(query) {
  return http({
    url: '/api/resource/oam/delOamInfo',
    method: 'post',
    data: query
  });
}

export function addOamInfo(query) {
  return http({
    url: '/api/resource/oam/addOamInfo',
    method: 'post',
    data: query
  });
}