// sort的比较函数
export const compare = (property, flag = 'asc') => {
  return function (a, b) {
    const value1 = a[property]
    const value2 = b[property]
    if (flag === 'asc') {
      return value1 - value2
    } else {
      return value2 - value1
    }
  }
}

// 字符串首字母变为大写
export const upperCaseFirstLetter = str => {
  if (!str) return ''
  return str.slice(0, 1).toUpperCase() + str.slice(1)
}

export const throttle = (fn, wait) => {
  let pre = Date.now()
  return function () {
    const context = this
    const args = arguments
    const now = Date.now()
    if (now - pre >= wait) {
      fn.apply(context, args)
      pre = Date.now()
    }
  }
}