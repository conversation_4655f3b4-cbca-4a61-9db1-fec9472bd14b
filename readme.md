# 5G切片自服务平台

## 安装依赖

```
npm install
```

### 启动项目

```
npm run serve
```

### 编译打包

#### 前缀为/，部署到html根目录
```
npm run build
```
#### 前缀为/newportal/，部署到二级目录
```
npm run build:prod
```

### element-ui 换肤功能使用说明

- 1.安装 elementui 主题工具 `npm i element-theme -g`
- 2.安装 chalk 主题 `npm i element-theme-chalk -D`
- 3.安装 gulp 及相关依赖，`npm install gulp gulp-clean-css gulp-css-wrap`，这个工具可以对 css 样式进行包裹
- 4.在根目录创建一个 gulpfile.js 文件，目前已经创建好，具体修改内容文件中已写明
- 5.初始化主题变量文件，运行命令`et -i`，会在根目录生成一个 element-variables.scss 文件
- 6.在这个 element-variables.scss 文件里可以修改主题颜色，进行主题配置
- 7.编译生成主题文件，运行命令`et` ，会在根目录生成一个 theme 文件夹，里面包含按照主题变量文件配置生成的主题
- 8.修改gulpfile.js文件，将里面主题色色值做对应修改，即修改打包后目录名和selector名
- 9.运行 `gulp css-wrap` 命令，输出包裹后的主题文件夹，文件夹位置在src/assets/css/theme下
- 10.字体文件已经在index.css引入，不需额外引入
- 11.在 src/assets/css/theme/index.scss 中引入生成的文件，并且在对应的主题 vue 文件中，添加新增的主题颜色（或在数据库CONF_PARAM表中SELF_THEME_CODE字段配置颜色）
- 12.添加新的主题重复 6-11 步即可
