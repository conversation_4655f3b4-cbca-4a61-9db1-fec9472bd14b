import http from '@/api/http'

export function page(query) {
  return http({
    url: '/api/admin/groupType/page',
    method: 'get',
    params: query
  });
}

export function addObj(obj) {
  return http({
    url: '/api/admin/groupType',
    method: 'post',
    data: obj
  });
}

export function getObj(id) {
  return http({
    url: '/api/admin/groupType/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return http({
    url: '/api/admin/groupType/' + id,
    method: 'delete'
  })
}

export function putObj(id, obj) {
  return http({
    url: '/api/admin/groupType/' + id,
    method: 'put',
    data: obj
  })
}
