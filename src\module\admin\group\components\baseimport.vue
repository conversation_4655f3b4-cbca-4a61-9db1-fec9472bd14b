<template>
 <div class=" calendar-list-container disposefiles addentry" >
  <div class="filter-container">
    <el-row>
        <el-col :span="16">
        <el-upload
        style="margin-right: 10px;height:100px;"
        class="upload-demo"
        action=""
        :accept="this.acceptFile('excel')"
        :http-request="uploadFile"
        :auto-upload="false"
        :multiple="false"
        ref="upload"
        :on-change="chooseFile"
        :file-list="fileList"
        >
        <el-button size="small" type="primary">选择文件</el-button>
        <div slot="tip" class="el-upload__tip">只能上传.xlsx文件</div>
        </el-upload>
        </el-col>
      <div class="martop10">
    
          <el-col :span="24">
            <el-button v-waves size="mini"  icon="el-icon-delete" type="primary" @click="handleClickdele">删除</el-button>
           
          </el-col>
     </div>
        <el-col :span='24'>
            <div class="listTables martop10" v-loading="loading">
            <el-table border height="320" size="mini"  :data="fileListdata" ref="multipleTable"  :highlight-current-row="true" style="width: 100%">
                <el-table-column
                type="selection"
                width="40">
                </el-table-column>
                <el-table-column prop="station_name" label="基站名称" min-width="150"  :showOverflowTooltip=true>
                </el-table-column>
                <el-table-column prop="station_code" label="基站编码" min-width="200"  :showOverflowTooltip=true>
                </el-table-column>
                <el-table-column prop="area_name" label="所属地市" min-width="180">
                </el-table-column>
                <!-- <el-table-column prop="date" label="上传时间" min-width="180">
                </el-table-column> -->
                <!-- <el-table-column fixed="right" label="操作" width="130" align="center">
                <template slot-scope="scope">
                    <el-button  @click.native.prevent="handleExlext(scope.row)"  type="text" size="small">下载</el-button>
                    <el-button  @click.native.prevent="handleExlextdel(scope.row)"  type="text" size="small">删除</el-button>
                </template>
                </el-table-column> -->
            </el-table>
            </div> 
             <div class="pagination-container pagination tablPaginat">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
                          :current-page.sync="listQuery.page" :page-sizes="[10,20,30, 50]" :page-size="listQuery.limit"
                          layout="total, sizes, prev, pager, next, jumper" :total="listQuery.total"></el-pagination>
            </div>   
        </el-col>
        <el-col :span="24">
            
        </el-col>
    </el-row>
     <div slot="footer" class="dialog-footer text-center martop10">
            <el-button type="primary" @click="submitUpload" :loading="submitUpflg">提交</el-button>
            <el-button @click="cancel()">取 消</el-button>
             <!-- <el-button  :loading="btnloading" type="primary" @click="uploadExelxt">模板下载</el-button> -->
    </div>
</div>
</div>
</template>
<script>
import {
  fetchTree,
  getStationByUser,//根据用户查询站点
  delStationByUser,//删除基站
} from '@/api/admin/menu/index';
import {getImportsExcel} from '@/api/resource/baseStation/index.js'
 import {
  getUserIds,
  getSysUserId
  } from '@/utils/auth';

import { mapGetters } from 'vuex';
export default {
  name: 'baseimport',
   props: {
      closeDiadl: {
        type: Function,
        default: null
      }
    },
  data() {
    return {
    
      loading:false,
      fileListdata:[],
      fileList:[],   
      submitUpflg:false,  
      btnloading:false,
      userId:getUserIds(),
      groupId:'',
      listQuery: {
        page: 1,
        limit: 10,
        total: 0,
      }
    }
  },
  watch: {
   
  },
  created() {
   // this.getList();
    
  },
  computed: {
    ...mapGetters([
      'elements'
    ])
  },
  methods: {
    handlick(sencId){
    //  console.log(sencId);
    let that = this;
      that.groupId = sencId;
      that.listQuery.params = 1;
      that.listQuery.limit = 10;
      that.listQuery.total = 0;
      this.getList();
      this.fileList = [];
    },
    getList() {
      let that = this;
      let pams = {
         user_id:that.groupId,
         page:that.listQuery.page,
         limit:that.listQuery.limit,
         'role_id':that.groupId,
      };
      getStationByUser(pams).then(data => {
        that.listQuery.total = data.total;
        that.fileListdata = data.rows || [];

        
      });
     
    },
    handleExlext(row){


    },
    handleClickdele(){//删除
      let that = this;
      let seleArr = that.$refs.multipleTable.selection;
      const msg = "你选中了 [ " + seleArr.length + " ] 条数据.";
    if (seleArr.length) {
         // const arr = {arr: JSON.stringify(_selectData)};
      this.$confirm(msg + ",将删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        let arrid = [];
        seleArr.forEach((item) => {
           arrid.push(item.dbid);
          
        });
        let  params = {
          'ids':arrid.join(',')
          };
     // console.log(params)
        delStationByUser(params).then(data => {
           if(data.status == '1'){
                that.$message({
                    showClose: true,
                    message: '删除成功',
                    type: "success",
                    duration: 1500
                    });
                that.getList();
            }else{
                that.$message({
                        showClose: true,
                        message: '删除失败',
                        type: "warning",
                        duration: 1500
                        });
                }
        }).catch(err =>{
             that.$message({
                    showClose: true,
                    message: '网络错误',
                    type: "warning",
                    duration: 1500
                });
        })

      }).catch(() => {
                
     });
        
    }else{
        this.$message.warning('请选择一条数据删除！');
    }

    },
    chooseFile(file, fileList) {// 添加文件、上传成功和上传失败时都会被调用
      if (fileList.length > 1) {
        fileList.splice(0, 1);
      }
      this.fileList = fileList
    },
    acceptFile(e) {
      const allowHook = {
        video: '.mp4, .ogv, .ogg, .webm',
        audio: '.wav, .mp3, .ogg, .acc, .webm, .amr',
        file: 'doc,.docx,.xlsx,.xls,.pdf',
        excel: '.xlsx,.xls',
        img: '.jpg, .jpeg, .png, .gif'
        }
      if (e) {
        return allowHook[e];
      }
      let srt = null
      for (const k in allowHook) {
        srt += allowHook[k]
      }
      return srt
    },
    uploadFile(param){
      let that = this;
      let usid = getUserIds();
       const formData = new FormData();
      formData.append('file', param.file);
      formData.append('creater', that.userId);
      formData.append('user_id', that.groupId);
      formData.append('role_id',that.groupId)
      //return;
      that.submitUpflg = true;
      let upurl = '@/api/admin/user/importExcel'
       getImportsExcel(formData,upurl).then(res => {
        const flag = res;
        //console.log(res)
         that.submitUpflg = false;
        if(res.status == 1){
         
            that.$message({
              showClose: true,
              message: res.data,
              type: "success",
              duration: 2500
              });
          that.getList();
          this.fileList = [];
        }else{
            that.$message({
              showClose: true,
              message: res.data,
              type: "warning",
              duration: 1500
              });

        }
       
      }).catch(err => {
           that.submitUpflg = false;

          that.$message.warning('网络错误');
      })
    
    },
    submitUpload() {
       let that = this;
       if(this.fileList.length === 0) {
        this.$message.warning('请选择上传文件');
        return;
        }
        this.$refs.upload.submit();
    },
    cancel(){
      let that = this;
    //   this.dialogAddVisible2 = false;
      that.fileList = [];
      if(this.closeDiadl){
         this.closeDiadl('1')
      }
    },
    uploadExelxt(){ 
     let that = this;
    //  let params = {
    //    SHEET_ID:that.sheetId
    //  }
    //  //console.log(params)
    //  that.btnloading = true;
    //  let  qparms =  qs.stringify(params);
    //    //http://***********:8765
    //     window.location.href = that.upobj.downurl;
    //      setTimeout(()=>{
    //        that.btnloading = false;
    //      },2000);
     
   },
    handleSizeChange(val) {
    //  this.isLoading = true
       this.listQuery.page = 1;
      this.listQuery.limit = val;
      this.getList();
    },
    handleCurrentChange(val) {
    //  this.isLoading = true
      this.listQuery.page = val;
      this.getList();
    },
  
  }
}
</script>
<style scoped lang="scss">
   .departree{
       padding:10px 5px;
       background-color: #fff;
       overflow:auto;
       border: solid 1px #EBEEF5;
       border-top:none;
   }
</style>
<style lang="scss" >
// @import "src/styles/common.scss";

</style>