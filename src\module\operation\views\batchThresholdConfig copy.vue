<template>
  <div class="app-container">


    <!-- 监控量 -->
    <div>
      <!-- 一级列表 -->
      <div v-if="currentModule == 'list'">
        <el-form :inline="true" :model="queryForm" size="small" label-width="100px" class="demo-form-inline">

          <el-form-item label="地市:">
            <el-select v-model="queryForm.cityId" @change="cityChange" filterable clearable placeholder="请选择">
              <el-option v-for="(item, index) in citylist" v-show="index !== 0" :key="item.cityId"
                :label="item.cityName" :value="item.cityId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="区域:">
            <el-select v-model="queryForm.areaId" @change="areaChange" filterable clearable placeholder="请选择">
              <el-option v-for="item in areaList" :key="item.areaId" :label="item.areaName"
                :value="item.areaId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="局站:">
            <el-select v-model="queryForm.siteId" @change="siteChange" :remote-method="getSite" filterable clearable
              placeholder="请选择">
              <el-option v-for="item in siteList" filterable :key="`${item.area_id}-${item.site_id}`"
                :label="item.site_name" :value="item.site_id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="机房:">
            <el-select v-model="queryForm.roomId" @change="roomChange" :remote-method="getRoom" filterable clearable
              placeholder="请选择">
              <el-option v-for="item in roomlist" :key="`${item.room_id}-${item.site_id}`" :label="item.room_name"
                :value="item.room_id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备类型:">
            <el-select v-model="queryForm.deviceType" @change="devTypeChange" :remote-method="getDeviceType" filterable
              remote clearable placeholder="请输入">
              <el-option v-for="(item, index) in deviceTypeList" :key="`${index}-${item.id}`" :label="item.label"
                :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备名称:">
            <el-select v-model="queryForm.deviceName" filterable remote clearable placeholder="请输入"
              :remote-method="getDeviceName">
              <el-option v-for="(item, index) in devoptions" :key="`${index}-${item.id}-${item.label}`"
                :label="item.label" :value="item.label">
              </el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="测点类型:">
            <el-select v-model="queryForm.signalType" clearable disabled placeholder="请选择">
              <el-option label="遥控-参数" :value="6"></el-option>
              <!-- <el-option label="遥信" :value="3"></el-option> -->
            </el-select>
            <!-- <el-input v-model="queryForm.signalType" placeholder="请输入" clearable></el-input> -->
          </el-form-item>
          <el-form-item label="测点名称:">
            <el-input v-model="queryForm.signalName" placeholder="请输入" clearable></el-input>
          </el-form-item>

          <el-form-item label="">
            <el-button style="margin-left: 80px;" @click="queryFormReset">重置</el-button>
            <el-button type="primary" @click="queryData(1, 10)">查询</el-button>
            <el-button type="primary" @click="showAdd">新增</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="deviceTableData" header-row-class-name="myHeaderClass" size="small" v-loading="tableLoading">
          <el-table-column prop="cityName" align="center" label="地市" show-overflow-tooltip></el-table-column>
          <el-table-column prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
          <el-table-column prop="siteName" align="center" label="局站" show-overflow-tooltip></el-table-column>
          <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
          <el-table-column prop="deviceTypeName" align="center" label="设备类型" show-overflow-tooltip></el-table-column>
          <el-table-column prop="deviceName" align="center" label="设备名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="signalName" align="center" label="测点名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="hLimit" align="center" label="上限" show-overflow-tooltip></el-table-column>
          <el-table-column prop="shLimit" align="center" label="过高上限" show-overflow-tooltip></el-table-column>
          <el-table-column prop="lLimit" align="center" label="下限" show-overflow-tooltip></el-table-column>
          <el-table-column prop="slLimit" align="center" label="过低下限" show-overflow-tooltip></el-table-column>
          <!-- <el-table-column prop="setValue" align="center" label="设置值" show-overflow-tooltip></el-table-column> -->
          <el-table-column prop="" label="操作" width="200">
            <template slot-scope="scope">
              <div class="ad">
                <el-button type="text" size="small" @click="showConfig(scope.row)">配置日志</el-button>
                <el-button type="text" size="small" @click="showDetial(scope.row)">配置详情</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pag">
          <el-pagination @size-change="handleSizeChange" background @current-change="handleCurrentChange"
            :current-page="currentPage" :page-sizes="[10, 20, 50]" :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="total">
          </el-pagination>
        </div>
      </div>




      <el-page-header v-if="currentModule !== 'list'" @back="goback">
        <div slot="content" style="font-size: 14px;">
          {{ content }}
        </div>
      </el-page-header>

      <!-- 设备阈值配置 -->
      <div style="margin-top: 16px;">

        <!-- 新增 -->
        <el-form ref="myForm" v-if="currentModule == 'add'" :rules="rules" :model="deviceThresholdData"
          label-width="100px" size="small">
          <el-form-item label="地市:">
            <el-select style="width: 400px" @change="getAreaOptions" filterable
              v-model="deviceThresholdData.selectCityIds" multiple placeholder="请选择">
              <el-option v-for="(item, index) in cfgcitylist" v-show="index !== 0" :key="item.cityId"
                :label="item.cityName" :value="item.cityId"></el-option>
            </el-select>
          </el-form-item>
          <!-- <div class="selectWrap"></div> -->
          <el-form-item label="区域:">
            <el-select @remove-tag="areaRemove" style="width: 400px" filterable
              v-model="deviceThresholdData.selectAreaIds" multiple placeholder="请选择">
              <el-option v-for="item in cfgareaList" :key="item.id" :label="item.label" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <!-- <div class="selectWrap"></div> -->
          <el-form-item label="局站:">
            <el-select @remove-tag="siteRemove" v-loadmore="getSiteOptions" reserve-keyword style="width: 400px" remote
              :remote-method="getSiteOptions" filterable v-model="deviceThresholdData.selectSiteIds" multiple
              placeholder="请输入进行检索">
              <el-option v-for="item in cfgsiteList" :key="item.id" :label="item.label" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <!-- <div class="selectWrap"> </div> -->
          <el-form-item label="机房:">
            <el-select @remove-tag="roomRemove" v-loadmore="getRoomOptions" style="width: 400px" remote
              :remote-method="getRoomOptions" v-model="deviceThresholdData.selectRoomIds" filterable multiple
              placeholder="请输入进行检索">
              <el-option v-for="item in cfgroomlist" :key="item.id" :label="item.label" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <!-- <div class="selectWrap"> </div> -->
          <div>
            <el-form-item label="设备类型:">
              <el-select @remove-tag="devTypeRemove" v-loadmore="getDevTypeOptions" style="width: 400px" remote
                :remote-method="getDevTypeOptions" v-model="deviceThresholdData.selectDevTypeIds" filterable multiple
                placeholder="请输入进行检索">
                <el-option v-for="item in cfgdeviceTypeList" :key="item.id" :label="item.label"
                  :value="item.id"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="设备名称:">
              <el-select @remove-tag="devIdRemove" v-loadmore="getDevNameOptions" filterable remote
                :remote-method="getDevNameOptions" style="width: 400px" multiple
                v-model="deviceThresholdData.selectDevNameIds" placeholder="请输入进行检索">
                <el-option v-for="item in cfgdeviceNameList" :key="item.id" :label="item.label"
                  :value="item.id"></el-option>
              </el-select>
            </el-form-item>
          </div>
          <!-- <div class="selectWrap"></div> -->
          <div>
            <el-form-item label="测点类型:">
              <el-select style="width: 400px" disabled multiple v-model="deviceThresholdData.signalTypeIds"
                placeholder="请选择">
                <el-option label="遥控-参数" :value="6"></el-option>
                <!-- <el-option label="遥测" :value="1"></el-option>
                <el-option label="遥信" :value="3"></el-option> -->
              </el-select>
            </el-form-item>
            <el-form-item label="测点名称:">
              <el-select v-loadmore="getSignalNameOptions" multiple filterable remote
                :remote-method="getSignalNameOptions" style="width: 400px" v-model="deviceThresholdData.signalNameIds"
                placeholder="请输入进行检索">
                <el-option v-for="item in cfgSignalNameList" :key="item.id" :label="item.label"
                  :value="item.id"></el-option>
              </el-select>
            </el-form-item>

          </div>
          <!-- <div class="selectWrap"></div> -->
          <el-row>
            <el-col :span="6">
              <el-form-item label="上限：" prop="HLimit">
                <!-- <el-input-number style="width: auto;" v-model="deviceThresholdData.HLimit" controls-position="right" :precision="2"  ></el-input-number> -->
                <el-input v-model="deviceThresholdData.HLimit" autocomplete="off"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="下限：" prop="LLimit">
                <el-input v-model="deviceThresholdData.LLimit"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="过高上限：" prop="SHLimit">
                <el-input v-model="deviceThresholdData.SHLimit"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="过低下限：" prop="SLLimit">
                <el-input v-model="deviceThresholdData.SLLimit"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <el-row>
            <el-col :span="6">
              <el-form-item label="设置值：" prop="SetValue">
                <el-input v-model="deviceThresholdData.SetValue"></el-input>
              </el-form-item>
            </el-col>
          </el-row> -->
          <el-form-item>
            <el-button @click="sdeviceThresholdConfig" :loading="submitThresholdLoading" type="primary">确定</el-button>
          </el-form-item>
        </el-form>

        <!-- 配置日志 -->
        <div class="detialWrap" v-if="currentModule == 'config'">
          <div class="title">审批详情</div>
          <el-steps :space="200" finish-status="success">
            <el-step v-for="(item, index) in detail.transferInfos"
              :status="[0, 2].includes(item.handleStatus) ? 'success' : 'wait'" :key="index"
              :title="item.handlerUser || '-'">
              <template slot="description">
                <div> {{ item.handleStatus == 1 ? '' : item.handleTime }}</div>
                <div v-if="item.handleStatus == 1">待审批</div>
                <div> {{ item.transferNodeName || '-' }}</div>
              </template>
            </el-step>
          </el-steps>
          <div class="title">流程详情</div>
          <el-descriptions style="width: 850px;" :column="4" direction="vertical" border>
            <el-descriptions-item label="工单编号">{{ detail.orderNum }}</el-descriptions-item>
            <el-descriptions-item label="申请人">{{ detail.applyUser }}</el-descriptions-item>
            <el-descriptions-item label="申请时间">{{ detail.startDate }}</el-descriptions-item>
            <el-descriptions-item label="流程名称">{{ detail.flowName }}</el-descriptions-item>
            <el-descriptions-item label="当前环节名称">{{ detail.nodeName }}</el-descriptions-item>
            <el-descriptions-item label="下一环节名称">{{ detail.nextNodeName }}</el-descriptions-item>
            <el-descriptions-item label="下一环节审批人">{{ detail.nextHandlerUsers }}</el-descriptions-item>
          </el-descriptions>

          <div class="title">
            <span>配置详情</span>
          </div>
          <div style="display: flex;">
            <!-- 批量配置 -->
            <el-table :data="currentRow" header-row-class-name="myHeaderClass" size="small" border>
              <el-table-column prop="cityName" align="center" label="地市" show-overflow-tooltip></el-table-column>
              <el-table-column prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
              <el-table-column prop="siteName" align="center" label="局站" show-overflow-tooltip></el-table-column>
              <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
              <el-table-column prop="devTypeName" align="center" label="设备类型" show-overflow-tooltip></el-table-column>
              <el-table-column prop="devName" align="center" label="设备名称" show-overflow-tooltip></el-table-column>
              <el-table-column prop="label" align="center" label="测点名称" show-overflow-tooltip></el-table-column>
              <el-table-column prop="HLimit" align="center" label="上限" show-overflow-tooltip></el-table-column>
              <el-table-column prop="SHLimit" align="center" label="过高上限" show-overflow-tooltip></el-table-column>
              <el-table-column prop="LLimit" align="center" label="下限" show-overflow-tooltip></el-table-column>
              <el-table-column prop="SLLimit" align="center" label="过低下限" show-overflow-tooltip></el-table-column>
              <!-- <el-table-column prop="SetValue" align="center" label="设置值" show-overflow-tooltip></el-table-column> -->
            </el-table>
          </div>
        </div>



        <!-- 配置详情2 -->
        <div v-if="currentModule == 'detail'">
          <el-table :data="currentRow" header-row-class-name="myHeaderClass" size="small" border>
            <el-table-column prop="cityName" align="center" label="地市" show-overflow-tooltip></el-table-column>
            <el-table-column prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
            <el-table-column prop="siteName" align="center" label="局站" show-overflow-tooltip></el-table-column>
            <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
            <el-table-column prop="devTypeName" align="center" label="设备类型" show-overflow-tooltip></el-table-column>
            <el-table-column prop="devName" align="center" label="设备名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="label" align="center" label="测点名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="HLimit" align="center" label="上限" show-overflow-tooltip></el-table-column>
            <el-table-column prop="SHLimit" align="center" label="过高上限" show-overflow-tooltip></el-table-column>
            <el-table-column prop="LLimit" align="center" label="下限" show-overflow-tooltip></el-table-column>
            <el-table-column prop="SLLimit" align="center" label="过低下限" show-overflow-tooltip></el-table-column>
            <!-- <el-table-column prop="SetValue" align="center" label="设置值" show-overflow-tooltip></el-table-column> -->
          </el-table>
        </div>
      </div>

    </div>

  </div>
</template>

<script>
export default {

  data() {
    return {
      submitThresholdLoading: false,

      rules: {
        HLimit: [
          {
            validator: (rule, value, callback) => {
              if (!value) return callback()
              const regex = /(^[1-9]\d*|0)(\.\d{1,2})?$/;
              if (regex.test(value)) {
                callback();
              } else {
                callback(new Error('请输入正确的整数或保留两位小数的小数'));
              }
            },
            trigger: 'blur'
          }
        ],
        LLimit: [
          {
            validator: (rule, value, callback) => {
              if (!value) return callback()
              const regex = /(^[1-9]\d*|0)(\.\d{1,2})?$/;
              if (regex.test(value)) {
                callback();
              } else {
                callback(new Error('请输入正确的整数或保留两位小数的小数'));
              }
            },
            trigger: 'blur'
          }
        ],
        SHLimit: [
          {
            validator: (rule, value, callback) => {
              if (!value) return callback()
              const regex = /(^[1-9]\d*|0)(\.\d{1,2})?$/;
              if (regex.test(value)) {
                callback();
              } else {
                callback(new Error('请输入正确的整数或保留两位小数的小数'));
              }
            },
            trigger: 'blur'
          }
        ],
        SLLimit: [
          {
            validator: (rule, value, callback) => {
              if (!value) return callback()
              const regex = /(^[1-9]\d*|0)(\.\d{1,2})?$/;
              if (regex.test(value)) {
                callback();
              } else {
                callback(new Error('请输入正确的整数或保留两位小数的小数'));
              }
            },
            trigger: 'blur'
          }
        ],
        SetValue: [
          {
            validator: (rule, value, callback) => {
              if (!value) return callback()
              const regex = /(^[1-9]\d*|0)(\.\d{1,2})?$/;
              if (regex.test(value)) {
                callback();
              } else {
                callback(new Error('请输入正确的整数或保留两位小数的小数'));
              }
            },
            trigger: 'blur'
          }
        ]

      },
      currentModule: 'list', // list 列表， add 新增 ，detail 详情，config 配置日志
      detail: null, // 配置日志
      // 查询条件下拉数据
      citylist: [],
      areaList: [],
      siteList: [],
      roomlist: [],
      deviceTypeList: [],
      cedianlist: [],
      cedianNameList: [],
      devoptions: [],

      //阈值设置下拉数据 
      cfgcitylist: [],
      cfgareaList: [],
      cfgsiteList: [],
      cfgroomlist: [],
      cfgdeviceTypeList: [], // 设备类型
      cfgdeviceNameList: [], // 设备名称
      cfgcedianlist: [],// 测点类型
      cfgSignalNameList: [], // 测点名称

      tableLoading: false,
      warningLoading: false,
      hyDataLoading: false,
      arrow: require('./img/arrow.svg'),
      opricon: require('./../views/img/齿轮组.svg'),
      tabActiveName: 'first',
      queryForm: {
        areaId: '',
        cityId: '',
        deviceName: '',
        deviceType: '',
        roomId: '',
        signalName: '',
        signalType: 6,
        siteId: '',
      },
      deviceTableData: [],
      pageSize: 10,
      currentPage: 1,
      total: 0,
      content: '',
      currentRow: null,

      // 设备阈值配置
      deviceThresholdFlag: false,
      deviceThresholdActive: '当前配置',
      sitePage: 1,
      siteSearch: '',
      roomPage: 1,
      roomSearch: '',
      devTypePage: 1,
      devTypeSearch: '',
      devNamePage: 1,
      devNameSearch: '',
      signalNamePage: 1,
      signalNameSearch: '',
      deviceThresholdData: {
        selectCityIds: [],
        selectAreaIds: [],
        selectSiteIds: [],
        selectRoomIds: [],
        selectDevTypeIds: [],
        selectDevNameIds: [],
        signalTypeIds: [6],
        signalNameIds: [],
        HLimit: '',
        LLimit: '',
        SHLimit: '',
        SLLimit: '',
        SetValue: ''
      },
      olddeviceThresholdData: {},
      deviceThresholdHistoryData: [],
      deviceThresholdPageSize: 10,
      deviceThresholdCurrentPage: 1,
      deviceThresholdTotal: 0,

      // 告警级别设置
      warnLevelSetFlag: false,
      warnLevelSetActive: '当前配置',
      warnLevelSetData: {},
      oldwarnLevelSetData: {},
      warnLevelSetHistoryData: [],
      warnLevelPageSize: 10,
      warnLevelCurrentPage: 1,
      warnLevelTotal: 0,

      // 权限按钮
      btnshowHistory: false,
      btnoperateHistory: false,
      btndeviceThreshold: false,
      btnwarnLevelSet: false,
      btnopenDevice: false,
      btncloseDevice: false,
    }
  },
  directives: {  // 在组件中接受一个 directives 的选项
    loadmore: {
      inserted(el, binding) {
        const dom = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');  // 获取下拉框元素
        dom.addEventListener('scroll', function () {       // 监听元素触底
          const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      },
    },
  },
  created() {
    this.queryCityList()
    this.getSite()
    this.getRoom()
    this.getDeviceType()
    this.getDeviceName()
    this.queryData()
  },
  methods: {
    validateInput(rule, value, callback) {
      const reg = /^\d+(\.\d{1,2})?$/
      if (!reg.test(value)) {
        callback(new Error('请输入正确的数字（最多两位小数）'))
      } else {
        callback()
      }
    },
    async showConfig(row) {
      let params = {
        orderNum: row.orderNum,
        handleUser: row.applyUser
      }

      let res = await this.$api.queryOrderDetail(params)
      if (res.code == 200) {
        //  res.data.afterJson = JSON.parse(res.data.afterJson)
        this.detail = res.data
        this.content = '配置日志'

        let currentRow = JSON.parse(row.afterJson).signalList
        currentRow = currentRow.map(v => {
          let obj = {
            ...v,
            HLimit: row.hLimit,
            LLimit: row.lLimit,
            SHLimit: row.shLimit,
            SLLimit: row.slLimit,
            SetValue: row.setValue,
          }
          return obj
        })
        // console.log(row, currentRow)
        this.currentRow = currentRow

        this.currentModule = 'config'
      } else {
        this.$message.error(res.msg)
      }
    },
    showDetial(row) {
      let currentRow = JSON.parse(row.afterJson).signalList
      currentRow = currentRow.map(v => {
        let obj = {
          ...v,
          HLimit: row.hLimit,
          LLimit: row.lLimit,
          SHLimit: row.shLimit,
          SLLimit: row.slLimit,
          SetValue: row.setValue,
        }
        return obj
      })
      // console.log(row, currentRow)
      this.currentRow = currentRow
      this.currentModule = 'detail'
      this.content = '配置详情'
    },
    showAdd() {
      this.currentModule = 'add'
      this.deviceThresholdData = {
        selectCityIds: [],
        selectAreaIds: [],
        selectSiteIds: [],
        selectRoomIds: [],
        selectDevTypeIds: [],
        selectDevNameIds: [],
        signalTypeIds: [6],
        signalNameIds: [],
        HLimit: '',
        LLimit: '',
        SHLimit: '',
        SLLimit: '',
        SetValue: ''
      },
        this.content = '设备阈值新增'
    },

    areaRemove() {
      this.deviceThresholdData.selectSiteIds = this.cfgsiteList
        .filter(v => this.deviceThresholdData.selectAreaIds.includes(v.lastId))
        .filter(v => this.deviceThresholdData.selectSiteIds.includes(v.id))
        .map(v => v.id)
      this.cfgsiteList = this.cfgsiteList.filter(v => this.deviceThresholdData.selectAreaIds.includes(v.lastId))
      this.siteRemove()
    },

    siteRemove() {
      this.deviceThresholdData.selectRoomIds = this.cfgroomlist
        .filter(v => this.deviceThresholdData.selectSiteIds.includes(v.lastId))
        .filter(v => this.deviceThresholdData.selectRoomIds.includes(v.id))
        .map(v => v.id)
      this.cfgroomlist = this.cfgroomlist
        .filter(v => this.deviceThresholdData.selectSiteIds.includes(v.lastId))
      this.roomRemove()
    },

    roomRemove() {
      this.deviceThresholdData.selectDevTypeIds = this.cfgdeviceTypeList
        .filter(v => this.deviceThresholdData.selectRoomIds.includes(v.lastId))
        .filter(v => this.deviceThresholdData.selectDevTypeIds.includes(v.id))
        .map(v => v.id)
      this.cfgdeviceTypeList = this.cfgdeviceTypeList
        .filter(v => this.deviceThresholdData.selectRoomIds.includes(v.lastId))
      this.devTypeRemove()
    },

    devTypeRemove() {
      this.deviceThresholdData.selectDevNameIds = this.cfgdeviceNameList
        .filter(v => this.deviceThresholdData.selectDevTypeIds.includes(v.devTypeId))
        .filter(v => this.deviceThresholdData.selectDevNameIds.includes(v.id))
        .map(v => v.id)
      this.cfgdeviceNameList = this.cfgdeviceNameList
        .filter(v => this.deviceThresholdData.selectDevTypeIds.includes(v.devTypeId))
      this.devIdRemove()
    },

    devIdRemove() {
      this.deviceThresholdData.signalNameIds = this.cfgSignalNameList
        .filter(v => this.deviceThresholdData.selectDevNameIds.includes(v.lastId))
        .filter(v => this.deviceThresholdData.signalNameIds.includes(v.id))
        .map(v => v.id)
      this.cfgSignalNameList = this.cfgSignalNameList
        .filter(v => this.deviceThresholdData.selectDevNameIds.includes(v.lastId))
    },

    // 获取多选区域下拉列表
    getAreaOptions() {
      if (!this.deviceThresholdData.selectCityIds.length) {
        this.cfgareaList = []
        return
      }
      let params = {
        pid: this.deviceThresholdData.selectCityIds,
        current: 1,
        size: 999
      }
      this.$api.getBatchArea(params).then(res => {
        if (res.code == 200) {
          this.cfgareaList = res.data.records
          // 删除区县
          this.deviceThresholdData.selectAreaIds = this.cfgareaList
            .filter(v => this.deviceThresholdData.selectAreaIds.includes(v.id))
            .map(v => v.id)
          // 删除局站 
          this.deviceThresholdData.selectSiteIds = this.cfgsiteList
            .filter(v => this.deviceThresholdData.selectAreaIds.includes(v.lastId))
            .filter(v => this.deviceThresholdData.selectSiteIds.includes(v.id))
            .map(v => v.id)
          // 删除机房
          this.deviceThresholdData.selectRoomIds = this.cfgroomlist
            .filter(v => this.deviceThresholdData.selectSiteIds.includes(v.lastId))
            .filter(v => this.deviceThresholdData.selectRoomIds.includes(v.id))
            .map(v => v.id)
          // 删除设备类型与设备名称
          this.deviceThresholdData.selectDevTypeIds = this.cfgdeviceTypeList
            .filter(v => this.deviceThresholdData.selectRoomIds.includes(v.lastId))
            .filter(v => this.deviceThresholdData.selectDevTypeIds.includes(v.id))
            .map(v => v.id)
          this.deviceThresholdData.selectDevNameIds = this.cfgdeviceNameList
            .filter(v => this.deviceThresholdData.selectRoomIds.includes(v.lastId))
            .filter(v => this.deviceThresholdData.selectDevNameIds.includes(v.id))
            .map(v => v.id)
          // 删除测点名称
          this.deviceThresholdData.signalNameIds = this.cfgSignalNameList
            .filter(v => this.deviceThresholdData.selectDevNameIds.includes(v.lastId))
            .filter(v => this.deviceThresholdData.signalNameIds.includes(v.id))
            .map(v => v.id)
        }
      })
    },

    // 获取局站多选
    async getSiteOptions(queryString) {
      if (!queryString && this.sitePage == 1) return
      if (queryString && this.siteSearch !== queryString) {
        this.siteSearch = queryString
        this.sitePage = 1
      }
      if (!this.cfgsiteList.length) this.sitePage = 1
      if (!this.deviceThresholdData.selectAreaIds.length) return this.$message.error('请先选择区县')
      let res = await this.$api.getBatchSite({
        name: this.siteSearch,
        pid: this.deviceThresholdData.selectAreaIds,
        current: this.sitePage,
        size: 20
      })
      if (res.code == 200) {
        // if (res.data.records.length == 0) {
        //   this.$message.success('未查询到更多数据！')
        // }
        this.cfgsiteList = [...this.cfgsiteList, ...res.data.records].filter((value, index, self) => {
          return self.findIndex((v) => v.id === value.id) === index;
        });

        // if (this.sitePage == 1) {
        //   this.cfgsiteList = res.data.records
        // } else {
        //   this.cfgsiteList = [...this.cfgsiteList, ...res.data.records]
        // }
        this.sitePage++
      } else {
        this.$message.error(res.msg)
      }
    },
    //获取机房多选
    async getRoomOptions(queryString) {
      if (!queryString && this.roomPage == 1) return
      if (queryString && this.roomSearch !== queryString) {
        this.roomSearch = queryString
        this.roomPage = 1
      }
      if (!this.cfgroomlist.length) this.roomPage = 1
      if (!this.deviceThresholdData.selectSiteIds.length) return this.$message.error('请先选择局站')
      let res = await this.$api.getBatchRoom({
        name: this.roomSearch,
        pid: this.deviceThresholdData.selectSiteIds,
        current: this.roomPage,
        size: 20
      })
      if (res.code == 200) {
        // if (res.data.records.length == 0) {
        //   this.$message.success('暂无更多数据！')
        // }
        this.cfgroomlist = [...this.cfgroomlist, ...res.data.records].filter((value, index, self) => {
          return self.findIndex((v) => v.id === value.id) === index;
        });

        // if (this.roomPage == 1) {
        //   this.cfgroomlist = res.data.records
        // } else {
        //   this.cfgroomlist = [...this.cfgroomlist, ...res.data.records]
        // }
        this.roomPage++
      } else {
        this.$message.error(res.msg)
      }
    },
    // 设备类型下拉数据选择
    async getDevTypeOptions(queryString) {
      if (!queryString && this.devTypePage == 1) return
      if (queryString && this.devTypeSearch !== queryString) {
        this.devTypeSearch = queryString
        this.devTypePage = 1
      }
      if (!this.cfgdeviceTypeList.length) this.devTypePage = 1
      if (!this.deviceThresholdData.selectRoomIds.length) return this.$message.error('请先选择机房')
      let res = await this.$api.getBatchType({
        name: this.devTypeSearch,
        pid: this.deviceThresholdData.selectRoomIds,
        current: this.devTypePage,
        size: 20
      })
      if (res.code == 200) {
        // if (res.data.records.length == 0) {
        //   this.$message.success('暂无更多数据！')
        // }
        this.cfgdeviceTypeList = [...this.cfgdeviceTypeList, ...res.data.records].filter((value, index, self) => {
          return self.findIndex((v) => v.id === value.id) === index;
        });

        // if (this.devTypePage == 1) {
        //   this.cfgdeviceTypeList = res.data.records
        // } else {
        //   this.cfgdeviceTypeList = [...this.cfgdeviceTypeList, ...res.data.records]
        // }
        this.devTypePage++
      } else {
        this.$message.error(res.msg)
      }
    },
    // 设备名称下拉数据选择
    async getDevNameOptions(queryString) {
      if (!queryString && this.devNamePage == 1) return
      if (queryString && this.devNameSearch !== queryString) {
        this.devNameSearch = queryString
        this.devNamePage = 1
      }
      if (!this.cfgdeviceNameList.length) this.devNamePage = 1
      if (!this.deviceThresholdData.selectDevTypeIds.length) return this.$message.error('请先选择设备类型')
      let res = await this.$api.getBatchName({
        name: this.devNameSearch,
        type: this.deviceThresholdData.selectDevTypeIds,
        pid: this.deviceThresholdData.selectRoomIds,
        current: this.devNamePage,
        size: 20
      })
      if (res.code == 200) {
        // if (res.data.records.length == 0) {
        //   this.$message.success('暂无更多数据！')
        // }
        this.cfgdeviceNameList = [...this.cfgdeviceNameList, ...res.data.records].filter((value, index, self) => {
          return self.findIndex((v) => v.id === value.id) === index;
        });

        // if (this.devNamePage == 1) {
        //   this.cfgdeviceNameList = res.data.records
        // } else {
        //   this.cfgdeviceNameList = [...this.cfgdeviceNameList, ...res.data.records]
        // }
        this.devNamePage++
      } else {
        this.$message.error(res.msg)
      }
    },
    // 测点名称下拉数据选择
    async getSignalNameOptions(queryString) {
      if (!queryString && this.
        signalNamePage == 1) return
      if (queryString && this.signalNameSearch !== queryString) {
        this.signalNameSearch = queryString
        this.signalNamePage = 1
      }
      if (!this.cfgSignalNameList.length) this.signalNamePage = 1
      if (
        !this.deviceThresholdData.selectDevNameIds.length ||
        !this.deviceThresholdData.selectDevTypeIds.length
      ) {
        return this.$message.error('请先选择设备类型与设备名称')
      }
      let res = await this.$api.getBatchSignal({
        name: this.signalNameSearch,
        devId: this.deviceThresholdData.selectDevNameIds,
        suId: this.cfgdeviceNameList.filter(v => this.deviceThresholdData.selectDevNameIds.includes(v.id)).map(v => v.suId),
        type: this.deviceThresholdData.signalTypeIds,
        current: this.signalNamePage,
        signalType: 6,
        size: 20
      })
      if (res.code == 200) {
        // if (res.data.records.length == 0) {
        //   this.$message.success('暂无更多数据！')
        // }
        this.cfgSignalNameList = [...this.cfgSignalNameList, ...res.data.records].filter((value, index, self) => {
          return self.findIndex((v) => v.id === value.id) === index;
        });

        // if (this.signalNamePage == 1) {
        //   this.cfgSignalNameList = res.data.records
        // } else {
        //   this.cfgSignalNameList = [...this.cfgSignalNameList, ...res.data.records]
        // }
        this.signalNamePage++
      } else {
        this.$message.error(res.msg)
      }
    },



    // 初始化地市
    queryCityList() {
      let params = {
        cityName: "",
        pageNum: 1,
        pageSize: 999
      }
      this.$api.queryCityList(params).then(res => {
        if (res.code == 200) {
          this.citylist = res.data.records
          this.cfgcitylist = res.data.records
        }
      })
    },
    cityChange(val) {
      this.queryForm.areaId = ''
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.queryForm.deviceName = ''
      this.areaList = []
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      this.devoptions = []
      this.areaList = this.citylist.find(v => v.cityId == val).areaList
      this.getSite()
      this.getRoom()
      this.getDeviceName()
      this.getDeviceType()
    },
    areaChange() {
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.queryForm.deviceName = ''
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      this.devoptions = []
      this.getSite()
      this.getRoom()
      this.getDeviceName()
      this.getDeviceType()
    },
    siteChange() {
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.queryForm.deviceName = ''
      this.roomlist = []
      this.deviceTypeList = []
      this.devoptions = []
      this.getRoom()
      this.getDeviceName()
      this.getDeviceType()
    },
    roomChange() {
      this.queryForm.deviceType = ''
      this.queryForm.deviceName = ''
      this.deviceTypeList = []
      this.devoptions = []
      this.getDeviceName()
      this.getDeviceType()
    },
    devTypeChange() {
      this.queryForm.deviceName = ''
      this.devoptions = []
      this.getDeviceName()
    },

    getSite(val) {
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      let params = {
        cityId: this.queryForm.cityId,
        areaId: this.queryForm.areaId,
        siteName: val,
        pageNum: 1,
        pageSize: 999
      }

      this.$api.querySiteInfo(params).then(res => {
        if (res.code == 200) {
          this.siteList = res.data.records
        }
      })
    },
    getRoom(val) {
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.roomlist = []
      this.deviceTypeList = []
      let params = {
        cityId: this.queryForm.cityId,
        areaId: this.queryForm.areaId,
        siteId: this.queryForm.siteId,
        roomName: val,
        pageNum: 1,
        pageSize: 999
      }
      this.$api.queryRoomInfo(params).then(res => {
        if (res.code == 200) {
          this.roomlist = res.data.records
        }
      })
    },
    async getDeviceName(queryString) {
      let res = await this.$api.deviceNameQuery({
        deviceName: queryString,
        cityId: this.queryForm.cityId ? [this.queryForm.cityId] : [],
        areaId: this.queryForm.areaId ? [this.queryForm.areaId] : [],
        siteId: this.queryForm.siteId ? [this.queryForm.siteId] : [],
        roomId: this.queryForm.roomId ? [this.queryForm.roomId] : [],
        deviceType: this.queryForm.deviceType ? [this.queryForm.deviceType] : [],
        current: 1,
        size: 999
      })
      if (res.code == 200) {
        this.devoptions = res.data.records
      } else {
        this.$message.error(res.msg)

      }
    },
    async getDeviceType(val) {
      let res = await this.$api.queryDeviceType({
        deviceType: val,
        cityId: this.queryForm.cityId ? [this.queryForm.cityId] : [],
        areaId: this.queryForm.areaId ? [this.queryForm.areaId] : [],
        siteId: this.queryForm.siteId ? [this.queryForm.siteId] : [],
        roomId: this.queryForm.roomId ? [this.queryForm.roomId] : [],
        current: 1,
        size: 999
      })
      if (res.code == 200) {
        this.deviceTypeList = res.data.records
      } else {
        this.$message.error(res.msg)
      }
    },


    queryFormReset() {
      this.areaList = []
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      this.devoptions = []
      this.queryForm = {
        areaId: '',
        cityId: '',
        deviceName: '',
        deviceType: '',
        roomId: '',
        signalName: '',
        signalType: 6,
        siteId: '',
      }
      this.queryData()
      this.role = sessionStorage.getItem('role')
      this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
      this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''

      if (this.role == '1') {
        this.cityDisabled = false
        this.areaDisabled = false
      }

      if (['94', '95'].includes(this.role)) {
        this.cityDisabled = true
      }
      this.queryCityList()
      this.getSite()
      this.getRoom()
      this.getDeviceType()
      this.getDeviceName()
      this.queryData()
      this.queryData(this.queryForm.current)
    },

    // 监控量
    queryData(pageNum, pageSize) {
      this.tableLoading = true
      if (pageNum) this.currentPage = pageNum
      if (pageSize) this.pageSize = pageSize
      let params = {
        ...this.queryForm,
        current: pageNum || this.currentPage,
        size: pageSize || this.pageSize
      }
      this.$api.getthresholdlist(params).then(res => {
        if (res.code == 200) {
          this.deviceTableData = res.data.records
          this.tableLoading = false
          this.total = res.data.total
        } else {
          this.tableLoading = false
          this.$message.error(res.msg)
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.queryData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.queryData()
    },

    // 返回
    goback() {
      this.currentModule = 'list'
      this.currentRow = null
      this.queryData(1, 10)

    },

    async sdeviceThresholdConfig() {

      this.$refs['myForm'].validate(async (valid) => {
        if (valid) {
          if (!this.deviceThresholdData.selectCityIds.length) return this.$message.error('请选择地市')
          if (!this.deviceThresholdData.selectAreaIds.length) return this.$message.error('请选择区县')
          if (!this.deviceThresholdData.selectSiteIds.length) return this.$message.error('请选择局站')
          if (!this.deviceThresholdData.selectRoomIds.length) return this.$message.error('请选择机房')
          if (!this.deviceThresholdData.selectDevTypeIds.length) return this.$message.error('请选择设备类型')
          if (!this.deviceThresholdData.selectDevNameIds.length) return this.$message.error('请选择设备名称')
          if (!this.deviceThresholdData.signalTypeIds.length) return this.$message.error('请选择测点类型')
          if (!this.deviceThresholdData.signalNameIds.length) return this.$message.error('请选择测点名称')
          if (
            !this.deviceThresholdData.HLimit &&
            !this.deviceThresholdData.LLimit &&
            !this.deviceThresholdData.SHLimit &&
            !this.deviceThresholdData.SLLimit &&
            !this.deviceThresholdData.SetValue
          ) {
            return this.$message.error('请变更阈值参数后再保存！')
          }
          this.submitThresholdLoading = true
          let afterJson = {
            signalList: this.cfgSignalNameList
              .filter(v => this.deviceThresholdData.signalNameIds.includes(v.id))
              .map(v => {
                let obj = {
                  ...v,
                  HLimit: this.deviceThresholdData.HLimit,
                  LLimit: this.deviceThresholdData.LLimit,
                  SHLimit: this.deviceThresholdData.SHLimit,
                  SLLimit: this.deviceThresholdData.SLLimit,
                  SetValue: this.deviceThresholdData.SetValue,
                }
                return obj
              }),
          }
          // console.log(afterJson)

          let params = {
            orderType: 3,
            businessInfoBatchReq: {
              cityId: this.deviceThresholdData.selectCityIds.join(','),
              cityName: this.cfgcitylist.filter(v => this.deviceThresholdData.selectCityIds.includes(v.cityId)).map(v => v.cityName).join(','),
              areaId: this.deviceThresholdData.selectAreaIds.join(','),
              areaName: this.cfgareaList.filter(v => this.deviceThresholdData.selectAreaIds.includes(v.id)).map(v => v.label).join(','),
              siteId: this.deviceThresholdData.selectSiteIds.join(','),
              siteName: this.cfgsiteList.filter(v => this.deviceThresholdData.selectSiteIds.includes(v.id)).map(v => v.label).join(','),
              roomId: this.deviceThresholdData.selectRoomIds.join(','),
              roomName: this.cfgroomlist.filter(v => this.deviceThresholdData.selectRoomIds.includes(v.id)).map(v => v.label).join(','),
              devId: this.deviceThresholdData.selectDevNameIds.join(','),
              devName: this.cfgdeviceNameList.filter(v => this.deviceThresholdData.selectDevNameIds.includes(v.id)).map(v => v.label).join(','),
              suId: this.cfgdeviceNameList.filter(v => this.deviceThresholdData.selectDevNameIds.includes(v.id)).map(v => v.suId).join(','),
              devType: this.deviceThresholdData.selectDevTypeIds.join(','),
              devTypeName: this.cfgdeviceTypeList.filter(v => this.deviceThresholdData.selectDevTypeIds.includes(v.id)).map(v => v.label).join(','),
              signalId: this.deviceThresholdData.signalNameIds.join(','),
              signalName: this.cfgSignalNameList.filter(v => this.deviceThresholdData.signalNameIds.includes(v.id)).map(v => v.label).join(','),
              signalType: this.deviceThresholdData.signalTypeIds.join(','),
              hLimit: this.deviceThresholdData.HLimit,
              lLimit: this.deviceThresholdData.LLimit,
              shLimit: this.deviceThresholdData.SHLimit,
              slLimit: this.deviceThresholdData.SLLimit,
              setValue: this.deviceThresholdData.SetValue,
              afterJson: JSON.stringify(afterJson)
            }
          }
          // console.log(params)
          //  return
          try {
            let res = await this.$api.orderCreate(params)
            if (res.code == 200) {
              this.goback()
              this.$message.success('保存成功')
            } else {
              this.$message.error(res.msg)
            }
            this.submitThresholdLoading = false
          } catch (error) {
            this.submitThresholdLoading = false
          }
        } else {
          return false;
        }
      });


    },

  }
}
</script>

<style lang="scss" scoped>
.ad {
  display: flex;
  gap: 6px;

  i {
    cursor: pointer;
  }
}

.pag {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 12px;
}

.selectWrap {
  width: 600px;
  height: 60px;
  margin-left: 100px;
  overflow: scroll;
  margin-bottom: 16px;
  //box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
  border: 1px solid #d7dae2;
}

.detialWrap {
  padding: 24px;

  .title {
    font-size: 14px;
    font-weight: bold;
    margin: 12px 0;
  }
}
</style>