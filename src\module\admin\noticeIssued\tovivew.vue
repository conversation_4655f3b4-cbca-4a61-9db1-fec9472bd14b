<!-- 定制任务页面 -->
<template>
<div class=" calendar-list-container disposefiles handFilter positcont">
    <div class="filter-container bgse">
    <div class="noticont">
      <el-row :gutter="40">
        <el-col :span="inde>1?6:12" v-for="(item,inde) in listarr" :key="inde" >
          <div class="noticodv">
           <h6 class="noticonh6">{{item.tit}}专业公告</h6>
           <div class="noticodvdv">
           <div class="noticodvdvcon" v-for="(ite,ind) in item.lisarr" :key="ind" @click="handletovivew(ite)">
             <el-tooltip class="item" effect="dark" :content="ite.notice_title" placement="top">
               <h6 class="noticodvh6">{{ite.notice_title}}</h6>
            </el-tooltip>
            <p class="noticodvp">
              <span class="noticodvsp1">{{ite.name}}</span>
              <span class="noticodvsp2">{{ite.create_time}}</span>
            </p>
           </div>
           </div>
          </div>
        </el-col>
      </el-row>

    </div>
    <!-- 编辑dilog -->
    <el-dialog :title="dialogtitle" width="1100px" :visible.sync="dialogEditVisible" top="5vh" class="" :append-to-body="false" :close-on-click-modal="true"  >
      <!-- <addEditor :closeDiadl="closeDiadl" ref="addEditor"></addEditor> -->
      <!-- 通告---strat-- -->
      <div class="notcont" v-loading="btnloading">
        <div class="nottop">
          <div class="nottoplef"></div>
          <img  class="logimg" :src="imlog" />
        </div>
        <div class="notcent">
          <p class="notcenth6" v-text="noticeobj.noticeTitle"></p>
          <div class="ql-editor editercont" v-html="noticeobj.noticeInfo">
          </div>
        </div>
        <div class="martop10">
          <uplodFile ref="uploadFiles" :rowtyp='rowtyp' :fileOrderNu='fileOrderNum'></uplodFile>
        </div>
        <div class="notbom">
          <span>该文档最后由:</span>
          <span class="notbomsp2">{{noticeobj.createBy}} </span>
          <span>更新于：{{noticeobj.createTime}}</span>
        </div>
      </div>
      <!-- 通告---end---- -->
    </el-dialog>

  </div>
</div>
</template>

<script>

import qs from "qs";
import {
  selNoticePage,//查询获取数据
  selNoticeById,//查看
  postnoticelist,
} from 'api/admin/user/index';
import imlog from "@/assets/login/logotop.png";
import uplodFile from './uplodfile.vue'
export default {
   components: {
    "addEditor": () => import("./addquillEditor"),
    uplodFile,
  },
  data () {
    return {
      formDatalist: {
        noticeInfo:'',
        titleName:'',
        DATE:[],
      },
      pageIndex: 1,
      pageSize: 10,
      totalCount: 0,
      tablelist:{
         pageIndex: 1,
         pageSize: 10,
         totalCount: 0,
      },
      tableData: [],
      columnDatas: [
          {'field': 'notice_title', title: '公告标题','columnWidth':'150'},
          // {'field': 'notice_title', title: '公告内容','columnWidth':'150'},
          {'field': 'name', title: '发布人','columnWidth':'100'},
          {'field': 'create_time', title: '发布时间','columnWidth':'180'},
      ],
      btnloading:false,
      tableheight:390,
      lodin:false,
      dialogEditVisible:false,
      dialogtitle:'公告',
      noticeobj:{
        'createBy':null,
        'createTime':null,
        'noticeInfo':null,//公告内容
        'noticeTitle':'',//公告标题
      },
      'imlog':imlog,
      listarr:[],
      fileOrderNum:'',
      rowtyp:'',
    }
  },
  created(){
    let bodyheight = document.documentElement.clientHeight;
    this.tableheight =  parseInt(bodyheight)>800?757:577;
    this.tablelist.pageSize = parseInt(bodyheight)>800?20:15;
    this.pageSize = this.tablelist.pageSize;

  },
  mounted () {
   // this.queryDateInit()
    //this.queryData();
    this.postnoticelistdata();
  },
  computed: {
    
  },
  methods: {
    getdatelist(lastMonthToday){
       let that = this;
    
      let lastMonthYear = lastMonthToday.getFullYear();
      let lastMonth = lastMonthToday.getMonth() + 1 < 10 ? "0" + (lastMonthToday.getMonth() + 1) : lastMonthToday.getMonth() + 1;
      let lastMonthDay =lastMonthToday.getDate < 10 ? "0" + lastMonthToday.getDate : lastMonthToday.getDate();
      let lastMonthKsrq = lastMonthYear + "-" + lastMonth + "-" + lastMonthDay;
       return lastMonthKsrq
    },
    handletovivew(rows){//查看
     let that = this;
      that.dialogtitle = '公告'
      that.dialogEditVisible = true;
      that.noticeobj.createBy = null;
      that.noticeobj.createTime = null;
      that.noticeobj.noticeInfo = null;//公告内容
      that.noticeobj.noticeTitle = '';//公告标题
      that.fileOrderNum = rows.id;
      that.rowtyp = 'toview';
      //获取数据
      that.toviewlist(rows.id);
       that.$nextTick(()=>{
      that.$refs.uploadFiles.getFilesTabeData();
      });

    },
   
    // 分页pagesize
    handleSizeChange (val) {
      this.pageIndex = 1;
      this.pageSize = val;
      this.queryData()
    },
    // 分页pageindex
    handleCurrentChange (val) {
      this.pageIndex = val;
      this.queryData()
    },
    // 查询数据
    queryData () {
      let that = this;
      let lastMonthToday = new Date(
        new Date().getTime() - 30 * 24 * 60 * 60 * 1000
      );
      let tadat = new Date(
        new Date().getTime()
      );
     // console.log(that.formDatalist.DATE);
      let datelist = that.formDatalist.DATE;
      let params =  {
        'page': this.pageIndex,
        'limit': this.pageSize,
        'startTime':that.getdatelist(tadat),//查询开始时间，
        'endTime':that.getdatelist(lastMonthToday),//查询截止时间
       // user_id:that.formDatalist.userName,
      }
     // return;
      that.lodin = true;
     selNoticePage(params).then((res) => {
         // console.log(res)
         if(res.status==200){
           let dat = res.data
            this.totalCount = dat.total;
          this.tableData = dat.rows;

         }
       
         that.lodin = false;
      }).catch((err) => {
       
        that.lodin = false;
      })
    },
     toviewlist(id){
      let that = this;
      let pasmt = {
           'id':id
      }
      that.btnloading = true;
      selNoticeById(pasmt).then((data)=>{
       // console.log(data)
          // that.listQuery.titleName = data.noticeTitle;
          // that.content = data.noticeInfo;
      that.noticeobj.createBy = data.createBy;
      that.noticeobj.createTime = data.createTime;
      that.noticeobj.noticeInfo = data.noticeInfo;//公告内容
      that.noticeobj.noticeTitle = data.noticeTitle;//公告标题
      that.btnloading = false;
     }).catch((error)=>{
       that.btnloading = false;

     })

    },
    postnoticelistdata(){
      let that = this;
      that.listarr = [];
      postnoticelist().then( res =>{
        let list = res;
        let liarr = [];
        //list = {'a':1,'b':'2'};
        let obj = {}
        for(var ob in list){
          let aa = ob;
           obj = {
             tit:ob,
             lisarr:list[ob],
           };
           liarr.push(obj)
        }
        that.listarr = liarr;
      //  console.log(that.listarr)

      }).catch(err =>{

      })

    },
   
   
  }
}
</script>

<style lang='scss' scoped>
.notcont{
  min-height:200px;
  max-height:570px;
  overflow: auto;
  .nottop{
    height:30px;
    position: relative;
    .nottoplef{
     position: absolute;
      border: dashed 1px #ccc;
      top: 25px;
      right:90px;
      left:0px;
    }
    .logimg{
      float:right;
      width:auto;
      height: 30px;
    }
  }
  .notcent{
    min-height:150px;
    margin-top: 5px;
  }
  .notcenth6{
    font-size:20px;
    font-weight: 550;
    text-align: center;
    padding:5px 0px;
    line-height: 1.4;
    margin:0px;
  }
  .notbom{
    font-size:13px;
    font-weight: 550;
    text-align: right;
    line-height: 30px;
    
    .notbomsp2{
      display: inline-block;
      padding:0px 8px;
    }
  }
}
.editercont{
  padding:0px;
}
.bgse{
  height:100%;
  background:url("../../../assets/dashse/bgse.png") no-repeat;
  background-size: 100% 100%;
  overflow:auto;
}
.positcont{
    position: absolute;
    top: 0px;
    bottom: 0px;
    left: 0px;
    right: 0px;
}
.noticont{
  padding:10px 20px 0px 20px;
  .noticodv{
    margin-top:15px;
    height:260px;
    overflow-y:auto;
    border:solid 3px #fff;
    border-radius: 15px;
    background-image: linear-gradient(to right, #f5f7fb , #dfe9f4);
    box-sizing: content-box;
  }
  .noticonh6{
    color:#027aff;
    font-size:16px;
    font-weight: 550;
    padding-left:15px;
    margin:10px 0px;
  }
  .noticodvdv{
    font-size:15px;
    padding-left:15px;
    height:220px;
    overflow-y:auto;
  }
  .noticodvdvcon{
    cursor: pointer;
  }
  .noticodvh6{
    display:inline-block;
    max-width: 90%;
    color:#414e5d;
    font-size:17px;
    font-weight: 550;
    margin:0px;
    padding:10px 8px 5px 0px;
    overflow:hidden;
    text-overflow:ellipsis;
    white-space:nowrap;
    
  }
  .noticodvp{
    margin:0px;
    .noticodvsp1{
      font-size:14px;
      color:#344251;

    }
    .noticodvsp2{
      display: inline-block;
      margin-left:10px;
      font-size:14px;
      color:#717477;

    }
  }

}
</style>
<style lang="scss" >
@import "src/styles/common.scss";
.noticont{
  padding:10px 20px;
  .el-row{
    margin:0px!important;
  }

}
</style>