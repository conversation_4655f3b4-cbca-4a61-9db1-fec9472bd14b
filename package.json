{"name": "portal", "version": "0.1.0", "private": true, "scripts": {"serve": "vue-cli-service serve --mode dev", "build": "vue-cli-service build --mode prod", "build:prod": "vue-cli-service build --mode prod", "build:bjprod": "vue-cli-service build --mode bjprod", "lint": "vue-cli-service lint"}, "dependencies": {"@smallwei/avue": "^2.8.20", "axios": "^0.21.1", "core-js": "^3.6.5", "crypto-js": "^4.0.0", "dayjs": "^1.11.11", "echarts": "^5.1.2", "element-ui": "2.15.6", "esri-loader": "^3.1.0", "file-saver": "^2.0.5", "html2canvas": "^1.4.1", "js-cookie": "^2.2.1", "lodash": "^4.17.21", "mavon-editor": "^2.10.4", "moment": "^2.29.1", "node-sass": "^9.0.0", "nprogress": "^0.2.0", "sass-loader": "^7.0.0", "vue": "^2.6.11", "vue-clipboard2": "^0.3.3", "vue-codemirror": "^4.0.6", "vue-cropper": "^0.5.8", "vue-draggable-resizable": "^2.3.0", "vue-grid-layout": "^2.3.12", "vue-loader": "^15.9.6", "vue-quill-editor": "^3.0.6", "vue-router": "^3.2.0", "vuedraggable": "^2.24.3", "vuex": "^3.4.0", "vuex-persistedstate": "^4.0.0-beta.3", "wangeditor": "^4.7.0", "xlsx": "^0.18.5", "xss": "^1.0.10"}, "devDependencies": {"@vue/cli-plugin-babel": "~4.5.0", "@vue/cli-plugin-eslint": "~4.5.0", "@vue/cli-plugin-router": "~4.5.0", "@vue/cli-plugin-vuex": "~4.5.0", "@vue/cli-service": "~4.5.0", "@vue/eslint-config-standard": "^5.1.2", "babel-eslint": "^10.1.0", "babel-plugin-transform-remove-console": "^6.9.4", "element-theme-chalk": "^2.15.1", "eslint": "^6.7.2", "eslint-plugin-import": "^2.20.2", "eslint-plugin-node": "^11.1.0", "eslint-plugin-promise": "^4.2.1", "eslint-plugin-standard": "^4.0.0", "eslint-plugin-vue": "^6.2.2", "gulp": "^4.0.2", "gulp-clean-css": "^4.3.0", "gulp-css-wrap": "^0.1.2", "sass": "^1.83.4", "vue-awesome-swiper": "^3.1.3", "vue-template-compiler": "^2.6.11"}}