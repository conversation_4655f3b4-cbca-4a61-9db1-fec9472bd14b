/**
 * @file 本文件为接口地址文件，页面使用时可直接引入对应接口
 */
import http from '@/api/http'

// 登陆 
// export const doLogin = (params) => {
//     return http.post("/api/rest/api/auth/jwt/token", params);
// }

// 资源结构化树展示
export const monitorDevice = (params) => {
    return http.post("/api/rest/monitor/device", params);
}

// 模糊查询资源结构
export const siteInfo = (params) => {
    return http.post("/api/rest/monitor/device/site/info", params);
}

// 采集监控量
export const deviceInfo = (params) => {
    return http.post("/api/rest/monitor/device/info", params);
}
// 查询监控量
export const deviceInfoList = (params) => {
    return http.post("/api/rest/monitor/device/info/list", params);
}
// 历史性能数据
export const devicePmHistory = (params) => {
    return http.post("/api/rest/monitor/device/pm/history", params);
}
// 操作记录查询
export const operateHistory = (params) => {
    return http.post("/api/rest/monitor/device/operate/history", params);
}

// 查询流程列表 /flowConfig/flow/query
export const queryFlowList= (params) => {
    return http.post("/api/rest/flowConfig/flow/query", params);
    // return http.post("/flowConfig/flow/query", params);
}

// 根据id查询流程
export const queryFlowById = (params) => {
    return http.post("/api/rest/flowConfig/flow/queryById", params);
    // return http.post("/flowConfig/flow/queryById", params);
}
// 删除流程 
export const removeFlow = (params) => {
    return http.post("/api/rest/flowConfig/flow/remove", params);
}

// 最新流程查询
export const queryFlow = (params) => {
    return http.post("/api/rest/flowConfig/flow/query/version", params);
}

// 保存流程
export const addFlow = (params) => {
    // return http.post("/api/rest/flowConfig/node/config", params);
    return http.post("/api/rest/flowConfig/node/config", params);
}



// 查询人员配置列表 
export const queryNodeConfig = (params) => {
    return http.post("/api/rest/flowConfig/nodeConfig/query", params);
}

// 保存人员配置
export const saveNodeConfig = (params) => {
    return http.post("/api/rest/flowConfig/nodeConfig/config", params);
}

// 资源起单接口
export const orderCreate = (params) => {
    return http.post("/api/rest/orderHandle/order/create", params);
}

// 查询配置日志 /orderHandle/device/query
export const configHistory = (params) => {
    return http.post("/api/rest/orderHandle/device/query", params);
}

// 待办与已办工单查询  
export const queryOrderInfo = (params) => {
    return http.post("/api/rest/orderHandle/orderInfo/list", params);
}

// 工单详情 
export const queryOrderDetail = (params) => {
    return http.post("/api/rest/orderHandle/orderInfo/view", params);
}

// 提交订单 /orderHandle/orderInfo/view
export const orderSubmit = (params) => {
    return http.post("/api/rest/orderHandle/order/submit", params);
}


// 设备开关机操作
export const handelDeviceSwitch = (params) => {
    return http.post("/api/rest/monitor/device/switch", params);
}

// 告警列表 /monitor/alarm
export const queryAlarmList = (params) => {
    return http.post("/api/rest/monitor/device/alarm", params);
}

// 资源废弃接口 
export const orderRevoke = (params) => {
    return http.post("/api/rest/orderHandle/order/revoke", params);
}

// 告警级别审批通过后提交 
export const submitAramLevel = (params) => {
    return http.post("/api/rest/monitor/device/alarmLevel", params);
}

// 设备阈值审批通过后提交 
export const submitAramParameter = (params) => {
    return http.post("/api/rest/monitor/device/parameter", params);
}


// 设备运行一览表
export const deviceChar = (params) => {
    return http.post("/api/rest/stat/device/char", params);
}

// 设备统计
export const deviceQuery = (params) => {
    return http.post("/api/rest/stat/device/query", params);
}

// 告警统计
export const deviceAlarm = (params) => {
    return http.post("/api/rest/stat/device/alarm", params);
}

// 性能统计
export const devicePerformance = (params) => {
    return http.post("/api/rest/stat/device/performance", params);
}
// 表格
export const queryTableData = (params) => {
    return http.post("/api/rest/monitor/device/battery/alarm", params);
}
// 地市
export const queryCityList = (params) => {
    return http.post("/api/rest/queryCityList", params);
}

// 区县
export const queryAreaNoCityList = (params) => {
    return http.post("/api/rest/queryAreaNoCityList", params);
}

// 局站
export const querySiteInfo = (params) => {
    return http.post("/api/rest/b_resource/queryCascadeSiteList", params);
}
// 局站测试
export const querySiteInfoTest = (params) => {
  return http.post("/api/rest/b_resource/queryCascadeSiteListV1", params);
}
// 机房
export const queryRoomInfo = (params) => {
    return http.post("/api/rest/b_resource/queryCascadeRoomListV1", params);
}


// 设备类型
export const queryDeviceType = (params) => {
    // return http.post("/api/rest/b_resource/queryBDeviceType", params);
    return http.post("/api/rest/monitor/device/list/type", params);
}

// // 批量列表 设备类型 /monitor/device/list/type
// export const newqueryDeviceType = (params) => {
//     return http.post("/api/rest/monitor/device/list/type", params);
// }

// 告警明细 /stat/alarm/detail/
export const alarmDetail = (params) => {
    return http.post("/api/rest/stat/alarm/detail", params);
}

// 设备一览表  设备名称查询条件下拉数据
export const deviceNameQuery  = (params) => {
    return http.post("/api/rest/monitor/device/list/name", params);
}

// 性能 设备名称查询条件下拉数据 /stat/performance/deviceName/query
export const performanceDevNameQuery  = (params) => {
    return http.post("/api/rest/stat/performance/deviceName/query", params);
}

// 告警 设备名称查询条件下拉数据 
export const alarmDevNameQuery  = (params) => {
    return http.post("/api/rest/stat/alarm/deviceName/query", params);
}

// 告警导出 
export const alarmExport  = (params) => {
    return http.post("/api/rest/stat/alarm/export", params, {
        responseType: 'blob',
        timeout: 1000*60*50
    });
}

// 告警导出 
export const alarmdetailExport  = (params) => {
    return http.post("/api/rest/stat/alarm/detail/export", params, {
        responseType: 'blob',
        timeout: 1000*60*50
    });
}


// 获取区域多选 
export const getBatchArea  = (params) => {
    return http.post("/api/rest/monitor/device/batch/area", params);
}

// 获取局站多选 
export const getBatchSite  = (params) => {
    return http.post("/api/rest/monitor/device/batch/site", params);
}

// 获取机房多选 
export const getBatchRoom  = (params) => {
    return http.post("/api/rest/monitor/device/batch/room", params);
}

// 获取设备类型
export const getBatchType  = (params) => {
    return http.post("/api/rest/monitor/device/batch/type", params);
}

// 获取设备名称多选 
export const getBatchName  = (params) => {
    return http.post("/api/rest/monitor/device/batch/name", params);
}

// 获取测点名称多选 
export const getBatchSignal  = (params) => {
    return http.post("/api/rest/monitor/device/batch/signal", params);
}

// 批量阈值配置列表查询
export const getthresholdlist  = (params) => {
    return http.post("/api/rest/monitor/device/batch/threshold/list", params);
}

// 批量告警配置列表查询
export const getalarmlist  = (params) => {
    return http.post("/api/rest/monitor/device/batch/alarm/list", params);
}


// 告警统计 地市查询
export const getAlarmCitylist  = (params) => {
    return http.post("/api/rest/ckParam/alarm/city/query", params);
}
// 告警统计 区域查询
export const getAlarmArealist  = (params) => {
    return http.post("/api/rest/ckParam/alarm/area/query", params);
}

// 设备监控 手工清除
export const clearAlarm  = (params) => {
    return http.post("/api/rest/monitor/alarm/clear", params);
}
// 告警统计 局站查询
export const getAlarmSitelist  = (params) => {
    return http.post("/api/rest/ckParam/alarm/site/query", params);
}
// 告警统计 机房查询
export const getAlarmRoomlist  = (params) => {
    return http.post("/api/rest/ckParam/alarm/room/query", params);
}
// 告警统计 告警标题
export const getAlarmColumnNamelist  = (params) => {
    return http.post("/api/rest/ckParam/alarm/columnName/query", params);
}

// 性能统计 地市查询
export const getSignalValuesCitylist  = (params) => {
    return http.post("/api/rest/ckParam/signalValues/city/query", params);
}
// 性能统计 区域查询
export const getsignalValuesArealist  = (params) => {
    return http.post("/api/rest/ckParam/signalValues/area/query", params);
}
// 性能统计 局站查询
export const getSignalValuesSitelist  = (params) => {
    return http.post("/api/rest/ckParam/signalValues/site/query", params);
}
// 性能统计 机房查询
export const getSignalValuesRoomlist  = (params) => {
    return http.post("/api/rest/ckParam/signalValues/room/query", params);
}
// 性能统计 告警标题
export const getSignalValuesColumnNamelist  = (params) => {
    return http.post("/api/rest/ckParam/performance/columnName/query", params);
}

// fsu 基本信息
export const getFsuInfo  = (params) => {
    return http.post("/api/rest/monitor/device/fsu/info", params);
}

// fsu重启
export const fsuReset  = (params) => {
    return http.post("/api/rest/monitor/device/fsu/reset", params);
}
// fus操作历史
export const getFsuResetHostory  = (params) => {
    return http.post("/api/rest/monitor/device/fsu/reset/hostory", params);
}


// 批量配置 设备列表查询 
export const getBatchDeviceList  = (params) => {
    return http.post("/api/rest/monitor/device/batch/list", params);
}


// 批量配置 测点列表查询 
export const getBatchSignalList  = (params) => {
    return http.post("/api/rest/monitor/device/signal/list", params);
}

// 批量重启/device/fsu/reset/batch
export const fsuResetBatch  = (params) => {
    return http.post("/api/rest/monitor/device/fsu/reset/batch", params);
}


// 中资资源核查 
export const checkSum  = (params) => {
    return http.post("/api/rest/stat/oss/res/check/sum", params);
}
// FSU资源核查

export const checkFsu = (params) => {
    return http.post("/api/rest/register/getQutFsuList", params);
}

// 设备资源核查
export const checkDevice = (params) => {
    return http.post("/api/rest/register/getQutDeviceList", params);
}
export const checkDetail = (params) => {
    return http.post("/api/rest/stat/oss/res/check/detail", params);
}

// 活动告警 
export const activeAlarm = (params) => {
    return http.post("/api/rest/monitor/device/active/alarm", params);
}

// 历史告警 
export const historyAlarm = (params) => {
    return http.post("/api/rest/monitor/device/history/alarm", params);
}

// 增量活动告警 
export const addAlarm = (params) => {
    return http.post("/api/rest/monitor/device/inc/alarm", params);
}

// 未注册资源列表 
export const getDeviceList = (params) => {
    return http.post("/api/rest/register/getDeviceList", params);
}

// FSU核查-状态修改接口  /fsu/updateFsuStsBatch
export const updateFsuSts = (params) => {
    return http.post("/api/rest/fsu/updateFsuSts", params);
}
export const updateFsuStsBatch = (params) => {
    return http.post("/api/rest/fsu/updateFsuStsBatch", params);
}
//设备核查-状态修改接口 
export const updateDeviceSts = (params) => {
    return http.post("/api/rest/device/updateDeviceSts", params);
}

export const updateDeviceStsBatch = (params) => {
    return http.post("/api/rest/device/updateDeviceStsBatch", params);
}
// 工单详情-绑定资源查询接口
export const orderInfoResource = (params) => {
    return http.post("/api/rest/orderHandle/orderInfo/view/resource", params);
}

// C库设备的设备类型查询 /query/deviceTypeByExp
export const deviceTypeByExp = (params) => {
    return http.post("/api/rest/query/deviceTypeByExp", params);
}
// C库设备的设备名称查询 /query/deviceNameByExp
export const deviceNameByExp = (params) => {
    return http.post("/api/rest/query/deviceNameByExp", params);
}