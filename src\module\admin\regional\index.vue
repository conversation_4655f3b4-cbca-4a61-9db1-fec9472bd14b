<template>
  <div class="app-container calendar-list-container disposefiles  usercontainer">
    <div class="filter-container">
      <div id="sizeForms" class="search-box" ref="sizeForms">
        <el-form :model="listQuery" size="small" ref="sizeForm" inline label-position="right" label-width="90px"
          label-suffix=":" class="demo-form-inline">
          <el-form-item label="区域名称">
            <el-input clearable class="filter-item" placeholder="区域名称" v-model.trim="listQuery.regname"> </el-input>
          </el-form-item>
          <el-form-item class="formbtn text-left">
            <el-button type="primary" v-waves icon="search" @click="handleFilter">查询</el-button>
            <el-button v-waves @click="resetTemp">重置</el-button>

          </el-form-item>

        </el-form>
      </div>
      <!--表格操作----------------------------------------strat-->
      <!-- <div style="margin-top:10px;"> -->
      <!-- <el-row>
        <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
          <el-button @click="handleCreate"  v-waves size="mini" type="primary"  icon="el-icon-plus">新增</el-button>
          <el-button v-waves size="mini"  icon="el-icon-edit" type="primary" @click="handleModify">修改</el-button> -->
      <!-- <el-button v-waves size="mini"  icon="el-icon-view" type="primary" @click="handleToviwe">查看</el-button> -->
      <!-- <el-button v-waves size="mini" icon="el-icon-delete" type="primary" @click="handleDelete">删除</el-button>
        </el-col>
      </el-row> -->
      <!-- </div> -->
      <!--表格----------------------------strat-->
      <div class="listTables martop10">
        <el-table ref="singleTable" :data="list" v-loading="listLoading" :height="tableheight" highlight-current-row
          row-key="city_code" :default-expand-all="false" style="width: 100%"
          :tree-props="{ children: 'children', hasChildren: 'hasChildren' }">
          <el-table-column type="selection" width="80">
          </el-table-column>
          <el-table-column v-for="(row, index) in columnData" align="left" :key="index" :prop="row.field"
            :label="row.title" :min-width="row.widt" show-overflow-tooltip></el-table-column>

        </el-table>
      </div>
      <!-- <div  class="pagination-container pagination cleafix tablPaginat">
    <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page.sync="listQuery.page" :page-sizes="[10,15,20,30, 50]" :page-size="listQuery.limit" layout="total, sizes, prev, pager, next, jumper" :total="total"> </el-pagination>
  </div> -->
      <el-dialog width="600px" :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible"
        :close-on-click-modal="false">
        <el-row class="userrows">
          <el-form :rules="rules" label-width="90px">
            <el-col :span="24">
              <el-form-item label="区域名称" prop="name">
                <el-input class="filter-item" v-model="listQuery.regname1" placeholder="请输入区域名称"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="区域ID" prop="username">
                <el-input class="filter-item" v-model="listQuery.regId" placeholder="请输入区域ID"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="区域级别">
                <el-select class="filter-item" v-model="listQuery.regType1" placeholder="区域级别">
                  <el-option label="省" value="1"></el-option>
                  <el-option label="市" value="2"></el-option>
                  <el-option label="区" value="3"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item label="描述">
                <el-input class="filter-item" type="textarea" :autosize="{ minRows: 3, maxRows: 5 }" placeholder="请输入内容"
                  v-model="listQuery.description"> </el-input>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancel()">取 消</el-button>
          <el-button type="primary" @click="create()">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
import {
  getAreaAll
} from '@/api/admin/user/index';
import { mapGetters } from 'vuex';

export default {
  name: 'user',
  data() {
    return {

      listQuery: {
        regname: null,//区域名称
        regType: null,//区域级别
        description: null,//描述
        regType1: null,
        regname1: null,//区域名称
        regId: null,//区域ID
        page: 1,
        // limit: 10,
        username: undefined
      },
      rules: {
        name: [
          {
            required: true,
            message: '请输入用户',
            trigger: 'blur'
          },
          {
            min: 1,
            max: 20,
            message: '长度在 1 到 20 个字符',
            trigger: 'blur'
          }
        ],
      },
      list: [],
      total: null,
      listLoading: false,
      listQuery: {

      },
      sexOptions: ['男', '女'],
      dialogFormVisible: false,
      dialogStatus: '',
      userManager_btn_edit: false,
      userManager_btn_del: false,
      userManager_btn_add: false,
      textMap: {
        update: '修改',
        create: '新增'
      },
      tableKey: 0,
      citieCounty: [],//地市
      city_id: 750,
      countydata: [],//区县
      buMdata: [],//部门
      tableheight: 400,
      pageSize: 10,
      allData: [],
      columnData: [
        { field: "city_name", title: "区域名称", widt: "150" },
        { field: "city_code", title: "区域ID", widt: "100" },
        { field: "regType", title: "区域级别", widt: "150" },
        // { field: "BUSSINESS_CODE", title: "描述", widt: "240" },
      ]

    }
  },
  created() {
    let bodyheight = document.documentElement.clientHeight;
    this.tableheight = parseInt(bodyheight) > 800 ? 580 : 400;
    // this.listQuery.limit = parseInt(bodyheight)>800?15:10;
    this.pageSize = this.listQuery.limit;
    //this.getList();



  },
  mounted() {
    this.getList();

  },
  computed: {
    ...mapGetters([
      'elements'
    ])
  },
  methods: {
    getList() {
      this.listLoading = true;
      let pams = {
        regname: this.listQuery.regname,
      }
      getAreaAll().then(response => {
        this.list = response;
        this.allData = JSON.parse(JSON.stringify(response))
        // this.total = response.length;
        this.listLoading = false;
      })
    },
    handleFilter() {
      if (this.listQuery.regname) {
        this.list = this.rebuildData(this.listQuery.regname.trim(), this.allData)
      } else {
        this.getList();
      }
    },
    // 重点代码 根据name字段模糊匹配树状结构数据，最后将处理好的数据返回出来
    rebuildData(value, arr) {
      if (!arr) {
        return []
      }
      const newarr = [];
      arr.forEach(element => {
        // indexOf用来判读当前节点name字段是否包含所搜索的字符串value
        // 返回值：包含则返回索引值，反之返回-1
        if (element.city_name.indexOf(value) > -1) {
          // const ab = this.rebuildData(value, element.children);
          // const obj = {
          //   ...element,
          //   children: ab
          // }
          newarr.push(element);
        } else {
          // 判断当前节点知否有子节点，并且子节点中有数据，有数据继续递归查找
          if (element.children && element.children.length > 0) {
            const ab = this.rebuildData(value, element.children);
            const obj = {
              ...element,
              children: ab
            };
            if (ab && ab.length > 0) {
              newarr.push(obj);
            }
          }
        }
      });
      return newarr
    },
    handleSizeChange(val) {
      this.listQuery.limit = val;
      this.getList();
    },
    handleCurrentChange(val) {
      this.listQuery.page = val;
      this.getList();
    },
    handleCreate() {//新增
      let that = this;
      let arrlist = this.$refs.singleTable.selection;
      that.dialogStatus = 'create';
      that.dialogFormVisible = true;


    },
    handleModify(row) {//修改
      let that = this;
      let arrlist = this.$refs.singleTable.selection;
      if (arrlist.length != '1') {
        that.$message({
          showClose: true,
          message: '请选择一条进行修改',
          type: "warning",
          duration: 1500
        });
        return;
      }
      that.dialogStatus = 'update';
      that.dialogFormVisible = true;

    },
    handleDelete(row) {
      let that = this;
      let arrlist = this.$refs.singleTable.selection;
      // console.log(arrlist)
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {

      });
    },
    create(formName) {
      let that = this;
      const set = this.$refs;
      set[formName].validate(valid => {
        if (valid) {

        } else {
          return false;
        }
      });
    },
    cancel(formName) {

      this.$refs[formName].resetFields();
      this.dialogFormVisible = false;

    },
    update(formName) {
      const set = this.$refs;
      let that = this;

    },
    resetTemp() {
      let that = this;
      that.listQuery.regname = null;
      that.listQuery.regType = null;
      that.list = [];
      this.getList();

    },
  }
}
</script>

<style scoped lang="scss"></style>
<style lang="scss">
// @import "src/styles/common.scss";
.usercontainer {
  .userrows {
    .filter-item {
      width: 100%;
      margin-bottom: 0px;
    }
  }

  .formbtn {
    .el-form-item__content {
      margin-left: 0px !important;
    }
  }
}

//  .search-box{
//   .el-form-item{
//     margin-bottom:0px;
//   }
// }</style>
