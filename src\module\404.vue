<template>
  <div class="not-found">
    <img src="~@/assets/images/404.png" alt="404" class="pic" />
    <div class="content">
      <h1 class="c-title">404</h1>
      <p class="c-text">抱歉，您访问的页面不存在</p>
      <!-- <el-button type="primary" @click="backHome">返回首页</el-button> -->
    </div>
  </div>
</template>

<script>
export default {
  name: 'NotFoundPage',
  methods: {
    backHome () {
      if (window.self === window.top) {
        this.$router.push('/home')
      } else {
        const data = JSON.stringify({ token: '#gotoHome#' })
        window.top.postMessage(data, '*')
      }

    }
  }
}
</script>
<style lang="scss" scoped>
.not-found {
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  .pic {
    margin-right: 40px;
    width: 500px;
    height: 500px;
  }
  .content {
    .c-title {
      font-size: 80px;
      font-family: ArialMT;
    }
    .c-text {
      color: #7b93a7;
      margin-bottom: 40px;
      font-size: 14px;
    }
  }
}
</style>


