<template>
  <div class="app-container">
    <el-form :inline="true" :model="queryForm" size="small" label-width="100px" class="demo-form-inline">
      <el-form-item label="地市:">
        <el-select v-model="queryForm.cityId" :disabled="cityDisabled" @change="cityChange" filterable clearable
          placeholder="请选择">
          <el-option v-for="(item, index) in citylist" v-show="index !== 0" :key="item.cityId" :label="item.cityName"
            :value="item.cityId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="区域:">
        <el-select v-model="queryForm.areaId" :disabled="areaDisabled" @change="areaChange" filterable clearable
          placeholder="请选择">
          <el-option v-for="item in areaList" :key="item.areaId" :label="item.areaName"
            :value="item.areaId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="局站:">
        <el-select v-model="queryForm.siteId" @change="siteChange" :remote-method="getSite" filterable clearable
          placeholder="请选择" @click="getSite">
          <el-option v-for="item in siteList" filterable :key="`${item.area_id}-${item.site_id}`"
            :label="item.site_name" :value="item.site_id"></el-option>
        </el-select>
      </el-form-item>
      <!-- <el-form-item label="test:">

        <el-button @click="getSiteTest">click</el-button>
      </el-form-item> -->
      <el-form-item label="机房:">
        <el-select v-model="queryForm.roomId" @change="roomChange" :remote-method="getRoom" filterable clearable
          placeholder="请选择">
          <el-option v-for="item in roomlist" :key="`${item.room_id}-${item.site_id}`" :label="item.room_name"
            :value="item.room_id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备类型:">
        <el-select v-model="queryForm.deviceType" @change="devTypeChange" :remote-method="getDeviceType" filterable
          remote clearable placeholder="请输入">
          <el-option v-for="(item, index) in deviceTypeList" :key="`${index}-${item.id}`" :label="item.label"
            :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备名称:">
        <el-select v-model="queryForm.deviceName" filterable remote clearable placeholder="请输入"
          :remote-method="getDeviceName">
          <el-option v-for="(item, index) in devoptions" :key="`${index}-${item.id}-${item.label}`" :label="item.label"
            :value="item.label">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="SU_ID:">
        <el-input v-model="queryForm.suId" placeholder="请输入" clearable></el-input>
      </el-form-item>
      <el-form-item label="SU_IP:">
        <el-input v-model="queryForm.suIp" placeholder="请输入" clearable></el-input>
      </el-form-item>
      <el-form-item label="工单编码:">
        <el-input v-model="queryForm.orderId" placeholder="请输入" clearable></el-input>
      </el-form-item>
      <el-form-item label="是否异常数据:">
        <el-select v-model="queryForm.state" filterable remote clearable placeholder="请输入"
          :remote-method="getDeviceName">
          <el-option label="是" value="ABNORMAL">
          </el-option>
          <el-option label="否" value="NORMAL">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="">
        <el-button style="margin-left: 60px;" @click="queryFormReset">重置</el-button>
        <el-button type="primary" @click="queryData(1, 10)">查询</el-button>
        <el-button type="primary" @click="devExport" :loading="exportloading">导出</el-button>
      </el-form-item>
    </el-form>
    <el-table :data="deviceTableData" header-row-class-name="myHeaderClass" size="small" v-loading="tableLoading"
      :row-class-name="tableRowClassName">
      <el-table-column prop="cityName" align="center" label="地市" show-overflow-tooltip></el-table-column>
      <el-table-column prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
      <el-table-column prop="siteName" align="center" label="局站" show-overflow-tooltip></el-table-column>
      <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
      <el-table-column prop="suIp" align="center" label="SU_IP" show-overflow-tooltip></el-table-column>
      <el-table-column prop="suVendor" align="center" label="SU厂家" show-overflow-tooltip>
        <template slot-scope="scope">
          <div v-if="scope.row.suVendor == 'ZNV'">力维</div>
          <div v-if="scope.row.suVendor == 'SAIERCOM'">赛尔</div>
        </template>
      </el-table-column>
      <el-table-column prop="deviceTypeName" align="center" label="设备类型" show-overflow-tooltip></el-table-column>
      <el-table-column prop="deviceName" align="center" label="设备名称" show-overflow-tooltip>

      </el-table-column>
      <el-table-column prop="deviceVender" align="center" label="设备厂家" show-overflow-tooltip>
        <template slot-scope="scope">
          <div v-if="scope.row.deviceVender == 'ZNV'">力维</div>
          <div v-else-if="scope.row.deviceVender == 'SAIERCOM'">赛尔</div>
          <div v-else>{{ scope.row.deviceVender }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="status" align="center" label="状态" show-overflow-tooltip>
        <template slot-scope="scope">
          {{ deviceState.find(v => v.type == scope.row.status)?.name || scope.row.status }}
        </template>
      </el-table-column>
      <el-table-column prop="beginRunTime" align="center" label="注册时间" show-overflow-tooltip></el-table-column>
      <el-table-column prop="monitorId" align="center" label="监控量ID" show-overflow-tooltip></el-table-column>
      <el-table-column prop="signalName" align="center" label="监控量名称" show-overflow-tooltip></el-table-column>
      <el-table-column prop="HLimit" align="center" label="上限" show-overflow-tooltip></el-table-column>
      <el-table-column prop="SHLimit" align="center" label="过高上限" show-overflow-tooltip></el-table-column>
      <el-table-column prop="LLimit" align="center" label="下限" show-overflow-tooltip></el-table-column>
      <el-table-column prop="SLLimit" align="center" label="过低下限" show-overflow-tooltip></el-table-column>
      <!-- <el-table-column prop="SetValue" align="center" label="设置值" show-overflow-tooltip></el-table-column> -->
    </el-table>
    <div class="pag">
      <el-pagination @size-change="handleSizeChange" background @current-change="handleCurrentChange"
        :current-page="currentPage" :page-sizes="[10, 20, 50]" :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </div>

  </div>
</template>

<script>
import exportTableToExcel from './components/exportExcel.js'
export default {
  data() {
    return {
      deviceState: [
        { type: 1, name: '正常' },
        { type: 2, name: '疑似退网' },
        { type: 3, name: '已退网' },
        { type: 4, name: '疑似退网' },
      ],
      // 查询条件下拉数据
      citylist: [],
      areaList: [],
      siteList: [],
      roomlist: [],
      deviceTypeList: [],
      cedianlist: [],
      cedianNameList: [],
      devoptions: [],

      tableLoading: false,
      queryForm: {
        areaId: '',
        cityId: '',
        deviceType: '',
        deviceName: '',
        roomId: '',
        siteId: '',
        suId: '',
        suIp: '',
        normalId: '',
      },
      deviceTableData: [],
      pageSize: 10,
      currentPage: 1,
      total: 0,
      content: '',
      currentRow: null,
      exportloading: false,
      role: '',
      cityDisabled: true,
      areaDisabled: true,
    }
  },
  created() {
    this.role = sessionStorage.getItem('role')
    this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
    this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''

    if (this.role == '1') {
      this.cityDisabled = false
      this.areaDisabled = false
    }

    if (['94', '95'].includes(this.role)) {
      this.cityDisabled = true
      this.areaDisabled = this.queryForm.areaId ? true : false
    }
    this.queryData()

    setTimeout(() => {
      this.queryCityList()
      this.getSite()
      this.getRoom()
      this.getDeviceType()
      this.getDeviceName()
      this.getSiteTest()
    }, 1000)

  },
  methods: {
    async devExport() {
      this.exportloading = true
      let data = [
        ['地市', '区域', '局站', '机房', 'SU_IP', 'SU厂家', '设备类型', '设备名称', '设备厂家', '状态', '注册时间', '监控量ID', '监控量名称', '上限', '过高上限', '下限', '过低下限']
      ]
      let params = {
        ...this.queryForm,
        current: 1,
        size: this.total
      }
      try {
        let res = await this.$api.deviceChar(params)
        if (res.code == 200) {
          res.data.records.forEach(v => {
            // const tempVender = ''
            // const status = ''
            if (v.suVendor == 'ZNV') {
              v.suVendor = '力维'
            } else if (v.suVendor == 'SAIERCOM') {
              v.suVendor = '赛尔'
            }
            if (v.status == 1) {
              v.status = '正常'
            } else if (v.status == 2) {
              v.status = '疑似退网'
            } else if (v.status == 3) {
              v.status = '已退网'
            } else if (v.status == 4) {
              v.status = '疑似退网'
            }
            let item = [
              v.cityName,
              v.areaName,
              v.siteName,
              v.roomName,
              v.suIp,
              // v.orderId,
              // tempVender,
              v.suVendor,
              v.deviceTypeName,
              v.deviceName,
              v.deviceVender,
              v.status,
              v.beginRunTime,
              v.monitorId,
              v.signalName,
              v.HLimit,
              v.SHLimit,
              v.LLimit,
              v.SLLimit,
              // v.SetValue,
            ]
            data.push(item)
          })
          exportTableToExcel(data, '设备运行一览表')
          this.exportloading = false
        } else {
          this.$message.error(res.msg)
          this.exportloading = false
        }
      } catch (error) {
        this.$message.error('导出异常')
        this.exportloading = false
      }

    },
    // 初始化地市
    queryCityList() {
      let params = {
        cityName: "",
        pageNum: 1,
        pageSize: 999
      }
      this.$api.queryCityList(params).then(res => {
        if (res.code == 200) {
          this.citylist = res.data.records
          this.cfgcitylist = res.data.records
          const temp = this.citylist.filter(v => v.cityName !== '陕西省')
          if (this.queryForm.cityId) {
            this.areaList = this.cfgcitylist.find(v => v.cityId == this.queryForm.cityId).areaList
          } else {
            temp.forEach(v => {
              this.areaList.push(...v.areaList)
            })
          }

        }
      })
    },
    cityChange(val) {
      this.areaList = this.citylist.find(v => v.cityId == val).areaList
      this.getSite()
      this.getRoom()
      this.getDeviceName()
      this.getDeviceType()
    },
    areaChange() {
      this.getSite()
      this.getRoom()
      this.getDeviceName()
      this.getDeviceType()
    },
    siteChange() {
      this.getRoom()
      this.getDeviceName()
      this.getDeviceType()
    },
    roomChange() {
      this.getDeviceName()
      this.getDeviceType()
    },
    devTypeChange() {
      this.getDeviceName()
    },

    getSite(val) {
      let params = {
        cityId: this.queryForm.cityId,
        areaId: this.queryForm.areaId,
        siteName: val,
        pageNum: 1,
        pageSize: 999
      }

      this.$api.querySiteInfoTest(params).then(res => {
        if (res.code == 200) {
          this.siteList = res.data
        }
      })
    },
    getSiteTest() {
      this.$api.querySiteInfoTest({
        cityId: this.queryForm.cityId,
        areaId: this.queryForm.areaId,
        siteName: '',
        pageNum: 1,

      }).then(res => {
        if (res.code == 200) {
          this.siteList = res.data
        }
      })
    },
    getRoom(val) {
      let params = {
        cityId: this.queryForm.cityId,
        areaId: this.queryForm.areaId,
        siteId: this.queryForm.siteId,
        roomName: val,
        pageNum: 1,
      }
      this.$api.queryRoomInfo(params).then(res => {
        if (res.code == 200) {
          this.roomlist = res.data
        }
      })
    },
    async getDeviceName(queryString) {
      let res = await this.$api.deviceNameQuery({
        deviceName: queryString,
        cityId: this.queryForm.cityId ? [this.queryForm.cityId] : [],
        areaId: this.queryForm.areaId ? [this.queryForm.areaId] : [],
        siteId: this.queryForm.siteId ? [this.queryForm.siteId] : [],
        roomId: this.queryForm.roomId ? [this.queryForm.roomId] : [],
        deviceType: this.queryForm.deviceType ? [this.queryForm.deviceType] : [],
        current: 1,
        size: 999
      })
      if (res.code == 200) {

        this.devoptions = res.data.records
      } else {
        this.$message.error(res.msg)

      }
    },
    async getDeviceType(val) {
      let res = await this.$api.queryDeviceType({
        deviceType: val,
        cityId: this.queryForm.cityId ? [this.queryForm.cityId] : [],
        areaId: this.queryForm.areaId ? [this.queryForm.areaId] : [],
        siteId: this.queryForm.siteId ? [this.queryForm.siteId] : [],
        roomId: this.queryForm.roomId ? [this.queryForm.roomId] : [],
        current: 1,
        size: 999
      })
      if (res.code == 200) {

        this.deviceTypeList = res.data.records
      } else {
        this.$message.error(res.msg)
      }
    },

    queryFormReset() {
      this.areaList = []
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      this.devoptions = []
      this.queryForm = {
        areaId: '',
        cityId: '',
        deviceType: '',
        deviceName: '',
        roomId: '',
        siteId: '',
        suId: '',
        suIp: '',
        orderId: '',
        state: '',
      }

      // this.queryData()

      this.role = sessionStorage.getItem('role')
      this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
      this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''

      if (this.role == '1') {
        this.cityDisabled = false
        this.areaDisabled = false
      }

      if (['94', '95'].includes(this.role)) {
        this.cityDisabled = true
      }

      setTimeout(() => {
        this.queryCityList()
        this.getSite()
        this.getRoom()
        this.getDeviceType()
        this.getDeviceName()
        this.getSiteTest()
      }, 1000)
      this.queryData()

    },

    // 监控量
    queryData(pageNum, pageSize) {
      this.tableLoading = true
      if (pageNum) this.currentPage = pageNum
      if (pageSize) this.pageSize = pageSize
      let params = {
        ...this.queryForm,
        current: pageNum || this.currentPage,
        size: pageSize || this.pageSize
      }
      this.role = sessionStorage.getItem('role')
      if (sessionStorage.getItem('cityId')) {
        this.queryForm.cityId = sessionStorage.getItem('cityId')
      } else if (sessionStorage.getItem('areaId') && sessionStorage.getItem('cityId')) {
        this.queryForm.cityId = sessionStorage.getItem('cityId')
        this.queryForm.areaId = sessionStorage.getItem('areaId')
      }
      // this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''

      // this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : 
      this.$api.deviceChar(params).then(res => {
        if (res.code == 200) {
          this.deviceTableData = res.data.records
          this.tableLoading = false
          this.total = res.data.total
        } else {
          this.tableLoading = false
          this.$message.error(res.msg)
        }
      })
    },
    tableRowClassName({ row, rowIndex }) {
      if (row.state != 'NORMAL') {
        return 'warning-row';
      }
      return '';
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.queryData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.queryData()
    }
  }
}
</script>

<style lang="scss" scoped>
.ad {
  display: flex;
  gap: 6px;

  i {
    cursor: pointer;
  }
}

.pag {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 12px;
}

.selectWrap {
  width: 600px;
  height: 60px;
  margin-left: 100px;
  overflow: scroll;
  margin-bottom: 16px;
  //box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
  border: 1px solid #d7dae2;
}

:deep(.el-table) .warning-row {
  background: oldlace;
}

// :deep(.el-table) .normal-row {
//   background: white;
// }</style>