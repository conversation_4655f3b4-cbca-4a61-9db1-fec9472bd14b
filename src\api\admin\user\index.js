import http from '@/api/http'

export function page(query) {
  return http({
    url: '/api/admin/user/listToUser',
    method: 'post',
    data: query
  });
}

export function dispatchPage(query) {
  return http({
    url: '/api/admin/dispatch/listToDispatchUser',
    method: 'post',
    data: query
  });
}
export function defaultUserPage(query) {
  return http({
    url: '/api/admin/dispatch/listToDefaultUser',
    method: 'post',
    data: query
  });
}

export function addObj(obj) {
  return http({
    url: '/api/admin/user/add',
    method: 'post',
    data: obj
  });
}
export function addDisPatchObj(obj) {
  return http({
    url: '/api/admin/dispatch/addDisPatch',
    method: 'post',
    data: obj
  });
}
export function addDefaultUserObj(obj) {
  return http({
    url: '/api/admin/dispatch/addDefaultUser',
    method: 'post',
    data: obj
  });
}

export function getObj(id) {
  return http({
    url: '/api/admin/user/' + id,
    method: 'get'
  })
}

export function delObj(obj) {
  // return http({
  //   url: '/api/admin/user/delUser?id='+id,
  //   method: 'get'
  // })
  return http({
    url: '/api/admin/user/delUser',
    method: 'post',
    data: obj

  })
}
export function delDispatchObj(obj) {
  return http({
    url: '/api/admin/dispatch/delDispatchUser',
    method: 'post',
    data: obj

  })
}
export function delDefaultUser(obj) {
  return http({
    url: '/api/admin/dispatch/delDefaultUser',
    method: 'post',
    data: obj

  })
}

export function putObj(id, obj) {
  return http({
    url: '/api/admin/user/' + id,
    method: 'put',
    data: obj
  })
}

/**
 * 修改用户信息
 * @param id
 * @param obj
 */
export function updateUser(obj) {
  return http({
    url: '/api/admin/user/updateUser',
    method: 'post',
    data: obj
  })
}
/**
 * 修改调度信息
 * @param id
 * @param obj
 */
export function updateDispatchUser(obj) {
  return http({
    url: '/api/admin/dispatch/updateDispatchUser',
    method: 'post',
    data: obj
  })
}
/**
 * 修改调度信息
 * @param id
 * @param obj
 */
export function updateDefaultUser(obj) {
  return http({
    url: '/api/admin/dispatch/updateDefaultUser',
    method: 'post',
    data: obj
  })
}

/**
 * 修改密码
 * @param obj
 */
export function updatePassword(obj) {
  return http({
    url: '/api/admin/user/updatePassword',
    method: 'post',
    data: obj
  })
}

/**
 * 重置密码
 * @param obj
 */
export function resetPassword(obj) {
  return http({
    url: '/api/admin/user/resetPassword',
    method: 'post',
    data: obj
  })
}
//个人信息回显
export function getUserInfo(obj) {
  return http({
    url: '/api/admin/user/getUserInfoByUserId',
    method: 'post',
    data: obj
  })
}

//调度信息
export function getDispatchUserInfo(obj) {
  return http({
    url: '/api/admin/dispatch/getDispatchUserInfoById',
    method: 'post',
    data: obj
  })
}

//调度信息
export function getDefaultUserInfo(obj) {
  return http({
    url: '/api/admin/dispatch/getDefaultUserInfoById',
    method: 'post',
    data: obj
  })
}


//获取调度人
export function getDisPatchAreaUser(obj) {
  return http({
    url: '/api/admin/user/getDisPatchAreaUser',
    method: 'post',
    data: obj
  })
}

//个人信息回显
export function getDepartAll(obj) {
  return http({
    url: '/api/admin/depart/getDepartCLAll',
    method: 'get',
    params: obj
  })
}
// 通知发布查询
export function selNoticePage(obj) {
  return http({
    url: '/api/admin/noticeCon/noticeList',
    method: 'post',
    data: obj
  })
}
// 通知新增
export function insertNotice(obj) {
  return http({
    url: '/api/admin/noticeCon/addNotice',
    method: 'post',
    data: obj
  })
}
//通知修改
export function updateNotice(obj) {
  return http({
    url: '/api/admin/noticeCon/updateNotice',
    method: 'post',
    data: obj
  })
}
// 通知查看
export function selNoticeById(obj) {
  return http({
    url: '/api/admin/noticeCon/getNoticeById',
    method: 'get',
    params: obj
  })
}
//通知删除
export function deleteNotice(obj) {
  return http({
    url: '/api/admin/noticeCon/delNotice',
    method: 'get',
    params: obj
  })
}
//地市
export function getComCityList(query) {
  return http({
    url: '/api/admin/areaCon/getCityList',
    method: 'get',
    params: query
  });
}
//获取区县
export function getcounuty(query) {
  return http({
    url: '/api/admin/areaCon/getCityList',
    method: 'get',
    params:query
  });
}
//获取责任区域
export function getDisPatchArea(query) {
  return http({
    url: '/api/admin/areaCon/getDisPatchAreaList',
    method: 'get',
    params:query
  });
}
//api/admin/areaCon/getAreaAll
export function getAreaAll(query) {
  return http({
    url: '/api/admin/areaCon/getAreaAll',
    method: 'get',
    params:query
  });
}
///
export function getnewNotice(query) {
  return http({
    url: '/api/admin/noticeCon/newNotice',
    method: 'post',
    data:query
  });
}
// /api/admin/noticeCon/notice
export function postnoticelist(query) {
  return http({
    url: '/api/admin/noticeCon/notice',
    method: 'post',
    data:query
  });
}
//创立解绑接口 手机IMEI解绑
// export function postFoundedUnbund(obj) {
//   return http({
//     url: '/user/unbind',
//    // url:'http://172.17.12.10:9175/api/user/unbind',
//     method: 'post',
//     data: obj
//   })
// }
//
export function postFoundedUnbund(obj) {
  return http({
    url: '/api/admin/user/getAesEncrypt',
    method: 'post',
    data: obj
  })
}
