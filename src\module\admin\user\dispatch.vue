<template>
  <div class="app-container calendar-list-container disposefiles  usercontainer">
    <div class="filter-container">
      <div id="sizeForms" class="search-box borderBottom paddbom5" ref="sizeForms">
        <el-form :model="listQuery" ref="sizeForm" label-position="right" label-width="80px" label-suffix=":" class="demo-form-inline">
          <el-row :gutter="10">
            <el-col :xs="8" :sm="8" :md="6" :lg="6" :xl="4">
              <el-form-item label="地市">
                <el-select clearable class="filter-item" v-model="listQuery.area" placeholder="请选择" @change="getpostcounuty1">
                  <el-option v-for="(item,inde) in citieCounty" :key="inde" :label="item.VALUE" :value="item.KEYSS"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :xs="8" :sm="8" :md="6" :lg="6" :xl="4">
              <el-form-item label="责任区域">
                <el-select clearable class="filter-item" v-model="listQuery.cityArea" placeholder="请选择" clearable>
                  <el-option v-for="(item,inde) in countydata1" :key="inde" :label="item.VALUE" :value="item.KEYSS"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="24">
              <el-form-item class="formbtn text-right">
                <el-button type="primary" v-waves icon="search" @click="handleFilter">查询</el-button>
                <el-button v-waves @click="btnresetTemp">重置</el-button>
                <el-button v-if="userManager_btn_add" @click="handleCreate" type="primary" icon="edit">添加</el-button>
              </el-form-item>
            </el-col>
          </el-row>
        </el-form>
      </div>
      <div class="listTables martop10">
        <el-table :key='tableKey' :data="list" v-loading.body="listLoading" :height="tableheight" border fit highlight-current-row style="width: 100%">
          <el-table-column align="left" label="序号" width="50" type="index"> </el-table-column>

          <el-table-column width="100" align="left" label="地市"> <template slot-scope="scope">
            <span>{{scope.row.area_name}}</span>
          </template> </el-table-column>
          <el-table-column width="100" align="left" label="责任区域"> <template slot-scope="scope">
            <span>{{scope.row.dispatch_area_name}}</span>
          </template> </el-table-column>
          <el-table-column width="100" align="left" label="省级负责人"> <template slot-scope="scope">
            <span>{{scope.row.province_user_name}}</span>
          </template> </el-table-column>
          <el-table-column width="100" align="left" label="市级负责人"> <template slot-scope="scope">
            <span>{{scope.row.city_user_name}}</span>
          </template> </el-table-column>
          <el-table-column width="100" align="left" label="区域负责人"> <template slot-scope="scope">
            <span>{{scope.row.dispatch_user_name}}</span>
          </template> </el-table-column>
          <el-table-column width="100" align="left" label="操作人"> <template slot-scope="scope">
            <span>{{scope.row.name}}</span>
          </template> </el-table-column>
          <el-table-column min-width="170" align="left" label="创建时间"> <template slot-scope="scope">
            <span>{{scope.row.create_time}}</span>
          </template> </el-table-column>
          <el-table-column min-width="170" align="left" label="更新时间"> <template slot-scope="scope">
            <span>{{scope.row.update_time}}</span>
          </template> </el-table-column>
          <el-table-column align="center" label="操作" fixed="right" width="330"> <template slot-scope="scope">
            <el-button v-if="userManager_btn_edit" size="small" type="success" @click="handleUpdate(scope.row)">编辑
            </el-button>
            <el-button v-if="userManager_btn_del" size="small" type="danger" @click="handleDelete(scope.row)">删除
            </el-button>
          </template> </el-table-column>
        </el-table>
      </div>
      <div v-show="!listLoading" class="pagination-container pagination cleafix tablPaginat">
        <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange" :current-page.sync="listQuery.page" :page-sizes="[10,15,20,30, 50]" :page-size="listQuery.limit" layout="total, sizes, prev, pager, next, jumper" :total="total"> </el-pagination>
      </div>
      <el-dialog :title="textMap[dialogStatus]" :visible.sync="dialogFormVisible" width="1000px" :close-on-click-modal="false">
        <el-row class="userrows">
          <el-form :model="form" :rules="rules" ref="form" label-width="90px">
            <el-col :span="12">
              <el-form-item label="地市" prop='cityCode'>
                <el-select class="filter-item" multiple multiple-limit="1" v-model="form.cityCode" placeholder="请选择" @change="getpostcounuty">
                  <el-option v-for="(item,inde) in citieCounty" :key="inde" :label="item.VALUE" :value="item.KEYSS"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="责任区域" prop='areaCode'>
                <el-select class="filter-item" v-model="form.areaCode"  multiple multiple-limit="1" placeholder="请选择">
                  <el-option v-for="(item,inde) in countydata" :key="inde" :label="item.VALUE" :value="item.KEYSS"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="省级负责人">
                <el-select class="filter-item" v-model="form.provinceUser"  filterable multiple placeholder="请选择">
                  <el-option v-for="(item,inde) in provinceDispatchUser" :key="inde" :label="item.VALUE" :value="item.KEYSS"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="市级负责人">
                <el-select class="filter-item" v-model="form.cityUser"  filterable multiple placeholder="请选择">
                  <el-option v-for="(item,inde) in dispatchUser" :key="inde" :label="item.VALUE" :value="item.KEYSS"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="区域负责人">
                <el-select class="filter-item" v-model="form.areaDispatchUser"  filterable multiple placeholder="请选择">
                  <el-option v-for="(item,inde) in dispatchUser" :key="inde" :label="item.VALUE" :value="item.KEYSS"></el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-form>
        </el-row>
        <div slot="footer" class="dialog-footer">
          <el-button @click="cancel('form')">取 消</el-button>
          <el-button v-if="dialogStatus=='create'" type="primary" @click="create('form')">确 定</el-button>
          <el-button v-else type="primary" @click="update('form')">确 定</el-button>
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>
  import {
    dispatchPage,
    addDisPatchObj,
    delDispatchObj,
    updateDispatchUser,
    resetPassword,
    getComCityList,
    getDisPatchArea,
    getDisPatchAreaUser,
    getDispatchUserInfo,
    postFoundedUnbund,//创立解绑
  } from 'api/admin/user/index';

  import{
    getSysUserId,
  } from 'utils/auth';
  import aes from "@/utils/aes";
  import {downloadfilter} from 'api/report/index'
  import {
    mapGetters
  } from 'vuex';
  export default {
    name: 'dispatch',
    data() {
      return {
        form: {
          id: undefined,
          cityCode: undefined,
          areaCode: [],
          provinceUser: [],
          cityUser: [],
          areaDispatchUser: [],
          provinceUserName: [],
          cityUserName: [],
          areaDispatchUserName: [],
        },
        rules: {
          cityCode: [{
            required: true,
            message: '请选择',
            trigger: 'change'
          }, ],
          areaCode: [{
            required: true,
            message: '请选择',
            trigger: 'change'
          }],
        },
        list: null,
        total: null,
        listLoading: true,
        listQuery: {
          page: 1,
          limit: 10,
          role_name: '',
          area: '',
          cityArea: '',
        },
        countydata1: [],
        dialogFormVisible: false,
        dialogStatus: '',
        userManager_btn_edit: false,
        userManager_btn_del: false,
        userManager_btn_add: false,
        userManager_btn_resetpass:false,
        textMap: {
          update: '编辑',
          create: '创建'
        },
        tableKey: 0,
        citieCounty: [], //地市
        city_id: '330100',
        countydata: [], //区县
        dispatchUser: [],//调度人员信息
        provinceDispatchUser: [],//省级调度人员信息
        tableheight: 400,
        pageSize: null,
        rolelist: [],
        userId:getSysUserId(),
        btnloading:false,

      }
    },
    created() {
      let bodyheight = document.documentElement.clientHeight;
      this.tableheight = parseInt(bodyheight) > 800 ? 570 : 400;
      this.listQuery.limit = parseInt(bodyheight) > 800 ? 15 : 10;
      this.pageSize = this.listQuery.limit;
      this.getList();
      this.userManager_btn_edit = this.elements['userManager:btn_edit'];
      this.userManager_btn_del = this.elements['userManager:btn_del'];
      this.userManager_btn_add = this.elements['userManager:btn_add'];
      this.userManager_btn_resetpass = this.elements['userManager:btn_pass']

    },
    mounted() {
      //获取地市
      this.getCitiList();
      this.getProvinceDispatchUser();
    },
    computed: {
      ...mapGetters([
        'elements'
      ])
    },
    methods: {
      getList() {
        this.listLoading = true;
        let pams = this.listQuery;
        dispatchPage(this.listQuery)
          .then(response => {
            this.list = response.data.rows;
            this.total = response.data.total;
            this.listLoading = false;
          })
      },
      handleFilter() {
        this.listQuery.limit = this.pageSize;
        this.listQuery.page = 1;
        this.getList();
      },
      btnresetTemp() { //重置
        this.listQuery.limit = this.pageSize;
        this.listQuery.page = 1;
        this.countydata1 = [];
        this.getList();

      },
      handleSizeChange(val) {
        this.listQuery.limit = val;
        this.getList();
      },
      handleCurrentChange(val) {
        this.listQuery.page = val;
        this.getList();
      },
      handleCreate() {
        let that = this;

        that.dialogStatus = 'create';
        that.dialogFormVisible = true;
        that.resetTemp();

        //查询地市
        that.getpostcounuty();
      },
      handleUpdate(row) { //修改
        let that = this;
        let obj = {
          tdu_id: row.tdu_id
        };
        getDispatchUserInfo(obj).then(response => {
          this.form = {
            id: response.ID,
            cityCode: response.city_code ? response.city_code.split(",") : [],
            areaCode: [],
            provinceUser: response.province_user ? response.province_user.split(",") : [],
            cityUser: response.city_user ? response.city_user.split(",") : [],
            areaDispatchUser: response.dispatch_user ? response.dispatch_user.split(",") : [],
          };
          this.dialogFormVisible = true;
          this.dialogStatus = 'update';
          that.getpostcounuty(2, response.area_code);
        });
      },
      handleDelete(row) {
        this.$confirm('此操作将永久删除, 是否继续?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          let pams = {
            'id':row.tdu_id
          };
          delDispatchObj(pams).then(res => {
            if (res.flag == '1') {
              this.$notify({
                title: '成功',
                message: '删除成功',
                type: 'success',
                duration: 2000
              });
              const index = this.list.indexOf(row);
              this.list.splice(index, 1);
            } else {
              this.$notify({
                title: '警告',
                message: res.msg,
                type: 'warning',
                duration: 2000
              });
            }

          });
        }).catch(() => {

        });
      },
      create(formName) {
        let that = this;
        const set = this.$refs;
        set[formName].validate(valid => {
          if (valid) {
            this.form.updUserId = localStorage.getItem('dlUserId');
            this.userIdToUserName();
            let formobj = this.form;
            formobj.cityCode = this.form.cityCode ? this.form.cityCode.join(',') : '';
            formobj.areaCode = this.form.areaCode ? this.form.areaCode.join(',') : '';
            formobj.provinceUser = this.form.provinceUser?this.form.provinceUser.join(',') : '';
            formobj.cityUser = this.form.cityUser?this.form.cityUser.join(',') : '';
            formobj.areaDispatchUser = this.form.areaDispatchUser?this.form.areaDispatchUser.join(',') : '';
            formobj.provinceUserName = this.form.provinceUserName?this.form.provinceUserName.join(',') : '';
            formobj.cityUserName = this.form.cityUserName?this.form.cityUserName.join(',') : '';
            formobj.areaDispatchUserName = this.form.areaDispatchUserName?this.form.areaDispatchUserName.join(',') : '';
            addDisPatchObj(formobj).then(res => {
              if (res.flag == '1') {
                this.dialogFormVisible = false;
                this.getList();
                this.$notify({
                  title: '成功',
                  message: '创建成功',
                  type: 'success',
                  duration: 2000
                });

                this.resetTemp();
              } else {
                this.$notify({
                  title: '警告',
                  message: res.msg,
                  type: 'warning',
                  duration: 2000
                });
              }
            })
          } else {
            return false;
          }
        });
      },
      userIdToUserName(){
        //code转name存入库中
        let provinceUserNameArr = [];
        let cityUserNameArr = [];
        let areaDispatchUserNameArr = [];
        if(this.form.provinceUser){
          for(let i = 0; i < this.form.provinceUser.length; i++){
            this.provinceDispatchUser.forEach((item) => {
              if(item.KEYSS === this.form.provinceUser[i]){
                provinceUserNameArr.push(item.VALUE);
              }
            })
          }
        }
        if(this.form.cityUser){
          for(let i = 0; i < this.form.cityUser.length; i++){
            this.dispatchUser.forEach((item) => {
              if(item.KEYSS === this.form.cityUser[i]){
                cityUserNameArr.push(item.VALUE);
              }
            })
          }
        }
        if(this.form.areaDispatchUser){
          for(let i = 0; i < this.form.areaDispatchUser.length; i++){
            this.dispatchUser.forEach((item) => {
              if(item.KEYSS === this.form.areaDispatchUser[i]){
                areaDispatchUserNameArr.push(item.VALUE);
              }
            })
          }
        }
        this.form.provinceUserName = provinceUserNameArr;
        this.form.cityUserName = cityUserNameArr;
        this.form.areaDispatchUserName = areaDispatchUserNameArr;
      },

      cancel(formName) {
        this.$refs[formName].resetFields();
        this.dialogFormVisible = false;
        this.resetTemp();
      },
      update(formName) {
        const set = this.$refs;
        let that = this;
        set[formName].validate(valid => {
          if (valid) {
            // this.dialogFormVisible = false;
            this.form.updUserId = localStorage.getItem('dlUserId');
            this.userIdToUserName();
            let formobj = this.form;
            formobj.cityCode = this.form.cityCode ? this.form.cityCode.join(',') : '';
            formobj.areaCode = this.form.areaCode ? this.form.areaCode.join(',') : '';
            formobj.provinceUser = this.form.provinceUser?this.form.provinceUser.join(',') : '';
            formobj.cityUser = this.form.cityUser?this.form.cityUser.join(',') : '';
            formobj.areaDispatchUser = this.form.areaDispatchUser?this.form.areaDispatchUser.join(',') : '';
            formobj.provinceUserName = this.form.provinceUserName?this.form.provinceUserName.join(',') : '';
            formobj.cityUserName = this.form.cityUserName?this.form.cityUserName.join(',') : '';
            formobj.areaDispatchUserName = this.form.areaDispatchUserName?this.form.areaDispatchUserName.join(',') : '';
            updateDispatchUser(formobj).then(res => {
              if (res.flag == '1') {
                this.dialogFormVisible = false;
                this.getList();
                this.$notify({
                  title: '成功',
                  message: '修改信息成功！',
                  type: 'success',
                  duration: 2000
                });

                that.resetTemp();
              } else {
                this.$notify({
                  title: '失败',
                  message: '修改信息失败...',
                  type: 'error',
                  duration: 2000
                });
              }
            });
          } else {
            return false;
          }
        });
      },
      resetTemp() {
        let that = this;

        this.form = {
          cityCode: undefined,
          areaCode: [],
          provinceUser: [],
          cityUser: [],
          areaDispatchUser: [],
          provinceUserName: [],
          cityUserName: [],
          areaDispatchUserName: [],
        };
        this.countydata = [];
        this.$nextTick(() => {
          that.$refs['form'].resetFields();
        })
      },
      //获取地市
      getCitiList() {
        let that = this;
        getComCityList().then(data => {
          //  console.log(data);
          data.unshift({
            KEYSS: '80',
            VALUE: "陕西省"
          })
          that.citieCounty = data;
          that.form.cityCode = that.city_id;
        }).catch(err => {})
      },
      getpostcounuty1(type, areaCode) {
        let that = this;
        let params = {
          dataSqlId: 'WH_SOURCE_1',
          OBJECT_ID: '60000053',
          VALUE: that.listQuery.area,
          staffAuthDistrictId: '80223',
        }
        that.listQuery.cityArea = '';
        that.countydata1 = [];
        getDisPatchArea(params).then(data => {
          that.countydata1 = data;

        }).catch(err => {})

      },

      getDispatchUser(){
        let areaCodes = this.form.cityCode ? this.form.cityCode.join(',') : '';
        let that = this;
        let params = {
          areaCode: areaCodes
        };
        getDisPatchAreaUser(params).then(data => {
          that.dispatchUser = data;
        }).catch(err => {});
      },
      getProvinceDispatchUser(){
        // let areaCodes = this.form.cityCode ? this.form.cityCode.join(',') : '';
        let that = this;
        let params = {
          areaCode: '80'
        };
        getDisPatchAreaUser(params).then(data => {
          that.provinceDispatchUser = data;
        }).catch(err => {});
      },

      getpostcounuty(type, areaCode) {
        let areaCodes = this.form.cityCode ? this.form.cityCode.join(',') : '';
        let that = this;
        let params = {
          dataSqlId: 'WH_SOURCE_1',
          OBJECT_ID: '60000053',
          VALUE: areaCodes,
          staffAuthDistrictId: '80223',
        }
        that.form.areaCode = [];
        that.countydata = [];
        //  console.log(params);
        getDisPatchArea(params).then(data => {
          that.countydata = data;
          if (type == 2 && areaCode) {
            that.form.areaCode = areaCode.split(',');
          }
        }).catch(err => {});
        this.getDispatchUser();
      },
    }
  }
</script>

<style scoped lang="scss">

</style>
<style lang="scss">
  @import "src/styles/common.scss";

  .usercontainer {
    .userrows {
      .filter-item {
        width: 100%;
        margin-bottom: 0px;
      }
    }

    .formbtn {
      .el-form-item__content {
        margin-left: 0px !important;
      }
    }
  }

  .search-box {
    .el-form-item {
      margin-bottom: 0px;
    }
  }
</style>
