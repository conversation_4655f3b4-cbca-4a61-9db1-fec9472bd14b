<template>
  <!--工单查询-->
  <div class="app-container disposefiles handFilter ">
    <div class="filter-container zproble">
      <el-form :model="listQuery" ref="listQuery" :rules="formrules" label-position="right" label-width="110px"
        label-suffix=":" class="demo-form-inline applyform">
        <el-row :gutter="10">
          <el-col :span=24>
            <el-form-item label="问题标题" prop="titleName">
              <el-input class="filter-item" placeholder="请输入内容" v-model="listQuery.titleName"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span=24>
            <el-form-item label="问题描述" prop="taskDesc">
              <el-input class="filter-item" type="textarea" :autosize="{ minRows: 2, maxRows: 3 }" placeholder="请输入内容"
                v-model="listQuery.taskDesc"> </el-input>
            </el-form-item>
          </el-col>
          <el-col :span="20">
            <el-form-item label="问题附件">
              <el-upload class="upload-demo" style="margin-right: 10px;height:30px;" action="" ref="upload"
                :on-change="handleChangeFile" :http-request="myUploadFile" :multiple="false" :limit="1"
                :file-list="fileListFile">

                <el-button size="small" type="primary">问题附件</el-button>
                <div slot="tip" class="el-upload__tip uploawar">上传附件不超过30M</div>

              </el-upload>
              <div class="errdv">
                <div v-show="erromasg">
                  <p>导入失败信息：</p>
                  <p class="errdvp1">{{ erromasg }}</p>
                </div>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item class="formbtn text-center">
              <el-button split-button type="primary" @click="submitUpload" :loading="formloding">提交 </el-button>
              <el-button @click="handleresetForm" :loading="formloding">重置</el-button>
            </el-form-item>
          </el-col>

        </el-row>
      </el-form>
      <div class="zlistTablecom listTables martop10">
        <el-table stripe border :fit="true" highlight-current-row max-height="390px" :data="tableData"
          style="width: 100%">
          <el-table-column type="index" align="left" width="50" label="序号"></el-table-column>
          <el-table-column align="left" prop="file_name" label="文件名称" min-width="200%">
          </el-table-column>
          <el-table-column align="left" label="上传人">
            <template slot-scope="scope">
              <span>{{ scope.row.name }}</span>
            </template>
          </el-table-column>
          <el-table-column align="left" label="上传时间">
            <template slot-scope="scope">
              <span>{{ scope.row.create_time }}</span>
            </template>
          </el-table-column>
          <el-table-column align="left" label="文件类型">
            <template>
              <span></span>
            </template>
          </el-table-column>
          <el-table-column align="left" label="操作">
            <template slot-scope="scope">
              <a @click="handleDelFile(scope.row)"
                style="text-decoration: underline; color: red; font-weight: bold; font-style: italic;">删&nbsp;除</a>
            </template>
          </el-table-column>
        </el-table>
      </div>

    </div>
  </div>
</template>

<script>

import { mapGetters } from 'vuex'

import { getFileOrderNum, doStartQuestionApply } from '@/api/admin/department/index';
import { uploadFile, saveOrderAddFile, deleteFile } from '@/api/require/sgzl/upload_file';
import { getFilesTabObj } from '@/api/form/form/index';
export default {
  name: '',
  components: {},
  component: {

  },
  data() {
    return {
      listQuery: {
        createUser: this.$store.state.user.name,
        createUserId: this.$store.state.user.staffId,
        taskDesc: null,
        titleName: null,

      },

      fileListFile: [],
      fileOrderNum: null,//获取上传的key
      erromasg: '',
      fileId: null,
      formrules: {
        titleName: [{ required: true, message: '请输入问题描述', trigger: 'blur' },],
        taskDesc: [{ required: true, message: '请输入问题描述', trigger: 'blur' },],
      },
      formloding: false,
      tableData: [],

    }
  },
  created() {
    this.getFileOrderNumlist();
  },
  mounted() {


  },
  methods: {
    acceptFile(e) {
      const allowHook = {
        video: '.mp4, .ogv, .ogg, .webm',
        audio: '.wav, .mp3, .ogg, .acc, .webm, .amr',
        file: 'doc,.docx,.xlsx,.xls,.pdf',
        excel: '.xlsx,.xls',
        img: '.jpg, .jpeg, .png, .gif'
      }
      if (e) {
        return allowHook[e];
      }
      let srt = null
      for (const k in allowHook) {
        srt += allowHook[k]
      }
      return srt
    },
    handleresetForm() {//重置
      this.$refs['listQuery'].resetFields();
      this.fileListFile = [];
      //this.fileOrderNum = null;
    },
    submitUpload() {//提交
      this.$refs['listQuery'].validate(valid => {
        if (valid) {
          //  if(this.fileListFile.length === 0) {
          //   this.$message.warning('请选择上传文件');
          //   return;
          //   }
          this.mysbmint();
        }
      })
    },
    handleChangeFile(file, fileList) {
      if (file.status === "ready") {

        if (fileList.length > 1) {
          fileList.splice(0, 1);
        }
        this.fileListFile = fileList;
      }
    },

    mysbmint() {
      let that = this;
      let pams = {
        'creat_by': this.listQuery.createUserId,
        'question': this.listQuery.taskDesc,
        'title': this.listQuery.titleName,
        'fileOrderNum': that.fileOrderNum,
      }
      //1.提交
      that.formloding = true;
      doStartQuestionApply(pams).then(response => {
        //that.fileOrderNum = null;
        that.formloding = false;
        if (response.flag == '0') {

          this.$message.success(response.msg);
          // this.orderNum = response.message;
        } else {
          this.$message.warning(response.msg);

        }


      }).catch(err => {
        //    that.erromasg = '';
        //    this.orderNum = '';
        // that.fileOrderNum = null;
        that.formloding = false;
      })
    },
    getFileOrderNumlist(file) {//获取上传的key
      let that = this;
      that.fileOrderNum = null;
      getFileOrderNum().then(data => {

        that.fileOrderNum = data //data.fileOrderNum;
        if (that.fileOrderNum) {
          //  that.myUploadFile(file)
        } else {
          this.$message.warning('生成序列号失败。请重新上传附件！！！！');
        }

      }).catch(err => {

      })

    },
    myUploadFile(file) {
      //  console.log(file)
      let that = this;
      let filSiz = file.file;
      filSiz = filSiz.size;
      let filnum = parseFloat(filSiz / 1024 / 1024);
      if (filnum > 30) {
        this.$message({
          message: '请上传小于30M的文件！！！',
          showClose: true,
          type: 'error',
          duration: 2000
        });
        this.$refs.upload.clearFiles();
        return;
      }
      let fd = new FormData();
      fd.append('file', file.file);//传文件
      fd.append('userId', this.listQuery.createUserId);//传其他参数
      //  fd.append('requireId', this.orderNum)
      //1.上传文件
      uploadFile(fd).then(response => {
        if (JSON.parse(JSON.stringify(response)).result === '2') {
          this.$message({
            title: '失败',
            message: '保存附件失败，失败原因：' + JSON.parse(JSON.stringify(response)).msg,
            showClose: true,
            type: 'error',
            duration: 2000
          });

          this.$refs.upload.clearFiles();
          return;
        }
        //2.获取文件fileId
        let fileId = JSON.parse(JSON.stringify(response)).fileId;

        //3.保存附件到业务表(tm_order_add)
        let saveObj = {
          relaId: that.fileOrderNum,
          // orderNum: this.orderNum.orderNum,
          attrId: 'wtfq',
          attrValue: fileId,
          remark: this.listQuery.createUserId
        };
        saveOrderAddFile(saveObj).then(response => {
          if (response.flag === "1") {
            this.$message({
              title: '成功',
              message: '保存附件成功',
              showClose: true,
              type: 'success',
              duration: 2000
            });
            this.$refs.upload.clearFiles();
            this.getFilesTabeData();

          } else if (response.flag === "0") {
            this.$message({
              title: '错误',
              message: '保存附件失败...',
              showClose: true,
              type: 'error',
              duration: 2000
            });

            this.$refs.upload.clearFiles();
          }
        });
      });
    },
    getFilesTabeData() {//查询附件
      //let orde = this.orderNum.split(',') || [];
      // console.log(12345)
      let that = this;
      let obj = { orderNum: that.fileOrderNum }
      getFilesTabObj(obj).then(response => {
        let json = JSON.parse(JSON.stringify(response));
        this.tableData = json.selectFilesTableData;
      });
    },
    handleDelFile(obj) {
      let that = this;
      let data = {
        requireId: that.fileOrderNum,
        fileId: obj.file_id,
        userId: this.listQuery.createUserId
      };
      deleteFile(data).then(response => {
        if (response.flag === "1") {
          this.$message({
            title: '成功',
            message: '删除附件成功',
            showClose: true,
            type: 'success',
            duration: 2000
          });
          this.getFilesTabeData();
        } else if (response.flag === "0") {
          this.$message({
            title: '错误',
            message: '删除附件失败...',
            showClose: true,
            type: 'error',
            duration: 2000
          });
        }
      });
    },

  }
}
</script>
<style scoped lang="scss">
.applyform {
  width: 80%;
  margin: 0px auto;
  background-color: #fff;
  padding: 20px 10px;
}

.errdv {
  min-height: 50px;
  color: #f56c6c;
  font-size: 14px;
  text-align: left;
  line-height: 1.4;

  p {
    margin: 0px;
  }

  .errdvp1 {
    padding-left: 10px;
  }
}
</style>
<style lang="scss">
// @import "src/styles/common.scss";
.zproble {
  .applyform {
    width: 90%;
    margin: auto;
    background: #fff;
    padding: 10px;

    .el-form-item {
      margin-bottom: 22px;
    }

    .filter-item {
      margin-bottom: 2px;
    }

  }
}
</style>
