import http from '@/api/http'

//获取部门书数据
export function getDepartAll(query) {
  return http({
    url: '/api/admin/depart/getDepartAll',
    method: 'get',
    params: query
  });
}
//添加部门
export function addDepart(obj) {
  return http({
    url: '/api/admin/depart/addDepart',
    method: 'post',
    data: obj
  });
}
//删除部门
export function delDepart(query) {
    return http({
      url: '/api/admin/depart/delDepart',
      method: 'get',
      params: query
    });
  }
//更新部门
export function updDepart(obj) {
    return http({
      url: '/api/admin/depart/updDepart',
      method: 'post',
      data: obj
    });
  }
//获取部门区域
export function getReqionList(query) {
    return http({
      url: '/api/admin/areaCon/getCityList',
      method: 'get',
      params: query
    });
}
//获取地市
export function getComCityList(query) {
    return http({
      url: '/api/admin/areaCon/getCityList',
      method: 'get',
      params: query
    });
  }
//获取编辑的数据 getObj
export function getDepartInfo(obj) {
    return http({
      url: '/api/admin/depart/getDepartInfo',
      method: 'post',
      data: obj
    });
}
//查询人员
export function getUserByNotDepart(obj) {
  return http({
    url: '/api/admin/user/getUserByNotDepart',
    method: 'post',
    data: obj
  });
}
//添加人员
export function saveUserDepart(obj) {
  return http({
    url: '/api/admin/depart/saveUserDepart',
    method: 'post',
    data: obj
  });
}

//查询部门人员
export function getUserByDepart(obj) {
  return http({
    url: '/api/admin/depart/getUserByDepart',
    method: 'post',
    data: obj
  });
}
//查询部门人员 删除人员 单个
export function delUserDepart(obj) {
  return http({
    url: '/api/admin/depart/delUserDepart',
    method: 'post',
    data: obj
  });
}

//获取账号申请的key  
export function getFileOrderNum(query) {
  return http({
    url: '/api/watchme/requireSystemCon/getFileOrderNum',
    method: 'get',
    params: query
  });
}
//申请账号上传 
export function doStartAccNumApply(obj) {
  return http({
    url: '/api/watchme/requireSystemCon/doStartAccNumApply',
    method: 'post',
    data:obj
  });
}
//问题反馈
export function doStartQuestionApply(obj) {
  return http({
    url: '/api/watchme/requireSystemCon/doStartQuestionApply',
    method: 'post',
    data: obj
  });
}



