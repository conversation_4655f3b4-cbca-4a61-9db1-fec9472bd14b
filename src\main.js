import Vue from 'vue'
import ElementUI from 'element-ui'
import App from './App.vue'
import router from './router'
import store from './store'
import * as filters from './filters'; // 全局vue filter
import './utils/directives'
import './styles/index.scss'
// 防止v-html输入恶意内容
import xss from 'xss'
import * as api from "./api/services"
import IconSvg from './components/Icon-svg';// svg 组件
import 'element-ui/lib/theme-chalk/index.css'
// 在此处引入打包后element-ui主题的css文件
//import './assets/css/theme/index.scss'
import "./assets/css/font/all.css"
import vueWaves from './directive/waves';// 水波纹指令
import "swiper/dist/css/swiper.css"
import './assets/iconfont/iconfont';
import VueCropper from 'vue-cropper'
import '@/assets/css/font-awesome.css'
import NProgress from 'nprogress'; // Progress 进度条
import 'nprogress/nprogress.css';// Progress 进度条 样式
import { getToken,removelocalStorage} from '@/utils/auth';
import { Message } from 'element-ui';
 
//定义一个新的Message方法，多传入一个offset参数，用于this.$messgae中修改offset中的初始值
const $message = options => {
  return Message({
    ...options,
    offset: 80
  });
};
//重写一遍success的方法,将offset写入options
["success", "warning", "info", "error"].forEach(type => {
  $message[type] = options => {
    if (typeof options === "string") {
      options = {
        message: options,
        offset: 60
      };
    }
    options.type = type;
    return Message(options);
  };
});

Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
});
import VueQuillEditor from 'vue-quill-editor'
import 'quill/dist/quill.core.css'
import 'quill/dist/quill.snow.css'
import 'quill/dist/quill.bubble.css'
import "./assets/css/reset.scss"

import VueClipboard from 'vue-clipboard2'

Vue.component('icon-svg', IconSvg);
Vue.use(VueQuillEditor)
Vue.use(ElementUI)
Vue.use(VueCropper)
Vue.use(VueClipboard)
Vue.use(vueWaves);
Vue.prototype.$api = api
Vue.prototype.$message = $message
Vue.prototype.xss = xss

const isDebug_mode = process.env.NODE_ENV === 'development'
Vue.config.debug = isDebug_mode
Vue.config.devtools = isDebug_mode
Vue.config.productionTip = isDebug_mode

new Vue({
  router,
  store,
  render: h => h(App)
}).$mount('#app')
