<!-- 定制任务页面 -->
<template>
  <div class="app-container calendar-list-container disposefiles handFilter">
    <div class="filter-container">
      <div class="search-box">
        <el-form label-position="right" :inline="true" :model="formDatalist" size="small" label-width="80px"
          label-suffix=":" class="demo-form-inline">

          <el-form-item label="标题">
            <el-input class="filter-item" placeholder="标题" clearable=""
              v-model.trim="formDatalist.titleName"></el-input>
          </el-form-item>
          <el-form-item label="内容">
            <el-input class="filter-item" placeholder="内容" clearable=""
              v-model.trim="formDatalist.noticeInfo"></el-input>
          </el-form-item>
          <el-form-item label="发布时间">
            <el-date-picker v-model="formDatalist.DATE" type="daterange" value-format="yyyy-MM-dd" format="yyyy-MM-dd"
              range-separator="至" start-placeholder="发布日期" end-placeholder="发布日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item class="formbtn">
            <el-button type="primary" @click="submitForm('formDatalist')">查询</el-button>
            <el-button @click="resetForm('formDatalist')">重置</el-button>
            <!-- <el-button :loading="btnloading" type="primary" @click="handlEexport"> 导出</el-button> -->
          </el-form-item>
          <!-- <el-row :gutter="10">
               <el-col :xs="8" :sm="8" :md="6" :lg="6" :xl="4">
                
              </el-col>
               <el-col :xs="8" :sm="8" :md="6" :lg="6" :xl="4">
                
              </el-col>
              <el-col :xs="8" :sm="8" :md="8" :lg="8" :xl="6" class = "form-iteam" >
                
              </el-col>

              <el-col :xs="8" :sm="8" :md="8" :lg="6" :xl="4" class = " text-left" >
                
              </el-col>
            </el-row> -->
        </el-form>
      </div>
      <!-- 表格 -->
      <div style="margin-bottom: 10px;">
        <el-row>
          <el-col :xs="24" :sm="24" :md="24" :lg="24" :xl="24">
            <el-button @click="handleCreate" v-waves size="mini" type="primary" icon="el-icon-plus">新增</el-button>

            <!-- <el-button @click="handletoView"  v-waves size="mini" type="primary" icon="el-icon-view" >查看</el-button> -->
            <el-button @click="handleDelete" v-waves size="mini" type="primary" icon="el-icon-delete">删除</el-button>

          </el-col>
        </el-row>
      </div>
      <div class="listTables martop10" v-loading="lodin">
        <el-table size="small" :data="tableData" ref="multipleTable" style="width: 100%"
          >
          <el-table-column type="selection" width="80"></el-table-column>
          <el-table-column v-for="(row, index) in columnDatas" :key="index" :prop="row.field" :label="row.title"
            :min-width="row.columnWidth || 120" show-overflow-tooltip>
          </el-table-column>
          <el-table-column align="center" label="操作" width="150">
            <template slot-scope="scope">
              <el-button v-waves type="text" size="small" @click="handleModify(scope.row)">修改</el-button>
              <el-button type="text" size="small" @click.stop="handletovivew(scope.row)">查看</el-button>

            </template>
          </el-table-column>
        </el-table>
        <div class="pag" style="margin-top: 15px;margin-bottom:0px; ">
          <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :page-sizes="[10, 15, 20, 50]" :current-page.sync="pageIndex" :page-size.sync="pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="totalCount">
          </el-pagination>
        </div>
      </div>
      <!-- 编辑dilog -->
      <el-dialog :title="dialogtitle" width="1100px" :visible.sync="dialogEditVisible" top="5vh" class=""
        :append-to-body="false" :close-on-click-modal="false" @close="closeAdd">
        <addEditor :closeDiadl="closeDiadl" ref="addEditor"></addEditor>
      </el-dialog>
    </div>
  </div>
</template>

<script>

import qs from "qs";
import {
  selNoticePage,//查询获取数据
  deleteNotice,//删除
} from '@/api/admin/user/index';

export default {
  components: {
    "addEditor": () => import("./addquillEditor"),
  },
  data() {
    return {
      formDatalist: {
        noticeInfo: '',
        titleName: '',
        DATE: [],
      },
      formDatalist1: {},
      pageIndex: 1,
      pageSize: 10,
      totalCount: 0,
      tablelist: {
        pageIndex: 1,
        pageSize: 10,
        totalCount: 0,
      },
      tableData: [],
      columnDatas: [
        { 'field': 'notice_title', title: '公告标题', 'columnWidth': '150' },
        // {'field': 'notice_title', title: '公告内容','columnWidth':'150'},
        { 'field': 'name', title: '发布人', 'columnWidth': '100' },
        { 'field': 'notice_type', title: '专业', 'columnWidth': '120' },
        { 'field': 'create_time', title: '发布时间', 'columnWidth': '180' },

      ],
      btnloading: false,

      tableheight: 390,
      lodin: false,
      dialogEditVisible: false,
      dialogtitle: '新增公告'
    }
  },
  created() {
    let bodyheight = document.documentElement.clientHeight;
    this.tableheight = parseInt(bodyheight) > 800 ? 565 : 390;
    this.tablelist.pageSize = parseInt(bodyheight) > 800 ? 15 : 10;
    this.pageSize = this.tablelist.pageSize;

  },
  mounted() {
    // this.queryDateInit()
    this.queryData()
  },
  computed: {

  },
  methods: {
    handleCreate() {//新增发布
      let that = this;
      that.dialogtitle = '新增公告';
      that.dialogEditVisible = true;
      that.$nextTick(() => {
        that.$refs.addEditor.getrowsId('', 'add')
      })


    },
    closeAdd() {
      //关闭修改弹框
      this.$nextTick(() => {
        //this.$refs.applyAdd.handreset();
      });
    },
    closeDiadl(typ) {
      //修改关闭
      let that = this;
      that.dialogEditVisible = false;
      if (typ == '1') {// 1新增  2修改
        that.queryData();
      } else {
        that.queryData();
      }
    },
    handleDelete() {//删除
      let that = this;
      const selectData = this.$refs.multipleTable.selection;

      const msg = "你选中了 [ " + selectData.length + " ] 条数据.";
      if (selectData.length != '1') {
        this.$message({
          message: "请选择一条数据删除",
          type: "warning",
          duration: 1500,
        });
        return;
      }


      // const arr = {arr: JSON.stringify(_selectData)};
      this.$confirm(msg + ",将永久删除, 是否继续?", "提示", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning"
      }).then(() => {
        let ids = [];
        selectData.forEach((item) => {
          ids.push(item.id)
        })
        let params = {
          id: ids.join(",")
        };
        deleteNotice(params).then((res) => {
          if (res.flag == '0') {
            that.$message({
              showClose: true,
              message: '删除成功',
              type: "success",
              duration: 1500
            });
            that.submitForm();
          } else {
            that.$message({
              showClose: true,
              message: '删除失败',
              type: "warning",
              duration: 1500
            });
          };


        }).catch((err) => {


        })
      }).catch(() => {

      });


    },
    handletovivew(rows) {//查看
      let that = this;
      // const selectData = this.$refs.multipleTable.selection;

      // if (selectData.length !='1') {
      //   this.$message({
      //     message: "请选择一条数据进行查看",
      //     type: "warning",
      //     duration:1500,
      //   });
      //   return;
      // }
      that.dialogtitle = '查看公告'
      that.dialogEditVisible = true;
      that.$nextTick(() => {
        that.$refs.addEditor.getrowsId(rows.id, 'toview')
      })

    },
    handleModify(rows) {//修改
      let that = this;
      that.dialogtitle = '修改公告'
      that.dialogEditVisible = true;
      that.$nextTick(() => {
        that.$refs.addEditor.getrowsId(rows.id, 'modify');
      })


    },
    // 分页pagesize
    handleSizeChange(val) {
      this.pageIndex = 1;
      this.pageSize = val;
      this.queryData()
    },
    // 分页pageindex
    handleCurrentChange(val) {
      this.pageIndex = val;
      this.queryData()
    },
    // 查询数据
    queryData() {
      let that = this;
      that.formDatalist1 = that.formDatalist;
      // console.log(that.formDatalist.DATE);
      let datelist = that.formDatalist.DATE;
      let params = {
        'page': this.pageIndex,
        'limit': this.pageSize,
        'noticeTitle': that.formDatalist.titleName,//标题；
        'noticeInfo': that.formDatalist.noticeInfo,//内容；
        'startTime': datelist.length ? datelist[0] : '',//查询开始时间，
        'endTime': datelist.length ? datelist[1] : '',//查询截止时间
        // user_id:that.formDatalist.userName,
      }
      // return;
      that.lodin = true;
      selNoticePage(params).then((res) => {
        // console.log(res)
        if (res.status == 200) {
          let dat = res.data
          this.totalCount = dat.total;
          this.tableData = dat.rows;

        }

        that.lodin = false;
      }).catch((err) => {

        that.lodin = false;
      })
    },
    // 查询
    submitForm(formName) {
      let that = this;
      this.pageIndex = 1;
      that.pageSize = this.tablelist.pageSize;
      this.queryData()
    },
    resetForm() {//重置
      let that = this;
      that.formDatalist.titleName = null;//标题；
      that.formDatalist.noticeInfo = null;//内容；
      that.formDatalist.DATE = [];
      this.pageIndex = 1;
      that.pageSize = this.tablelist.pageSize;
    },

  }
}
</script>

<style lang='scss' scoped>
.search-tile {
  line-height: 34px;
  color: #999;
  margin-bottom: 30px;
  border-bottom: 1px solid #ddd;
  font-size: 16px;
}

.table-box {
  margin-top: 10px;
}

.tablebtn {
  padding: 5px !important;
}

.tablebtn.el-button+.tablebtn.el-button {
  margin-left: 5px;
}
</style>
<style lang="scss">
// @import "src/styles/common.scss";</style>
