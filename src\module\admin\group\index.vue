<template>
  <div class="app-container calendar-list-container disposefiles">
    <div class="filter-container">
      <el-row :gutter="10">


        <!-- <el-table border size='small' :data="treeData">
          <el-table-column prop="label" label="角色名称" min-width="150"  :showOverflowTooltip=true>
          </el-table-column>
          <el-table-column prop="name" label="所属系统" min-width="150"  :showOverflowTooltip=true>
          </el-table-column>
          <el-table-column prop="station_name" label="描述" min-width="150"  :showOverflowTooltip=true>
          </el-table-column>
          <el-table-column prop="station_name" label="操作" min-width="150"  :showOverflowTooltip=true>
          </el-table-column>
        </el-table> -->

        <el-col :span="24">
          <el-button-group>
            <el-button type="primary" size="small" v-if="groupManager_btn_add" @click="handlerAdd">添加</el-button>
            <el-button type="primary" size="small" v-if="groupManager_btn_edit" @click="handlerEdit">编辑</el-button>
            <el-button type="primary" size="small" v-if="groupManager_btn_del" @click="handleDelete">删除</el-button>
            <el-button type="primary" size="small" v-if="groupManager_btn_view" @click="handlerUser(1)">查看用户</el-button>
            <el-button type="primary" size="small" v-if="groupManager_btn_resourceManager" @click="handlerAuthority">
              权限分配</el-button>
            <el-button type="primary" size="small" v-if="groupManager_btn_userManager" @click="handlerUser(2)">
              关联用户</el-button>
            <!--<el-button type="primary" size="small" v-if="groupManager_btn_userManager" @click="handlerjizhan">
      关联基站</el-button>-->
          </el-button-group>
        </el-col>
        <el-col :span="8" style='margin-top:10px;'>
          <el-input placeholder="输入关键字进行过滤" v-model="filterText"> </el-input>
          <div class="departree" ref="treedv">
            <el-tree class="filter-tree" :data="treeData" node-key="id" highlight-current :props="defaultProps"
              :filter-node-method="filterNode" ref="groupTree" @node-click="getNodeData" default-expand-all>
            </el-tree>
          </div>
        </el-col>
        <el-col :span="16" style='margin-top:10px;'>
          <el-card class="box-card">
            <el-form :label-position="labelPosition" size="small" label-width="80px" :model="form" ref="form">
              <el-form-item label="名称">
                <el-input v-model="form.name" :disabled="formEdit"></el-input>
              </el-form-item>
              <!-- <el-form-item label="编码">
                <el-input v-model="form.code" :disabled="formEdit"></el-input>
              </el-form-item> -->
              <el-form-item label="所属系统">
                <el-select v-model="form.system_name" placeholder="请选择" :disabled="formEdit">
                  <el-option v-for="(item, inde) in systemlist" :key="inde" :label="item.data_name"
                    :value="item.data_id"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="描述">
                <el-input v-model="form.description" :disabled="formEdit"></el-input>
              </el-form-item>
              <el-form-item v-if="formStatus == 'update'">
                <el-button type="primary" v-if="groupManager_btn_edit" @click="update">更新</el-button>
                <el-button @click="onCancel">取消</el-button>
              </el-form-item>
              <el-form-item v-if="formStatus == 'create'">
                <el-button type="primary" v-if="groupManager_btn_add" @click="create">保存</el-button>
                <el-button @click="onCancel">取消</el-button>
              </el-form-item>
            </el-form>
          </el-card>
        </el-col>
        <el-dialog :title="dialogUserName" width="1000px" :visible.sync="dialogUserVisible" @close="closeAdd1">
          <group-user :closeDiadl="closeDiadl" ref="groupUser"></group-user>
        </el-dialog>
        <el-dialog :title="dialogAuthorityName" size="large" :visible.sync="dialogAuthorityVisible">
          <group-authority :groupId="treenodeId" @closeAuthorityDialog="closeAuthorityDialog"
            ref="groupAuthority"></group-authority>
        </el-dialog>
        <el-dialog title="关联基站" size="large" :visible.sync="dialogVisible1" top="5vh">
          <baseimport :closeDiadl="closeDiadl1" ref="baseimport"></baseimport>
        </el-dialog>
      </el-row>
    </div>
  </div>
</template>

<script>
import {
  fetchTree,
  getObj,
  addObj,
  delObj,
  putObj,
  delGroup,//删除分组
  getqueryList,//查询所属系统
} from '@/api/admin/group/index';
import { mapGetters } from 'vuex';
export default {
  name: 'groupDetail',
  components: {
    'group-user': () => import('./components/groupUser'),
    'group-authority': () => import('./components/groupAuthority'),
    'baseimport': () => import('./components/baseimport'),
  },
  props: {
    type: {
      default: '1'
    }
  },
  data() {
    return {
      filterText: '',
      list: null,
      total: null,
      formEdit: true,
      formAdd: true,
      formStatus: '',
      dialogUserVisible: false,
      dialogUserName: '关联用户',
      dialogAuthorityVisible: false,
      dialogAuthorityName: '关联资源',
      listQuery: {
        groupType: this.type,
        name: undefined
      },
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'label'
      },
      labelPosition: 'right',
      groupManager_btn_edit: false,
      groupManager_btn_del: false,
      groupManager_btn_add: false,
      groupManager_btn_view: false,
      groupManager_btn_userManager: false,
      groupManager_btn_resourceManager: false,
      form: {
        code: undefined,
        name: undefined,
        description: undefined,
        groupType: this.type,
        system_name: null,
      },
      currentId: -1,
      treenodeId: null,
      systemlist: [],//所属系统
      dialogVisible1: false,
    }
  },
  watch: {
    filterText(val) {
      this.$refs.groupTree.filter(val);
    }
  },
  created() {
    this.getList();
    this.groupManager_btn_edit = this.elements['groupManager:btn_edit'];
    this.groupManager_btn_del = this.elements['groupManager:btn_del'];
    this.groupManager_btn_add = this.elements['groupManager:btn_add'];
    this.groupManager_btn_view = this.elements['groupManager:btn_view'];
    this.groupManager_btn_userManager = this.elements['groupManager:btn_userManager'];
    this.groupManager_btn_resourceManager = this.elements['groupManager:btn_resourceManager'];
  },
  computed: {
    ...mapGetters([
      'elements'
    ])
  },
  mounted() {
    let that = this;
    this.$refs.treedv.style.height = (document.documentElement.clientHeight - 229) + 'px';
    this.getqueryListdata();
  },
  methods: {
    getqueryListdata() {//所属系统
      let pams = {
        'typeId': 'system',
      }
      getqueryList(pams).then(data => {
        this.systemlist = data;
      });

    },
    getList() {
      fetchTree(this.listQuery).then(data => {
        this.treeData = data;
      });
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    getNodeData(data) {
      if (!this.formEdit) {
        this.formStatus = 'update';
      }
      getObj(data.id).then(response => {
        this.form = response.data;
      });
      // this.currentId = data.id;
      this.treenodeId = data.id
    },
    handlerEdit() {
      if (this.form.id) {
        this.formEdit = false;
        this.formStatus = 'update';
      }
    },
    handlerAdd() {
      this.resetForm();
      this.formEdit = false;
      this.formStatus = 'create';
    },
    handleDelete() {
      let that = this;
      if (that.treenodeId == null) {
        that.$message({
          showClose: true,
          message: "请选择删除的数据",
          type: "warning",
          duration: 1500
        });
        return;
      }
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let obj = {
          id: this.treenodeId
        }
        delGroup(obj).then((data) => {
          if (data.flag == '1') {
            that.$message({
              showClose: true,
              message: "删除成功",
              type: "success",
              duration: 1500
            });
            this.getList();
            this.resetForm();
            this.onCancel();
          } else {
            that.$message({
              showClose: true,
              message: "删除失败",
              type: "warning",
              duration: 1500
            });
          };

        });
      });
    },
    update() {
      let that = this;

      if (!this.form.name) {
        that.$message({
          showClose: true,
          message: "请输入名称",
          type: "warning",
          duration: 2500
        });
        return;
      }
      if (!this.form.system_name) {
        that.$message({
          showClose: true,
          message: "请选择所属系统",
          type: "warning",
          duration: 2500
        });
        return;
      }

      putObj(this.treenodeId, this.form).then(() => {
        this.getList();
        that.$message({
          showClose: true,
          message: "编辑成功",
          type: "success",
          duration: 1500
        });
      });
    },
    create() {
      let that = this;

      if (!this.form.name) {
        that.$message({
          showClose: true,
          message: "请输入名称",
          type: "warning",
          duration: 2500
        });
        return;
      }
      if (!this.form.system_name) {
        that.$message({
          showClose: true,
          message: "请选择所属系统",
          type: "warning",
          duration: 2500
        });
        return;
      }

      let obj = {
        'parentId': that.currentId,
        'name': that.form.name,
        'description': that.form.description,
        'groupType': '1',
        'system_name': that.form.system_name,
      }
      addObj(obj).then(() => {
        this.getList();
        that.$message({
          showClose: true,
          message: "创建成功",
          type: "success",
          duration: 1500
        });
      });
    },
    onCancel() {
      this.formEdit = true;
      this.formStatus = '';
    },
    resetForm() {
      this.form = {
        parentId: this.currentId,
        code: undefined,
        name: undefined,
        description: undefined,
        groupType: this.type,
        system_name: null,
      };
    },
    handlerUser(typ) {
      let that = this;
      if (!that.treenodeId) {
        that.$message({
          showClose: true,
          message: "请选择关联的数据",
          type: "warning",
          duration: 1500
        });
        return;
      }

      if (typ == '2') {
        that.dialogUserName = "关联用户"
      } else if (typ == '1') {
        that.dialogUserName = "查看用户"
      }
      this.dialogUserVisible = true;
      // this.$refs.groupUser.groupId = this.currentId;
      //  this.$refs.groupUser.initUsers();
      that.$nextTick(() => {
        that.$refs.groupUser.handlick(this.treenodeId, typ);
      });

    },
    handlerAuthority() {
      let that = this;
      if (!that.treenodeId) {
        that.$message({
          showClose: true,
          message: "请选择分配的数据",
          type: "warning",
          duration: 1500
        });
        return;
      }
      this.dialogAuthorityVisible = true;
      if (this.$refs.groupAuthority !== undefined) {
        this.$refs.groupAuthority.groupId = that.treenodeId;//this.currentId;
        this.$refs.groupAuthority.initAuthoritys();
      }
    },
    closeDiadl(typ) {
      let that = this;
      if (typ === "1") {
        that.dialogUserVisible = false;
        // console.log('取消')
      } else if (typ === "2") {//查询

      } else if (typ === "3") {//只查询并关闭
        that.dialogUserVisible = false;
      }
    },
    closeAuthorityDialog() {
      this.dialogAuthorityVisible = false;
    },
    closeAdd1() {
      this.$nextTick(() => {
        this.$refs.groupUser.handreset()
      })
    },
    handlerjizhan() {
      let that = this;
      if (!that.treenodeId) {
        that.$message({
          showClose: true,
          message: "请选择关联的数据",
          type: "warning",
          duration: 2500
        });
        return;
      }
      that.dialogVisible1 = true;
      that.$nextTick(() => {
        that.$refs.baseimport.handlick(this.treenodeId);
      });

    },
    closeDiadl1(typ) {
      let that = this;
      if (typ === "1") {
        that.dialogVisible1 = false;
        // console.log('取消')
      } else if (typ === "2") {//查询

      } else if (typ === "3") {//只查询并关闭
        that.dialogVisible1 = false;
      }
    },
  }
}
</script>
<style scoped lang="scss">
.departree {
  padding: 10px 5px;
  background-color: #fff;
  overflow: auto;
  border: solid 1px #EBEEF5;
  border-top: none;
}
</style>
<style lang="scss">
// @import "src/styles/common.scss";</style>
