import http from '@/api/http'
//告警窗口查询
export function getuserGrid(query) {
  return http({
    url: '/api/alarmdy/alarmGrid/userGrid',
    method: 'get',
    params: query
  });
}
//获取表格表头
export function getAlarclums(query,pams) {
    return http({
      url: '/api/alarmdy/alarmGridCol/clums/'+pams,
      method: 'get',
      params: query
    });
}
//告警数据 alarm/page/gridAct
export function getgridAct(query,pams,dat) {
  return http({
    url: '/api/alarmdy/alarm/gridAct/page/'+pams,
    method: 'get',
    params: query,
    'headers':dat,
  });
}

//告警等级数据 alarm/page/gridActlev
export function getgridlevAct(query,dat) {
  return http({
    url: '/api/alarmdy/alarm/gridActlev/page',
    method: 'get',
    params: query,
    'headers':dat,
  });
}
//告警窗口-列加载 

export function getallclums(pams) {
  return http({
    url: '/api/alarmdy/alarmGridCol/allclums/'+pams,
    method: 'get',
    // params: query,
  });
}
// 告警列表初始化
export function getalarmGridList(pams) {
  return http({
    url: '/api/alarmdy/alarmGrid/list',
    method: 'post',
    data: pams
  });
}
//告警过滤器数据源 
export function getqueryList(pams) {
  return http({
    url: '/api/alarmdy/alarmFilter/queryList',
    method: 'get',
    params: pams,
  });
}
// 告警窗口新增 
export function postaddalarmGrid(query,typ) {
  return http({
    url: '/api/alarmdy/alarmGrid',
    method: typ,
    data: query
  });
}
// 告警窗口删除接口
export function postdelealarmGrid(query) {
  return http({
    url: '/api/alarmdy/alarmGrid/batch/'+query,
    method: 'DELETE',
    data: query
  });
}
//告警窗口-布局
export function getuserconfig(pams) {
  return http({
    url: '/api/alarmdy/alarmGridLayout/userconfig',
    method: 'get',
    params: pams,
  });
}
//表头数据
export function postcolbatch(pams) {
  return http({
    url: '/api/alarmdy/alarmGridCol/batch',
    method: 'post',
    data: pams
  });
}
// 列行数据
export function postcolLayout(pams) {
  return http({
    url: '/api/alarmdy/alarmGridLayout',
    method: 'post',
    data: pams
  });
}
//告警窗口-排序
export function postconfigsort(pams) {
  return http({
    url: '/api/alarmdy/alarmGrid/configsort',
    method: 'post',
    data: pams
  });
}
// 告警策略查询
export function getAlarmOrderConf(pams) {
  return http({
    url: '/api/alarmdy/alarmOrderConf/getAlarmOrderConf',
    method: 'get',
    params: pams,
  });
}
//策略类型
export function finddistruorderCode(pams) {
  return http({
    url: '/api/alarmdy/alarmOrderConf/finddistruorderCode',
    method: 'get',
    params: pams,
  });
}
// 告警过滤器查询
export function getAlarmFilter(pams) {
  return http({
    url: '/api/alarmdy/alarmFilter/getAlarmFilter',
    method: 'get',
    params: pams,
  });
}
//告警过滤器删除   
export function delAlarmFilter(pams) {
  return http({
    url: '/api/alarmdy/alarmFilter/delAlarmFilter',
    method: 'get',
    params: pams,
  });
}
// 新增过滤器 
export function addNewAlarmFilter(pams) {
  return http({
    url: '/api/alarmdy/alarmFilter/addNewAlarmFilter',
    method: 'post',
    data: pams
  });
}
//站点分组或地址
export function getfindAlarmLevel(query) {
  return http({
    url: '/api/alarmdy/alarmActivity/findAlarmLevel',
    method: 'get',
    params: query
  });
}
//告警级别查询 
export function getFilterSta(pams) {
  return http({
    url: '/api/alarmdy/alarmFilter/getFilterSta',
    method: 'get',
    params: pams,
  });
}
//高铁场景 地市 
export function getCityAreaTree(pams) {
  return http({
    url: '/api/alarmdy/alarmOrderConf/getCityAreaTree',
    method: 'get',
    params: pams,
  });
}
//场景
export function getGroupTypeTree(pams) {
  return http({
    url: '/api/alarmdy/alarmOrderConf/getGroupTypeTree',
    method: 'get',
    params: pams,
  });
}



