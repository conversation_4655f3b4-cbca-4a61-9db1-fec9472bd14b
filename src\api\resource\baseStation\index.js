
import http from '@/api/http'
import {getSysUserId} from '@/utils/auth';
//基站级别
export function findStationLevel(query) {
  return http({
    url: '/api/alarmdy/stationInfo/findStationLevel',
    method: 'get',
    params: query
  });
}
//基站地市树
export function getStationRelaGroup(query) {
    return http({
      url: '/api/alarmdy/stationInfo/getStationRelaGroup',
      method: 'get',
      params: query
    });
}
//查询基站信息
export function getStationInfo(query) {
    return http({
      url: '/api/alarmdy/stationInfo/getStationInfo',
      method: 'get',
      params: query
    });
  }
//获取地市
export function getfindAreaList(query) {
  query = {};
  query['user_id'] = getSysUserId();
    return http({
      url: '/api/alarmdy/alarmActivity/findAreaList',
      method: 'get',
      params: query
    });
}
//获取区县
export function getfindCityArealist(query) {
    return http({
      url: '/api/alarmdy/alarmActivity/findCityArealist',
      method: 'get',
      params: query
    });
}
//获取网格
export function getfindGrid(query) {
    return http({
      url: '/api/alarmdy/alarmActivity/findGrid',
      method: 'get',
      params: query
    });
}
//获取基站
export function getfindStationInfo(query) {
  return http({
    url: '/api/alarmdy/alarmActivity/findStationInfo',
    method: 'get',
    params: query
  });
}
//派单状态
export function getfindPaiDanStatus(query) {
  return http({
    url: '/api/alarmdy/alarmActivity/findPaiDanStatus',
    method: 'get',
    params: query
  });
}
//告警级别
export function getfindAlarmLevel(query) {
  return http({
    url: '/api/alarmdy/alarmActivity/findAlarmLevel',
    method: 'get',
    params: query
  });
}
//告警状态
export function getfindAlarmStatus(query) {
  return http({
    url: '/api/alarmdy/alarmActivity/findAlarmStatus',
    method: 'get',
    params: query
  });
}
// 设备类型 alarmActivity/findDeviceType
export function getfindDeviceType(query){
  return http({
    url: '/api/alarmdy/alarmActivity/findDeviceType',
    method: 'get',
    params: query
  });
}
// 告警查询
export function postfindAlarmActivity(query) {
  return http({
    url: '/api/alarmdy/alarmActivity/findAlarmActivity',
    method: 'post',
    data: query
  });
}
//导出
export function postfindAlarmActivityToExcel(query) {
  return http({
    url: '/api/alarmdy/alarmActivity/findAlarmActivityToExcel',
    method: 'post',
    data: query
  });
}
//alarmActivity/findAlarmXml
export function getfindAlarmXml(query){
  return http({
    url: '/api/alarmdy/alarmActivity/findAlarmXml',
    method: 'post',
    data: query
  });
}
//基站批量 导入
export function getImportsExcel(query,url1){
  return http({
    url: url1,
    method: 'post',
    header: {
      "Content-Type": "application/x-www-form-urlencoded;charset=utf-8"
    },
    data: query
  });
}
//基站新增 stationInfo/addNewStationInfo
export function getaddNewStationInfo(query){
  return http({
    url: '/api/alarmdy/stationInfo/addNewStationInfo',
    method: 'get',
    params: query
  });
}
//  删除基站
export function deleteStationInfo(query){
  return http({
    url: '/api/alarmdy/stationInfo/deleteStationInfo',
    method: 'post',
    data: query
  });
}
//
export function getupdateStationInfo(query){
  return http({
    url: '/api/alarmdy/stationInfo/updateStationInfo',
    method: 'get',
    params: query
  });
}
//基站场景分组查询
export function getGroupImage(query){
  return http({
    url: '/api/alarmdy/stationInfo/getGroupImage',
    method: 'get',
    params: query
  });
}
//场景查询
export function getfindGroupType(query){
  return http({
    url: '/api/alarmdy/stationInfo/findGroupType',
    method: 'get',
    params: query
  });
}
// 新增分组
export function getaddNewStation(query){
  return http({
    url: '/api/alarmdy/stationInfo/addNewStation',
    method: 'get',
    params: query
  });
}
//  删除
export function deleteGroupInfo(query){
  return http({
    url: '/api/alarmdy/stationInfo/deleteGroupInfo',
    method: 'post',
    data: query
  });
}
//  查询当前已选基站
export function getSelectedStationByGroup(query){
  return http({
    url: '/api/alarmdy/stationInfo/getSelectedStationByGroup',
    method: 'get',
    params: query
  });
}
//  待选基站
export function getNotSelectStationByGroup(query){
  return http({
    url: '/api/alarmdy/stationInfo/getNotSelectStationByGroup',
    method: 'get',
    params: query
  });
}
//批量添加基站
export function addBatchStation(query){
  return http({
    url: '/api/alarmdy/stationInfo/addBatchStation',
    method: 'post',
    data: query
  });
}
//删除场景中的基站
export function delBatchStation(query){
  return http({
    url: '/api/alarmdy/stationInfo/delBatchStation',
    method: 'post',
    data: query
  });
}
//修改分组基本信息
export function updateStationGroup(query){
  return http({
    url: '/api/alarmdy/stationInfo/updateStationGroup',
    method: 'get',
    params: query
  });
}

export function getSourceList(){
  return http({
    url: '/api/flowable/sys/code/getSourceList',
    method: 'get'
  });
}

export function getCategoryList(){
  return http({
    url: '/api/flowable/sys/code/getCategoryList',
    method: 'get'
  });
}

export function getSubItemList(){
  return http({
    url: '/api/flowable/sys/code/getSubitemList',
    method: 'get'
  });
}
export function addSysCode(param){
  return http({
    url: '/api/flowable/sys/code/addSysCode',
    method: 'post',
    data: param
  });
}
export function deleteSysCode(param){
  return http({
    url: '/api/flowable/sys/code/deleteSysCode',
    method: 'post',
    data: param
  })
}

