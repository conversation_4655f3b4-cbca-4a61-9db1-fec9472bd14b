<template>
  <div class="app-container">
    <template v-if="showContent == 'list'">
      <el-form :inline="true" :model="queryForm" size="small" label-width="100px" class="demo-form-inline">
        <el-form-item label="地市:">
          <el-select v-model="queryForm.cityCode" :disabled="cityDisabled" @change="cityChange" filterable clearable
            placeholder="请选择">
            <el-option v-for="(item, index) in citylist" v-show="index !== 0" :key="item.cityId" :label="item.cityName"
              :value="item.cityId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="区域:">
          <el-select v-model="queryForm.areaCode" :disabled="areaDisabled" @change="areaChange" filterable clearable
            placeholder="请选择">
            <el-option v-for="item in areaList" :key="item.areaId" :label="item.areaName"
              :value="item.areaId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="局站:">
          <el-select v-model="queryForm.siteId" @change="siteChange" :remote-method="getSite" filterable clearable
            placeholder="请选择" @click="getSite">
            <el-option v-for="item in siteList" filterable :key="`${item.area_id}-${item.site_id}`"
              :label="item.site_name" :value="item.site_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="机房:">
          <el-select v-model="queryForm.roomId" :remote-method="getRoom" filterable clearable
            placeholder="请选择">
            <el-option v-for="item in roomlist" :key="`${item.room_id}-${item.site_id}`" :label="item.room_name"
              :value="item.room_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="SU_IP:">
          <el-input v-model="queryForm.suIp" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="FSU厂家:">
          <el-select v-model="queryForm.suVendor" filterable clearable
            placeholder="请选择">
            <el-option v-for="item in suVendorList" :key="item.suVendor_id" :label="item.suVendor_name"
              :value="item.suVendor_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-button style="margin-left: 60px;" @click="queryFormReset">重置</el-button>
          <el-button type="primary" @click="queryData(1, 10)">查询</el-button>
          <el-button type="primary" @click="devExport" :loading="exportloading">导出</el-button>
        </el-form-item>
      </el-form>
      <el-table border :data="deviceTableData" header-row-class-name="myHeaderClass" size="small" v-loading="tableLoading">
        <el-table-column prop="cityName" align="center" label="地市"></el-table-column>
        <el-table-column prop="areaName" align="center" label="区县" show-overflow-tooltip></el-table-column>
        <el-table-column prop="siteName" align="center" label="局站" show-overflow-tooltip></el-table-column>
        <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>

        <el-table-column prop="suIp" align="center" label="SU_IP" show-overflow-tooltip></el-table-column>
        <el-table-column prop="suId" align="center" label="mac地址" show-overflow-tooltip></el-table-column>
        <el-table-column prop="suVendor" align="center" label="FSU厂家" show-overflow-tooltip>
          <template slot-scope="scope">
            <div v-if="scope.row.suVendor == 'ZNV'">力维</div>
            <div v-if="scope.row.suVendor == 'SAIERCOM'">赛尔</div>
          </template>
        </el-table-column>
        <el-table-column prop="status" align="center" label="状态" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == 1" type="success">正常</el-tag>
            <el-tag v-if="scope.row.status == 2" type="danger">疑似退网</el-tag>
            <el-tag v-if="scope.row.status == 3" type="warning">已退网</el-tag>
          </template>
        </el-table-column>
        <el-table-column align="center" label="疑似退网天数" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.numDayRetire }}天
          </template>
        </el-table-column>
        <el-table-column label="确认已退网" align="center">
          <template slot-scope="scope">
            <el-button size="mini" style="color: #409EFF; border: none;" @click="confirmRetire(scope.row)">确认</el-button>
          </template>
        </el-table-column>
        <el-table-column prop="registTime" align="center" label="注册时间" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.registTime) }}
          </template>
        </el-table-column>
        
      </el-table>
      <div class="pag">
        <el-pagination @size-change="handleSizeChange" background @current-change="handleCurrentChange"
          :current-page="currentPage" :page-sizes="[10, 20, 50]" :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="total">
        </el-pagination>
      </div>
    </template>


  </div>
</template>

<script>
import exportTableToExcel from './components/exportExcel.js'
export default {
  data() {
    return {
      // 查询条件下拉数据
      citylist: [],
      areaList: [],
      siteList: [],
      roomlist: [],
      suVendorList: [
        { suVendor_id: 'ZNV', suVendor_name: '力维' },
        { suVendor_id: 'SAIERCOM', suVendor_name: '赛尔' }
      ],
      tableLoading: false,
      queryForm: {
        areaCode: '',
        cityCode: '',
        roomId: '',
        siteId: '',
        suIp: '',
        suVendor: ''
      },
      deviceTableData: [],
      pageSize: 10,
      currentPage: 1,
      total: 0,

      exportloading: false,
      role: '',
      cityDisabled: false,
      areaDisabled: false,

      showContent: 'list', // list 一级列表
    }
  },
  created() {

    this.queryCityList()
    this.getSite()
    this.getRoom()

    this.queryData()
  },
  methods: {
    // 确认已退网
    confirmRetire(row){
      console.log("🚀 ~ confirmRetire ~ row:", row)
      const params = {
        suId: row.suId,
        type: 'FSU'
      }
      this.$api.checkRetire(params).then(res => { 
        if (res.code === 200) {
          this.$message.success('已确认')
        } else {
          this.$message.error('确认失败')
        }
      })
    },
    // 格式化时间
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      const date = new Date(dateTime)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },

    // FSU设备导出
    async devExport() {
      this.exportloading = true
      let data = [
        ['地市', '区域', '局站', '机房', 'SU_IP', 'mac地址', 'FSU厂家', '状态', '疑似退网天数', '注册时间']
      ]
      let params = {
        ...this.queryForm,
        pageNum: 1,
        pageSize: this.total
      }
      try {
        let res = await this.$api.checkFsu(params)
        if (res.code == 200) {
          res.data.records.forEach(v => {
            let item = [
              v.cityName,
              v.areaName,
              v.siteName,
              v.roomName,
              v.suIp,
              v.suId,
              v.suVendor == 'ZNV' ? '力维' : v.suVendor == 'SAIERCOM' ? '赛尔' : v.suVendor,
              v.status == 1 ? '正常' : v.status == 2 ? '疑似退网' : v.status == 3 ? '已退网' : v.status,
              v.numDayRetire,
              this.formatDateTime(v.registTime)
            ]
            data.push(item)
          })
          exportTableToExcel(data, 'FSU疑似退网表')
          this.exportloading = false
        } else {
          this.$message.error(res.msg)
          this.exportloading = false
        }
      } catch (error) {
        this.$message.error('导出异常')
        this.exportloading = false
      }
    },
    // 初始化地市
    queryCityList() {
      let params = {
        cityName: "",
        pageNum: 1,
        pageSize: 999
      }
      this.$api.queryCityList(params).then(res => {
        if (res.code == 200) {
          this.citylist = res.data.records
        }
      })
    },
    cityChange(val) {
      this.queryForm.areaCode = ''
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.areaList = []
      this.siteList = []
      this.roomlist = []
      this.areaList = this.citylist.find(v => v.cityId == val).areaList
      this.getSite()
      this.getRoom()
    },
    areaChange() {
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.siteList = []
      this.roomlist = []
      this.getSite()
      this.getRoom()
    },
    siteChange() {
      this.queryForm.roomId = ''
      this.roomlist = []
      this.getRoom()
    },


    getSite(val) {
      let params = {
        cityId: this.queryForm.cityCode,
        areaId: this.queryForm.areaCode,
        siteName: val,
        pageNum: 1,
        pageSize: 999
      }

      this.$api.querySiteInfo(params).then(res => {
        if (res.code == 200) {
          this.siteList = res.data.records
        }
      })
    },
    getRoom(val) {
      let params = {
        cityId: this.queryForm.cityCode,
        areaId: this.queryForm.areaCode,
        siteId: this.queryForm.siteId,
        roomName: val,
        pageNum: 1,
        pageSize: 999
      }
      this.$api.queryRoomInfo(params).then(res => {
        if (res.code == 200) {
          this.roomlist = res.data
        }
      })
    },
    queryFormReset() {
      this.areaList = []
      this.siteList = []
      this.roomlist = []
      this.queryForm = {
        areaCode: '',
        cityCode: '',
        roomId: '',
        siteId: '',
        suIp: '',
        suVendor: ''
      }
      this.role = sessionStorage.getItem('role')
      this.queryForm.cityCode = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
      this.queryForm.areaCode = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''

      if (this.role == '1') {
        this.cityDisabled = false
        this.areaDisabled = false
      }

      if (['94', '95'].includes(this.role)) {
        this.cityDisabled = true
      }
      this.queryCityList()
      this.getSite()
      this.getRoom()
      this.queryData()
    },
    // 一级列表
    queryData(pageNum, pageSize) {
      this.tableLoading = true
      if (pageNum) this.currentPage = pageNum
      if (pageSize) this.pageSize = pageSize
      let params = {
        ...this.queryForm,
        pageNum: pageNum || this.currentPage,
        pageSize: pageSize || this.pageSize
      }
      this.$api.checkFsu(params).then(res => {
        if (res.code == 200) {
          this.deviceTableData = res.data.records
          this.tableLoading = false
          this.total = res.data.total
        } else {
          this.tableLoading = false
          this.$message.error(res.msg)
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.queryData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.queryData()
    },

  }
}
</script>

<style lang="scss" scoped>
.pag {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 12px;
}
</style>