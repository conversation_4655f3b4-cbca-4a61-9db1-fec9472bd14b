<template>
<div style="width: 100%;min-width: 880px;">

  <el-form :inline="true" :model="queryForm" size="small" label-width="100px" class="demo-form-inline">
    <el-form-item label="地市:">
      <el-select :disabled="cityDisabled" v-model="queryForm.cityId" @change="cityChange" filterable clearable
        placeholder="请选择">
        <el-option v-for="(item, index) in citylist" v-show="item.cityName !== '陕西省'" :key="`${index}-${item.cityId}`"
          :label="item.cityName" :value="item.cityId"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="区域:">
      <el-select :disabled="areaDisabled" v-model="queryForm.areaId" @change="areaChange" :remote-method="getArea"
        filterable clearable placeholder="请选择">
        <el-option v-for="(item, index) in areaList" :key="`${index}-${item.areaId}`" :label="item.areaName"
          :value="item.areaName"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="局站:">
      <el-select v-model="queryForm.siteId" @change="siteChange" :remote-method="getSite" filterable clearable
        placeholder="请选择">
        <el-option v-for="(item, index) in siteList" filterable :key="`${index}-${item.siteId}`" :label="item.siteName"
          :value="item.siteId"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="机房:">
      <el-select v-model="queryForm.roomId" @change="roomChange" :remote-method="getRoom" filterable clearable
        placeholder="请选择">
        <el-option v-for="(item, index) in roomlist" :key="`${index}-${item.roomId}`" :label="item.roomName"
          :value="item.roomId"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="设备类型:">
      <el-select v-model="queryForm.deviceType" @change="devTypeChange" :remote-method="getDeviceType" filterable remote
        clearable placeholder="请输入">
        <el-option v-for="(item, index) in deviceTypeList" :key="`${index}-${item.id}`" :label="item.label"
          :value="item.id"></el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="设备名称:">
      <el-select v-model="queryForm.deviceName" filterable remote clearable placeholder="请输入"
        :remote-method="getDeviceName">
        <el-option v-for="(item, index) in devoptions" :key="`${index}-${item.id}-${item.label}`" :label="item.label"
          :value="item.label">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="告警标题:">
      <!-- alarmTitleList: [], -->
      <el-select v-model="queryForm.alarmDesc" filterable remote clearable placeholder="请输入"
        :remote-method="getAlarmTitle">
        <el-option v-for="(item, index) in alarmTitleList" :key="`${index}-${item}`" :label="item" :value="item">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="告警级别:">
      <el-select v-model="queryForm.alarmLevel" placeholder="请选择" clearable>
        <el-option v-for="item in warnLevelOptions" :key="item.level" :label="item.name" :value="item.level">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="所属厂家:">
      <el-select v-model="queryForm.suVendor" placeholder="请选择" clearable>
        <el-option v-for="item in belonging" :key="item.level" :label="item.name" :value="item.level">
        </el-option>
      </el-select>
    </el-form-item>
    <el-form-item label="时间：">
      <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="date" type="datetimerange" range-separator="至"
        start-placeholder="开始日期" end-placeholder="结束日期" @change="changeParams">
      </el-date-picker>
    </el-form-item>
    <el-form-item label="">
      <el-input v-model="hour" placeholder="请输入整数" type="number" style="width: 120px;"/>小时未恢复
    </el-form-item>
    <el-form-item label="">
      <el-button style="margin-left: 80px;" @click="queryFormReset">重置</el-button>
      <el-button type="primary" @click="queryData(1, this.pageSize)">查询</el-button>
      <!-- <el-button type="primary" @click="exportExcel">导出</el-button> -->
      <el-button type="primary" @click="waringExport" :loading="exportloading">导出</el-button>

    </el-form-item>
  </el-form>
  <!-- <transition-group name="fade" tag="tbody"> -->
  <el-table 
    :data="displayData"
    key="table" 
    class="myWarnTable" 
    :row-style="rowStyleFn" 
    :cell-class-name="warnLevelClassFn" 
    style="width: 100%;"
    size="small" 
    v-loading="tableLoading" 
    height="60vh"
    ref="dataTable"
    border
  >
    <el-table-column fixed prop="cityName" align="center" label="地市" show-overflow-tooltip></el-table-column>
    <el-table-column fixed prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
    <el-table-column fixed prop="stationName" align="center" label="局站" show-overflow-tooltip></el-table-column>
    <el-table-column fixed prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
    <el-table-column prop="deviceTypeName" align="center" label="设备类型" show-overflow-tooltip></el-table-column>
    <el-table-column prop="deviceName" align="center" label="设备名称" show-overflow-tooltip></el-table-column>
    <el-table-column prop="suVendor" align="center" label="厂家" width="50" show-overflow-tooltip>
      <template slot-scope="scope">
        {{ belonging.find(v => v.level === scope.row.suVendor)?.name || scope.row.suVendor }}
      </template>
    </el-table-column>
    <el-table-column prop="alarmLevel" align="center" label="告警级别">
      <template slot-scope="scope">
        {{warnLevelOptions.find(v => v.level == scope.row.alarmLevel)?.name || "六级告警"}}
      </template>

    </el-table-column>
    <el-table-column prop="alarmTime" align="center" label="告警发生时间" show-overflow-tooltip></el-table-column>
    <!-- <el-table-column prop="alarmClearTime" align="center" label="告警清除时间"></el-table-column> -->
    <!-- <el-table-column prop="alarmDuration" align="center" label="告警历时（分）"></el-table-column> -->
    <!-- <el-table-column prop="alarmDesc" align="center" label="告警标题" show-overflow-tooltip></el-table-column> -->
    <el-table-column prop="alarmDesc" align="center" label="告警描述" show-overflow-tooltip></el-table-column>
    <el-table-column prop="triggerVal" align="center" label="触发值"></el-table-column>
    <el-table-column align="center" label="操作">
      <template slot-scope="scope">
        <el-button style="color: skyblue; border:none" size="mini" @click="handleClear(scope.row)">手工清除</el-button>
      </template>
    </el-table-column>
  </el-table>
  <!-- </transition-group> -->

  <!-- 滚动懒加载模式下的加载提示 -->
  <div v-if="isLazyLoading" class="lazy-loading-status">
    <div v-if="isLoadingMore" class="loading-more">
      <i class="el-icon-loading"></i> 正在加载更多数据...
    </div>
    <div v-else-if="!hasMore && displayData.length > 0" class="no-more-data">
      已加载全部数据 (共 {{total}} 条)
    </div>
    <div v-else-if="displayData.length > 0" class="scroll-tip">
      已加载 {{displayData.length}} / {{total}} 条数据，向下滚动加载更多
    </div>
  </div>

  <!-- 传统分页模式 -->
  <div v-else class="pag" v-if="deviceTableData.length">
    <el-pagination @size-change="handleSizeChange" background @current-change="handleCurrentChange"
      :current-page="currentPage" :page-sizes="[10, 20, 50]" :page-size="pageSize"
      layout="total, sizes, prev, pager, next, jumper" :total="total">
    </el-pagination>
  </div>

</div>
</template>

<script>
import dayjs from 'dayjs'
import { set } from 'lodash';
import exportTableToExcel from './exportExcel.js'
export default {
  data() {
    return {
      dateStatus: 0,
      warnLevelOptions: [
        { level: 1, name: '一级告警' },
        { level: 2, name: '二级告警' },
        { level: 3, name: '三级告警' },
        { level: 4, name: '四级告警' },
        { level: 5, name: '五级告警' },
        { level: 6, name: '六级告警' },
      ],
      belonging: [
        { level: 'ZNV', name: '力维' },
        { level: 'SAIERCOM', name: '赛尔' },
      ],
      showDetail: false,
      exportDetailloading: false,
      exportloading: false,
      // 查询条件下拉数据
      citylist: [],
      areaList: [],
      siteList: [],
      roomlist: [],
      deviceTypeList: [],
      cedianlist: [],
      cedianNameList: [],
      devoptions: [],
      alarmTitleList: [],
      tableLoading: false,
      queryForm: {
        alarmDesc: '',
        alarmLevel: '',
        areaId: '',
        cityId: '',
        deviceName: '',
        roomId: '',
        siteId: '',
        deviceTypeCode: '',
        suVendor: '',
        period:'',
      },
      hour: '',
      date: [dayjs().subtract('30', 'day').format("YYYY-MM-DD HH:mm:ss"), dayjs().format("YYYY-MM-DD HH:mm:ss")],

      deviceTableData: [], // 保留用于轮询更新
      currentPageData: [], // 当前页显示的数据
      displayData: [], // 滚动懒加载显示的数据
      pageSize: 20, // 每次加载的数据量
      currentPage: 1,
      total: 0,
      isLazyLoading: true, // 懒加载标识，默认启用
      hasMore: true, // 是否还有更多数据
      isLoadingMore: false, // 是否正在加载更多数据

      deviceDetailTableData: [],
      detailLoading: false,
      detailPageSize: 10,
      detailCurrentPage: 1,
      detailTotal: 0,
      content: '',
      currentRow: null,
      clearType: '',
      role: '',
      cityDisabled: false,
      areaDisabled: false,

      firstTime: '',
      secondTime: '',
      timer: null,
      alarmUniqueId: '',
    }
  },
  watch: {
    '$route.query': {
      //immediate: true, // 如果需要在组件创建时立即触发，设置为true
      handler(newVal, oldVal) {
        // 当路由参数变化时，这里会被调用
        // newVal 是新的参数，oldVal 是旧的参数
        // console.log('Route parameter changed:', newVal);
        // 你可以在这里根据新的参数执行相应的操作
        console.log(newVal)
        this.type = newVal?.type || ''
        //this.typeId = newVal?.id || ''
        clearTimeout(this.timer)
        this.timer = null

        this.queryForm.cityId = newVal?.cityId || ''
        this.queryForm.areaId = newVal?.areaId || ''
        this.queryForm.siteId = newVal?.siteId || ''
        this.queryForm.roomId = newVal?.roomId || ''
        this.date = [dayjs().subtract('30', 'day').format("YYYY-MM-DD HH:mm:ss"), dayjs().format("YYYY-MM-DD HH:mm:ss")]

        if (this.queryForm.cityId) {
          let params = {
            cityId: this.queryForm.cityId,
            pageNum: 1,
            pageSize: 999
          }
          this.$api.getAlarmArealist(params).then(res => {
            if (res.code == 200) {
              this.areaList = res.data.records
            }
          })
        }

        if (this.queryForm.areaId) {
          let temp;
          try {
            temp = this.areaList.filter(i => {
              return i.areaName == this.queryForm.areaId
            })
            // this.queryForm.areaId = temp[0].areaId
          } catch (error) {

          }
          let params = {
            cityId: this.queryForm.cityId,
            areaId: temp[0].areaId,
            pageNum: 1,
            pageSize: 999
          }

          this.$api.getAlarmSitelist(params).then(res => {
            if (res.code == 200) {
              this.siteList = res.data.records
            }
          })
        }
        if (this.queryForm.siteId) {
          let temp;
          try {
            temp = this.areaList.filter(i => {
              return i.areaName == this.queryForm.areaId
            })
            // this.queryForm.areaId = temp[0].areaId
          } catch (error) {

          }
          let params = {
            cityId: this.queryForm.cityId,
            areaId: temp[0].areaId,
            siteId: this.queryForm.siteId,
            pageNum: 1,
            pageSize: 999
          }
          this.$api.getAlarmRoomlist(params).then(res => {
            if (res.code == 200) {
              this.roomlist = res.data.records
            }
          })
        }

        // 根据当前模式进行查询
        if (this.isLazyLoading) {
          this.queryData(1, this.pageSize, true)
        } else {
          this.queryData()
        }

      }
    },
    'hour'(val) {
      console.log(val);
      this.queryForm.period = val*3600+''
    }
  },
  created() {
    this.role = sessionStorage.getItem('role')
    this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
    this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''

    if (this.role == '1') {
      this.cityDisabled = false
      this.areaDisabled = false
    }

    if (['94', '95'].includes(this.role)) {
      this.cityDisabled = true
      this.areaDisabled = this.queryForm.areaId ? true : false
    }



    this.queryCityList()
    this.getArea()
    this.getSite()
    this.getRoom()
    this.getDeviceType()
    this.getDeviceName()
    this.getAlarmTitle()
    // 默认启用懒加载模式进行查询
    this.queryData(1, this.pageSize, true)
  },
  mounted() {
    const keys = ['cityId', 'areaId', 'siteId', 'roomId'];
    keys.forEach(key => {
      this.queryForm[key] = this.$route.query[key] || '';
    });

    this.role = sessionStorage.getItem('role')
    if (sessionStorage.getItem('role') == '94') {
      this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
      this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''
      this.type = this.$route.query.type || ''
    }

    // 添加滚动监听
    this.$nextTick(() => {
      if (this.$refs.dataTable && this.$refs.dataTable.$el) {
        const tableBody = this.$refs.dataTable.$el.querySelector('.el-table__body-wrapper')
        if (tableBody) {
          tableBody.addEventListener('scroll', this.handleTableScroll)
        }
      }
    })
  },
  beforeDestroy() {
    clearTimeout(this.timer)
    this.timer = null

    // 移除滚动监听
    if (this.$refs.dataTable && this.$refs.dataTable.$el) {
      const tableBody = this.$refs.dataTable.$el.querySelector('.el-table__body-wrapper')
      if (tableBody) {
        tableBody.removeEventListener('scroll', this.handleTableScroll)
      }
    }
  },
  methods: {
    // 手工清除
    handleClear(row){
      console.log("🚀 ~ handleClear ~ row:", row)
      // clearAlarm
      // this.alarmUniqueId = row.alarmUniqueId
      this.$confirm('是否确认清除此条告警?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => { 
        this.$api.clearAlarm({alarmUniqueId:row.alarmUniqueId})
        
      })
    },
    /**
     * 处理表格滚动事件
     */
    handleTableScroll(event) {
      if (!this.isLazyLoading || this.isLoadingMore || !this.hasMore) {
        return
      }

      const { scrollTop, scrollHeight, clientHeight } = event.target
      // 当滚动到距离底部50px时开始加载
      if (scrollTop + clientHeight >= scrollHeight - 50) {
        this.loadMoreData()
      }
    },

    /**
     * 加载更多数据
     */
    async loadMoreData() {
      if (this.isLoadingMore || !this.hasMore) {
        return
      }

      this.isLoadingMore = true

      try {
        const nextPage = this.currentPage + 1
        await this.loadPageData(nextPage)
      } catch (error) {
        this.$message.error('加载更多数据失败')
      } finally {
        this.isLoadingMore = false
      }
    },

    rowStyleFn({ row }) {
      return 'myRowStyle'
    },
    warnLevelClassFn({ row, column }) {
      // console.log(row,column)
      if (column.property == 'alarmLevel') {
        if (row.alarmLevel == "1") {
          return 'warn1'
        }
        if (row.alarmLevel == "2") {
          return 'warn2'
        }
        if (row.alarmLevel == "3") {
          return 'warn3'
        }
      }
    },

    // 初始化地市
    queryCityList() {
      let params = {
        cityName: "",
        pageNum: 1,
        pageSize: 999
      }
      this.$api.getAlarmCitylist(params).then(res => {
        if (res.code == 200) {
          this.citylist = res.data.records
        }
      })
    },
    cityChange(val) {
      this.queryForm.areaId = ''
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.queryForm.deviceName = ''
      this.areaList = []
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      this.devoptions = []
      this.getArea()
      this.getSite()
      this.getRoom()
      this.getDeviceName()
      this.getDeviceType()
    },
    areaChange() {
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.queryForm.deviceName = ''
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      this.devoptions = []
      this.getSite()
      this.getRoom()
      this.getDeviceName()
      this.getDeviceType()
    },
    siteChange() {
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.queryForm.deviceName = ''
      this.roomlist = []
      this.deviceTypeList = []
      this.devoptions = []
      this.getRoom()
      this.getDeviceName()
      this.getDeviceType()
    },
    roomChange() {
      this.queryForm.deviceType = ''
      this.queryForm.deviceName = ''
      this.deviceTypeList = []
      this.devoptions = []
      this.getDeviceName()
      this.getDeviceType()
    },
    devTypeChange() {
      this.queryForm.deviceName = ''
      this.devoptions = []
      this.getDeviceName()
    },
    changeParams() { 
      this.dateStatus = 1;
      clearTimeout(this.timer)
      this.timer = null;
    },
    getArea(val) {
      this.queryForm.areaId = ''
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.areaList = []
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      let params = {
        cityId: this.queryForm.cityId,
        areaName: val,
        pageNum: 1,
        pageSize: 999
      }
      this.$api.getAlarmArealist(params).then(res => {
        if (res.code == 200) {
          this.areaList = res.data.records
        }
      })
    },
    getSite(val) {
      // this.queryForm.siteId = ''
      // this.queryForm.roomId = ''
      // this.queryForm.deviceType = ''
      // this.siteList = []
      // this.roomlist = []
      // this.deviceTypeList = []
      // let temp;
      // try {
      //   temp = this.areaList.filter(i => {
      //     return i.areaName == this.queryForm.areaId
      //   })
      //   // this.queryForm.areaId = temp[0].areaId
      // } catch (error) {

      // }
      let params = {
        cityId: this.queryForm.cityId,
        areaId: this.queryForm.areaId,
        siteName: val,
        pageNum: 1,
        pageSize: 999
      }

      this.$api.getAlarmSitelist(params).then(res => {
        if (res.code == 200) {
          this.siteList = res.data.records
        }
      })
    },
    getRoom(val) {
      // this.queryForm.roomId = ''
      // this.queryForm.deviceType = ''
      // this.roomlist = []
      // this.deviceTypeList = []
      // let temp;
      // try {
      //   temp = this.areaList.filter(i => {
      //     return i.areaName == this.queryForm.areaId
      //   })
      //   // this.queryForm.areaId = temp[0].areaId
      // } catch (error) {

      // }
      let params = {
        cityId: this.queryForm.cityId,
        areaId: this.queryForm.areaId,
        siteId: this.queryForm.siteId,
        roomName: val,
        pageNum: 1,
        pageSize: 999
      }
      this.$api.getAlarmRoomlist(params).then(res => {
        if (res.code == 200) {
          this.roomlist = res.data.records
        }
      })
    },
    getAlarmTitle(val) {
      let params = {
        columnName: 'alarm_desc',
        columnValue: val,
        pageNum: 1,
        pageSize: 999
      }
      this.$api.getAlarmColumnNamelist(params).then(res => {
        if (res.code == 200) {
          this.alarmTitleList = res.data.records
        }
      })
    },
    async getDeviceName(queryString) {
      // let temp;
      // try {
      //   temp = this.areaList.filter(i => {
      //     return i.areaName == this.queryForm.areaId
      //   })
      //   // this.queryForm.areaId = temp[0].areaId
      // } catch (error) {

      // }
      let res = await this.$api.deviceNameQuery({
        deviceName: queryString,
        cityId: this.queryForm.cityId ? [this.queryForm.cityId] : [],
        areaId: this.queryForm.areaId ? [this.queryForm.areaId] : [],
        siteId: this.queryForm.siteId ? [this.queryForm.siteId] : [],
        roomId: this.queryForm.roomId ? [this.queryForm.roomId] : [],
        deviceType: this.queryForm.deviceType ? [this.queryForm.deviceType] : [],
        current: 1,
        size: 999
      })
      if (res.code == 200) {
        this.devoptions = res.data.records
      } else {
        this.$message.error(res.msg)

      }
    },
    async getDeviceType(val) {
      // let temp;
      // try {
      //   temp = this.areaList.filter(i => {
      //     return i.areaName == this.queryForm.areaId
      //   })
      //   // this.queryForm.areaId = temp[0].areaId
      // } catch (error) {

      // }
      let res = await this.$api.queryDeviceType({
        deviceType: val,
        cityId: this.queryForm.cityId ? [this.queryForm.cityId] : [],
        areaId: this.queryForm.areaId ? [this.queryForm.areaId] : [],
        siteId: this.queryForm.siteId ? [this.queryForm.siteId] : [],
        roomId: this.queryForm.roomId ? [this.queryForm.roomId] : [],
        current: 1,
        size: 999
      })
      if (res.code == 200) {
        this.deviceTypeList = res.data.records
      } else {
        this.$message.error(res.msg)
      }
    },
    queryFormReset() {
      this.areaList = []
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      this.devoptions = []
      this.dateStatus = 0

      this.queryForm = {
        alarmDesc: '',
        alarmLevel: '',
        areaId: '',
        cityId: '',
        deviceName: '',
        roomId: '',
        siteId: '',
        period: '',
      },
      this.hour = ''
      this.date = [dayjs().subtract('30', 'day').format("YYYY-MM-DD HH:mm:ss"), dayjs().format("YYYY-MM-DD HH:mm:ss")]
      this.role = sessionStorage.getItem('role')
      this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
      this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''

      if (this.role == '1') {
        this.cityDisabled = false
        this.areaDisabled = false
      }

      if (['94', '95'].includes(this.role)) {
        this.cityDisabled = true
      }
      this.queryCityList()
      this.getArea()
      this.getSite()
      this.getRoom()
      this.getDeviceType()
      this.getDeviceName()
      this.getAlarmTitle()

      // 根据当前模式进行查询
      if (this.isLazyLoading) {
        this.queryData(1, this.pageSize, true)
      } else {
        this.queryData()
      }

    },

    /**
     * 获取增量数据
     * @param {date} firstTime 第一次请求时间
     * @param {date} secondTime 第二次请求时间
     */
    queryAddData(firstTime, secondTime) {

      let params = {
        lastTime: firstTime,
        currentTime: secondTime,
        type: "ADD"
      }
      this.firstTime = this.secondTime
      this.$api.addAlarm(params).then(res => {
        if (res.code == 200) {
          // suId + deviceId + serialNo + alarmFlag
          console.log('----------------------------------------------------------------------------')
          console.log('当前的数据长度：' + this.deviceTableData.length)
          console.log('新增的数据长度：' + res.data.active.length)
          this.deviceTableData = [...res.data.active, ...this.deviceTableData]

          if (res.data.clear.length) {
            let clearList = res.data.clear.map(v => v.alarmUniqueId)
            console.log('接口返回的需要被清除的alarmUniqueId：' + clearList.length)
            console.log('实际被清除的alarmUniqueId：' + this.deviceTableData.filter(v => clearList.includes(v.alarmUniqueId)).length)
            this.deviceTableData = this.deviceTableData.filter(v => !clearList.includes(v.alarmUniqueId))
          }
          this.deviceTableData.sort((a, b) => {
            // 首先根据告警级别排序，1级到5级
            if (a.alarmLevel !== b.alarmLevel) {
              return a.alarmLevel - b.alarmLevel;
            }
            // 如果告警级别相同，则根据时间倒序排序
            return new Date(b.alarmTime) - new Date(a.alarmTime);
          });
          console.log('清除后的数据长度：' + this.deviceTableData.length)

          this.total = this.deviceTableData.length
        } else {
          this.$message.error("增量查询出错")
        }
      })
    },
    // startPolling() {
    //   this.timer = null
    //   if (this.timer === null) {
    //     this.secondTime = dayjs().format("YYYY-MM-DD HH:mm:ss")
    //     // this.queryAddData(this.firstTime, this.secondTime)
    //     if (this.date.length) {
    //       // let date = dayjs(this.date[1]).add(2, 'minute').format("YYYY-MM-DD HH:mm:ss")
    //       this.$set(this.date, 1, this.secondTime)
    //     }
    //     this.queryAllData()
    //     // this.timer = setTimeout(this.startPolling, 2 * 60 * 1000);
    //     this.timer = setTimeout(this.startPolling, 10 * 1000);
    //   }
    // },
    startPolling() {
      // 先清除之前的定时器
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }
      
      // 检查dateStatus，如果为1则停止轮询
      if (this.dateStatus === 1) {
        return
      }
      
      this.secondTime = dayjs().format("YYYY-MM-DD HH:mm:ss")
      if (this.date.length) {
        this.$set(this.date, 1, this.secondTime)
      }
      this.queryAllData()
      
      // 设置新的定时器
      this.timer = setTimeout(() => {
        this.startPolling()
      }, 10 * 1000)
    },
    queryAllData() {
      // 如果是懒加载模式，不进行轮询更新
      if (this.isLazyLoading) {
        return
      }

      let temp;
      try {
        temp = this.areaList.filter(i => {
          return i.areaName == this.queryForm.areaId
        })
        // this.queryForm.areaId = temp[0].areaId
      } catch (error) {

      }
      let params = {
        ...this.queryForm,
        ...temp[0],
        startReportTime: this.dateStatus == 1 ? this.date ? this.date[0] : '' : '',
        endReportTime: this.dateStatus == 1 ? this.date ? this.date[1] : '' : '',
        // current: pageNum || this.currentPage,
        // size: pageSize || this.pageSize,
        type: "ALL"
      }
      this.$api.addAlarm(params).then(res => {
        if (res.code == 200) {
          this.deviceTableData = res.data.active.sort((a, b) => {
            // 首先根据告警级别排序，1级到5级
            if (a.alarmLevel !== b.alarmLevel) {
              return a.alarmLevel - b.alarmLevel;
            }
            // 如果告警级别相同，则根据时间倒序排序
            return new Date(b.alarmTime) - new Date(a.alarmTime);
          });
          this.total = res.data.active.length

          // 更新当前页显示数据
          this.updateCurrentPageData()

        } else {
          this.$message.error(res.msg)
        }
      })
    },
    /**
     * 加载指定页的数据
     * @param {number} pageNum 页数
     * @param {boolean} isAppend 是否追加到现有数据
     */
    async loadPageData(pageNum, isAppend = true) {
      let temp;
      try {
        temp = this.areaList.filter(i => {
          return i.areaName == this.queryForm.areaId
        })
      } catch (error) {
        // 错误处理
      }

      let params = {
        cityId: this.queryForm.cityId,
        areaId: temp && temp[0] ? temp[0].areaId : this.queryForm.areaId,
        siteId: this.queryForm.siteId,
        roomId: this.queryForm.roomId,
        deviceName: this.queryForm.deviceName,
        alarmDesc: this.queryForm.alarmDesc,
        alarmLevel: this.queryForm.alarmLevel,
        suVendor: this.queryForm.suVendor,
        createTimeStart: this.dateStatus == 1 ? this.date ? this.date[0] : '' : '',
        createTimeEnd: this.dateStatus == 1 ? this.date ? this.date[1] : '' : '',
        current: pageNum,
        size: this.pageSize
      }


      const res = await this.$api.activeAlarm(params)
      if (res.code == 200) {
        const responseData = res.data.records || []
        const totalCount = res.data.total || 0

        const sortedData = responseData.sort((a, b) => {
          if (a.alarmLevel !== b.alarmLevel) {
            return a.alarmLevel - b.alarmLevel;
          }
          return new Date(b.alarmTime) - new Date(a.alarmTime);
        });

        if (isAppend && pageNum > 1) {
          // 追加数据
          this.displayData = [...this.displayData, ...sortedData]
        } else {
          // 重置数据
          this.displayData = sortedData
        }

        this.currentPage = pageNum
        this.total = totalCount
        this.hasMore = this.displayData.length < totalCount

        return sortedData
      } else {
        throw new Error(res.msg || '请求失败')
      }
    },

    /**
     * 监控量查询 - 支持懒加载
     * @param {number} pageNum 页数
     * @param {number} pageSize 每页大小
     */
    queryData(pageNum = 1, pageSize = this.pageSize) {
      this.tableLoading = true

      // 在每次查询前先清除现有定时器
      if (this.timer) {
        clearTimeout(this.timer)
        this.timer = null
      }

      if (this.isLazyLoading) {
        // 滚动懒加载模式：重置数据并加载第一页
        this.displayData = []
        this.currentPage = 0
        this.hasMore = true
        this.loadPageData(1, false).then(() => {
          this.tableLoading = false
        }).catch(error => {
          this.tableLoading = false
          this.$message.error('查询失败')
          console.error('查询错误:', error)
        })
        return
      }

      // 非懒加载模式：保持原有逻辑
      this.deviceTableData = []
      this.currentPageData = []
      this.currentPage = 1

      let temp;
      try {
        temp = this.areaList.filter(i => {
          return i.areaName == this.queryForm.areaId
        })
      } catch (error) {
        // 错误处理
      }

      let params;

      if (this.isLazyLoading) {
        // 懒加载模式：使用activeAlarm接口的参数格式
        params = {
          cityId: this.queryForm.cityId,
          areaId: temp && temp[0] ? temp[0].areaId : this.queryForm.areaId,
          siteId: this.queryForm.siteId,
          roomId: this.queryForm.roomId,
          deviceName: this.queryForm.deviceName,
          alarmDesc: this.queryForm.alarmDesc,
          alarmLevel: this.queryForm.alarmLevel,
          suVendor: this.queryForm.suVendor,
          createTimeStart: this.dateStatus == 1 ? this.date ? this.date[0] : '' : '',
          createTimeEnd: this.dateStatus == 1 ? this.date ? this.date[1] : '' : '',
          current: pageNum,
          size: pageSize
        }
      } else {
        // 非懒加载模式：使用addAlarm接口的参数格式
        params = {
          ...this.queryForm,
          ...temp[0],
          startReportTime: this.dateStatus == 1 ? this.date ? this.date[0] : '' : '',
          endReportTime: this.dateStatus == 1 ? this.date ? this.date[1] : '' : '',
          current: pageNum,
          size: pageSize,
          type: "ALL"
        }
      }

      // 根据模式选择不同的接口
      const apiCall = this.isLazyLoading ?
        this.$api.activeAlarm(params) :
        this.$api.addAlarm(params)

      apiCall.then(res => {
        if (res.code == 200) {
          let responseData, totalCount;

          if (this.isLazyLoading) {
            // 懒加载模式：使用activeAlarm接口返回的分页数据
            responseData = res.data.records || []
            totalCount = res.data.total || 0
          } else {
            // 非懒加载模式：使用addAlarm接口返回的全量数据
            responseData = res.data.active || []
            totalCount = responseData.length
          }

          const sortedData = responseData.sort((a, b) => {
            if (a.alarmLevel !== b.alarmLevel) {
              return a.alarmLevel - b.alarmLevel;
            }
            return new Date(b.alarmTime) - new Date(a.alarmTime);
          });

          if (this.isLazyLoading) {
            // 懒加载模式：直接设置当前页数据
            this.currentPageData = sortedData
            this.total = totalCount
          } else {
            // 非懒加载模式：保持原有逻辑用于轮询
            this.deviceTableData = sortedData
            this.currentPageData = sortedData.slice(0, this.pageSize)
            this.total = totalCount
          }

          this.tableLoading = false

          // 只在dateStatus为0且非懒加载模式时启动轮询
          if (this.dateStatus == 0 && !this.isLazyLoading) {
            this.timer = setTimeout(() => {
              this.startPolling()
            }, 1000 * 10)
          }
        } else {
          this.tableLoading = false
          this.$message.error(res.msg)
        }
      }).catch(error => {
        this.tableLoading = false
        this.$message.error('查询失败')
        console.error('查询错误:', error)
      })
    },
    waringExport() {
      // 导出所有
      this.$confirm("是否确认导出当前所有报表?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {

        const data = []
        let temp;
        try {
          temp = this.areaList.filter(i => {
            return i.areaName == this.queryForm.areaName
          })
          // this.queryForm.areaId = temp[0].areaId
        } catch (error) {

        }
        let params = {
          ...this.queryForm,
          ...temp[0],
          startReportTime: this.dateStatus == 1 ? this.date ? this.date[0] : '' : '',
          endReportTime: this.dateStatus == 1 ? this.date ? this.date[1] : '' : '',
          type: "ALL"
        }
        await this.$api.addAlarm(params).then(res => {
          data.push(...res.data.active)
        })
        if (data.length) {
          const fileName = '设备监控';
          const excelData = [
            ['地市','区域','局站','机房','设备类型','设备名称','厂家','告警级别','告警发生时间','告警描述','触发值' ]] // 表格表头
          data.forEach((item, index) => {
            let rowData = [];
            //导出内容的字段
            rowData = [
              item.cityName,
              item.areaName,
              item.stationName,
              item.roomName,
              item.deviceTypeName,
              item.deviceName,
              item.suVendor,
              item.alarmLevel,
              item.alarmTime,
              item.alarmDesc,
              item.triggerVal,
            ];
            excelData.push(rowData);
          });
          console.log(excelData);
          exportTableToExcel(excelData, fileName)
        } else {
          this.$message({
            type: 'error',
            message: '请确保导出的报表数量不为0'
          });
        }
      });
    },
    handleSizeChange(val) {
      this.pageSize = val
      if (this.isLazyLoading) {
        // 懒加载模式：重新请求第一页数据
        this.currentPage = 1
        this.queryData(1, val)
      } else {
        // 非懒加载模式：从已有数据中切片
        this.updateCurrentPageData()
      }
    },
    handleCurrentChange(val) {
      this.currentPage = val
      if (this.isLazyLoading) {
        // 懒加载模式：请求对应页的数据
        this.queryData(val, this.pageSize)
      } else {
        // 非懒加载模式：从已有数据中切片
        this.updateCurrentPageData()
      }
    },

    /**
     * 更新当前页数据（非懒加载模式）
     */
    updateCurrentPageData() {
      const startIndex = (this.currentPage - 1) * this.pageSize
      const endIndex = startIndex + this.pageSize
      this.currentPageData = this.deviceTableData.slice(startIndex, endIndex)
    },

    /**
     * 切换加载模式
     * @param {boolean} isLazy 是否启用懒加载
     */
    toggleLazyLoading(isLazy = true) {
      this.isLazyLoading = isLazy
      if (isLazy) {
        // 切换到懒加载模式
        this.queryData(this.currentPage, this.pageSize, true)
      } else {
        // 切换到全量加载模式
        this.queryData(1, this.pageSize, false)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ad {
  display: flex;
  gap: 6px;

  i {
    cursor: pointer;
  }
}

.pag {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 12px;
}

.fade-enter-active,
.fade-leave-active {
  transition: all 1s ease;
}

.fade-enter,
.fade-leave-to {
  opacity: 0;
  transform: translateX(60px);
}

.lazy-loading-tip,
.normal-loading-tip {
  margin-bottom: 12px;
}

.lazy-loading-status {
  text-align: center;
  padding: 20px;
  color: #666;

  .loading-more {
    color: #409EFF;
    font-size: 14px;

    i {
      margin-right: 5px;
    }
  }

  .no-more-data {
    color: #909399;
    font-size: 14px;
  }

  .scroll-tip {
    color: #67C23A;
    font-size: 14px;
  }
}
</style>