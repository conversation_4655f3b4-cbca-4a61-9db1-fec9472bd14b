const path = require('path')
// const UnoCSS = require('@unocss/webpack').default
const resolve = dir => {
  return path.join(__dirname, dir)
}

let api = 'http://10.93.22.100:8085/' // 动环测试环境
var locurl = 'http://10.93.22.100:8765'; // 测试

module.exports = {
  devServer: {
    open: true,
    port: 2021,
    proxy: {
      '/jwt': {
        target: locurl, 
        pathRewrite: {
          '^/jwt': '/jwt'
        },
      },
      '/api':{
        target:locurl,
        pathRewrite: {
          '^/api': '/api'
        },
      },
      "/monitor/": {
        target: locurl,
        changeOrigin: true,
      },
      "/flowConfig/": {
        target: api,
        changeOrigin: true,
      },
      "/queryCityList": {
        target: locurl,
        changeOrigin: true,
      },
      "/orderHandle": {
        target: locurl,
        changeOrigin: true,
      },
      "/upload": {
        target: 'http://10.93.22.100:8080/',
        changeOrigin: true,
      },
    },
    overlay: {
      warnings: false,
      errors: true
    }
  },
  publicPath: process.env.VUE_APP_BASE_URL,
  lintOnSave: false,
  productionSourceMap: false,

  configureWebpack: {
    // plugins: [
    //   UnoCSS(),
    // ],
    resolve: {
      alias: {
        '@': resolve('src')
      },
      extensions: ['.js', '.vue', '.scss', '.css', '.json']
    }
  },
  chainWebpack: config => {
    config.plugin('html').tap(args => {
      args[0].title = 'Portal'
      return args
    })
    config.optimization.splitChunks({
      cacheGroups: {
        elementUI: {
          name: 'element',
          priority: 20,
          test: /[\\/]node_modules[\\/]_?element-ui(.*)/,
          chunks: 'all'
        }
      }
    })

  }
}
