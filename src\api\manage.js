import request from '@/api/http'

// post请求
export function postAction (url, parameter) {
  return request({
    url: url,
    method: 'post',
    data: parameter
  })
}

// put请求
export function putAction (url, parameter) {
  return request({
    url: url,
    method: 'put',
    data: parameter
  })
}

// get请求
export function getAction (url, parameter) {
  return request({
    url: url,
    method: 'get',
    params: parameter
  })
}

// delete请求
export function deleteAction (url, parameter) {
  return request({
    url: url,
    method: 'delete',
    data: parameter
  })
}
