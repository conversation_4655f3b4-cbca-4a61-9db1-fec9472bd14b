<template>
  <div class="app-container" style="height: 100%;">

    <div v-show="!showPeopleCfg" class="app-container">
      <!-- <div class="title">工单审批工作台</div> -->
      <el-form :inline="true" :model="flowQuery" size="small" class="demo-form-inline">
        <el-form-item label="流程名称">
          <el-input v-model="flowQuery.name" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="所属地市">
          <el-select v-model="flowQuery.cityCode" clearable>
            <el-option v-for="item in regionOption" :key="item.cityId" :label="item.cityName"
              :value="item.cityId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="流程类型">
          <el-select v-model="flowQuery.type" clearable>
            <el-option v-for="item in typeList" :key="item.type" :label="item.value" :value="item.type"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item style="margin: 0 24px">
          <el-button @click="flowreset('flowQuery')">重置</el-button>
          <el-button type="primary" @click="queryFlowData">查询</el-button>
          <el-button type="primary" @click="addFLowDialog">新增</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="flowtableData" size="small" v-loading="tabLoading1">
        <el-table-column prop="cityName" align="center" label="地市"></el-table-column>
        <el-table-column prop="name" align="center" show-overflow-tooltip label="流程名称"></el-table-column>
        <el-table-column prop="type" align="center" label="流程类型" width="140">
          <template slot-scope="scope">
            <!-- { type: '7', value: 'FSU核查流程' },
                { type: '8', value: '设备资源核查流程' },
                { type: '9', value: '未注册资源核查流程' }, -->
            <el-tag size="small" v-if="scope.row.type == 1" type="info">普通单站阈值</el-tag>
            <el-tag size="small" v-if="scope.row.type == 2" type="info">普通单站告警</el-tag>
            <el-tag size="small" v-if="scope.row.type == 3" type="success">批量阈值</el-tag>
            <el-tag size="small" v-if="scope.row.type == 4" type="success">批量告警</el-tag>
            <el-tag size="small" v-if="scope.row.type == 5" type="success">核心单站阈值</el-tag>
            <el-tag size="small" v-if="scope.row.type == 6" type="success">核心单站告警</el-tag>
            <el-tag size="small" v-if="scope.row.type == 7" type="success">FSU核查流程</el-tag>
            <el-tag size="small" v-if="scope.row.type == 8" type="success">设备资源核查流程</el-tag>
            <el-tag size="small" v-if="scope.row.type == 9" type="success">未注册资源核查流程</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="type" align="center" label="流程状态" width="140">
          <template slot-scope="scope">
            <!-- {{ scope.row.sts == 1 ? '已启用' : '未启用' }} -->

            <el-tag size="small" v-if="scope.row.sts == 1" type="success">已启用</el-tag>
            <el-tag size="small" v-if="scope.row.sts == 0" type="danger">未启用</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="createTime" align="center" label="创建时间"></el-table-column>
        <el-table-column prop="updateTime" align="center" label="更新时间"></el-table-column>
        <el-table-column align="center" label="操作" width="180">
          <template slot-scope="scope">
            <el-button @click="flowEdit(scope.row)" type="text" size="small">编辑</el-button>
            <el-button style="color: #f56c6c;" @click="removeFlow(scope.row)" type="text" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
      <div class="pag">
        <el-pagination @size-change="flowhandleSizeChange" background @current-change="flowhandleCurrentChange"
          :current-page="flowcurrentPage" :page-sizes="[10, 20, 50]" :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="flowtotal">
        </el-pagination>
      </div>
    </div>


    <div v-show="showPeopleCfg">
      <el-page-header :content="addOrEdit" @back="goback"></el-page-header>
      <div style="padding: 24px;">
        <el-tabs v-model="tabActiveName" @tab-click="handleClick">
          <el-tab-pane label="流程配置" name="first"></el-tab-pane>
          <el-tab-pane label="人员配置" name="second" :disabled="this.processOption.length ? false : true"></el-tab-pane>
        </el-tabs>
        <div v-if="tabActiveName == 'first'">
          <!-- <div class="title">流程配置</div> -->
          <div style="width: 100%; display: flex;flex-direction: column;align-items: center;">

            <el-form :inline="true" :model="nodeparams" :rules="nodeparamsrules" size="small" class="demo-form-inline">
              <el-form-item label="所属地市" prop="cityCode">
                <el-select :disabled="!isEditProcess" v-model="nodeparams.cityCode" clearable>
                  <el-option v-for="item in regionOption" :key="item.cityId" :label="item.cityName"
                    :value="item.cityId"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="流程类型" prop="type">
                <el-select @change="flowTypeChange" :disabled="!isEditProcess" v-model="nodeparams.type" clearable>
                  <el-option v-for="item in typeList" :key="item.type" :label="item.value"
                    :value="item.type"></el-option>
                </el-select>
              </el-form-item>
            </el-form>

            <div class="mystep" style="margin: 24px ;width: 100%;">
              <el-steps style="width: 600px;" :active="processTableData.length" :space="140" finish-status="success">
                <el-step :title="item.name" :key="index" v-for="(item, index) in processTableData"></el-step>
              </el-steps>
            </div>

            <el-table :data="processTableData" border size="small" style="width: 600px">
              <el-table-column type="index" label="环节顺序号" width="100">
              </el-table-column>
              <el-table-column prop="name" label="环节名称">
                <template slot-scope="scope">
                  <el-input :disabled="!isEditProcess" v-model="scope.row.name" size="small"
                    placeholder="请输入环节名称"></el-input>
                </template>
              </el-table-column>
              <el-table-column prop="name" label="是否下发指令" width="120">
                <template slot-scope="scope">
                  <el-switch :disabled="!isEditProcess" @change="extraConfig(scope)" v-model="scope.row.extraConfig"
                    active-color="#13ce66" inactive-color="#ff4949" :active-value="1" :inactive-value="0">
                  </el-switch>
                </template>
              </el-table-column>
              <template v-if="isEditProcess">
                <el-table-column prop="" label="操作" width="100">
                  <template slot-scope="scope">
                    <div class="ad">
                      <i class="el-icon-plus" @click="addPro()"></i>
                      <i class="el-icon-delete" @click="deletePro(scope)"></i>
                    </div>
                  </template>
                </el-table-column>
              </template>

            </el-table>
            <el-button v-if="isEditProcess" @click="addFlow" style="margin: 24px auto;" type="primary">确定</el-button>
          </div>
        </div>
        <div v-if="tabActiveName == 'second'">
          <!-- <div class="title">人员配置</div> -->
          <el-form :inline="true" :model="queryForm" size="small" class="demo-form-inline">

            <el-form-item label="环节">
              <el-select v-model="queryForm.nodeId" clearable>
                <el-option v-for="item in processOption" :key="item.nodeId" :label="item.name"
                  :value="item.nodeId"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="地市">
              <el-select @change="cityChange" v-model="queryForm.cityId" clearable>
                <el-option v-for="item in regionOption" :key="item.cityId" :label="item.cityName"
                  :value="item.cityId"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item label="区县">
              <el-select v-model="queryForm.areaId" clearable>
                <el-option v-for="item in districtOption" :key="item.areaId" :label="item.areaName"
                  :value="item.areaId"></el-option>
              </el-select>
            </el-form-item>
            <el-form-item>
              <el-button @click="reset">重置</el-button>
              <el-button type="primary" @click="queryData">查询</el-button>
              <el-button v-if="approvalProcess_btn_add" type="primary" @click="addConfig">新增</el-button>
              <el-button v-if="approvalProcess_btn_config_add" type="primary" @click="saveAllConfig">保存人员配置</el-button>
            </el-form-item>
          </el-form>
          <el-table :data="peopleConfigList" size="small">

            <el-table-column prop="nodeName" align="center" label="审批环节名称"></el-table-column>
            <el-table-column prop="cityName" align="center" label="地市"></el-table-column>
            <el-table-column prop="areaName" align="center" label="区县"></el-table-column>
            <el-table-column prop="users" align="center" label="审批人"></el-table-column>
            <el-table-column prop="" label="操作" width="200">
              <template slot-scope="scope">
                <div class="ad">
                  <el-button @click="editConfig(scope)" type="text" style="margin: 0 12px">编辑</el-button>
                  <el-popover placement="top" width="180" v-model="visible">
                    <p>确定要删除该配置吗？</p>
                    <div style="text-align: right; margin: 8px 0 0">
                      <el-button size="mini" type="text" @click="visible = false">取消</el-button>
                      <el-button type="primary" size="mini" @click="visible = false">确定</el-button>
                    </div>
                    <el-button style="color: #f56c6c;" slot="reference" @click="deleteConfig(scope.row)"
                      type="text">删除</el-button>
                  </el-popover>
                </div>
              </template>
            </el-table-column>
          </el-table>
          <div class="pag">
            <el-pagination @size-change="handleSizeChange" @current-change="handleCurrentChange"
              :current-page="currentPage" :page-sizes="[10, 20, 50]" :page-size="pageSize"
              layout="total, sizes, prev, pager, next, jumper" :total="total">
            </el-pagination>
          </div>

          <el-dialog :title="dialogTitle" :visible.sync="dialogVisible" width="860px">
            <el-form ref="addAndEditData" :inline="true" :rules="rules" :model="addAndEditData" label-width="70px"
              size="small" class="demo-form-inline">

              <el-form-item label="环节: " prop="nodeId">
                <el-select v-model="addAndEditData.nodeId" clearable>
                  <el-option v-for="item in processOption" :key="item.nodeId" :label="item.name"
                    :value="item.nodeId"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="地市: " prop="cityId">
                <el-select v-model="addAndEditData.cityId" disabled @change="dialogcityChange" clearable>
                  <el-option v-for="item in regionOption" :key="item.cityId" :label="item.cityName"
                    :value="item.cityId"></el-option>
                </el-select>
              </el-form-item>
              <el-form-item label="区县: " prop="areaId">
                <el-select v-model="addAndEditData.areaId" clearable>
                  <el-option v-for="item in dialogareaList" :key="item.areaId" :label="item.areaName"
                    :value="item.areaId"></el-option>
                </el-select>
              </el-form-item>
              <el-row>
                <el-col :span="24">
                  <el-form-item label="审批人: " prop="userIds">
                    <el-select filterable multiple v-model="addAndEditData.userIds" style="width: 708px" clearable>
                      <el-option v-for="item in userlist" :key="item.user_id" :label="item.name"
                        :value="item.id"></el-option>
                    </el-select>
                  </el-form-item>
                </el-col>
              </el-row>

            </el-form>
            <span slot="footer" class="dialog-footer">
              <el-button size="small" @click="closeDialog">取 消</el-button>
              <el-button size="small" type="primary" @click="savePeolpleConfig">确 定</el-button>
            </span>
          </el-dialog>
        </div>
      </div>

    </div>

  </div>

</template>

<script>
import {
  page
} from '@/api/admin/user/index';
import { mapGetters } from 'vuex';
import { getSysUserId } from '@/utils/auth';
export default {
  data() {
    return {
      isEditProcess: true,

      tabLoading1: false,
      addOrEdit: '新增流程',
      showPeopleCfg: false,
      // 1 普通单站阈值 2 普通单站告警 3 批量阈值 4 批量告警 5 核心单站阈值 6 核心单站告警
      typeList: [
        // { type: '0', value: '普通' },
        // { type: '1', value: '核心' },
        // { type: '3', value: '批量告警' },
        // { type: '4', value: '批量阈值' },
        { type: '1', value: '普通单站阈值' },
        { type: '2', value: '普通单站告警' },
        { type: '3', value: '批量阈值' },
        { type: '4', value: '批量告警' },
        { type: '5', value: '核心单站阈值' },
        { type: '6', value: '核心单站告警' },
        { type: '7', value: 'FSU核查流程' },
        { type: '8', value: '设备资源核查流程' },
        { type: '9', value: '未注册资源核查流程' },
        { type: '10', value: '资源标记流程' }

      ],
      flowQuery: {
        name: '', // 名称
        cityCode: '', // 地市
        type: '', // 类型
      },
      flowtableData: [],
      flowpageSize: 10,
      flowcurrentPage: 1,
      flowtotal: 0,
      flowcurrentRow: null,

      user_id: getSysUserId(),
      stepActiveindex: 0, // 步骤条激活下标
      tabActiveName: 'first', // tab页激活下标
      processTableData: [], // 环节列表 表格与环节都是用
      flowId: '',

      nodeparams: {
        cityCode: '', // 当前市级Id
        type: '', // 当前流程类型
      },

      queryForm: {
        nodeId: '',
        cityId: '',
        areaId: ''
      },
      queryList: [],
      processOption: [],
      regionOption: [],
      districtOption: [],
      peopleConfigList: [],
      allpeopleConfigList: [],
      pageSize: 10,
      currentPage: 1,
      total: 400,
      dialogVisible: false,
      visible: false,
      dialogTitle: '',
      userlist: [],
      addAndEditData: {
        areaId: '',
        cityId: '',
        nodeId: '',
        nodeName: '',
        userIds: []
      },
      dialogareaList: [],
      rules: {
        nodeId: [
          { required: true, message: '请选择环节', trigger: 'change' }
        ],
        cityId: [
          { required: true, message: '请选择地市', trigger: 'change' }
        ],
        userIds: [
          { required: true, message: '请选择审批人', trigger: 'change' }
        ]
      },
      nodeparamsrules: {
        cityCode: [
          { required: true, message: '请选择地市', trigger: 'change' }
        ],
        type: [
          { required: true, message: '请选择类型', trigger: 'change' }
        ]
      },
      opr: '',
      approvalProcess_btn_add: false,
      approvalProcess_btn_config_add: false,
    }
  },
  computed: {
    ...mapGetters([
      'elements'
    ])
  },
  created() {
    this.queryFlowData()

    //   this.queryFlow()
    this.queryCityList()
    this.getusers()
    this.approvalProcess_btn_add = this.elements['approvalProcess:btn_add']
    this.approvalProcess_btn_config_add = this.elements['approvalProcess:btn_config_add']
  },
  methods: {
    extraConfig(scope) {
      let extraConfig = scope.row.extraConfig
      let _index = scope.$index
      if (extraConfig == 1) {
        this.processTableData = this.processTableData.map((item, index) => {
          if (_index !== index) {
            item.extraConfig = 0
          }
          return item
        })
      }
      console.log(scope)
    },
    flowTypeChange(flowType) {

      // 7-FSU核查流程    第一个环节 自动稽核 第二个 环节疑似退网处理 
      // 8-设备资源核查流程  第一个环节 自动稽核 第二个环节 疑似退网处理 
      // 9-未注册资源核查流程 第一个环节 自动稽核 第二个环节 未注册设备处理 

      if (flowType == '7') {
        this.processTableData = [
          { name: '自动稽核', extraConfig: 0 },
          { name: '疑似退网处理', extraConfig: 0 },
        ]
        return
      }
      if (flowType == '8') {
        this.processTableData = [
          { name: '自动稽核', extraConfig: 0 },
          { name: '疑似退网处理', extraConfig: 0 },
        ]
        return
      }
      if (flowType == '9') {
        this.processTableData = [
          { name: '自动稽核', extraConfig: 0 },
          { name: '未注册设备处理', extraConfig: 0 },
        ]
        return
      }

      if (['5', '6'].includes(flowType)) {
        this.processTableData = [
          { name: '提交申请', extraConfig: 0 },
          { name: '地市负责人审批', extraConfig: 0 },
          { name: '省份负责人审批', extraConfig: 1 },
        ]
        return
      } else {
        this.processTableData = [
          { name: '提交申请', extraConfig: 0 },
          { name: '地市负责人审批', extraConfig: 1 },
        ]
      }
    },
    removeFlow(row) {
      this.$api.removeFlow({ flowId: row.flowId }).then(res => {
        if (res.code == 200) {
          this.$message.success('删除成功')
          this.queryFlowData()
        } else {
          this.$message.error(res.msg)
        }
      })
    },
    goback() {
      this.flowId = ""
      this.processOption = []
      this.allpeopleConfigList = []
      this.peopleConfigList = []
      this.queryList = []
      this.showPeopleCfg = false
      this.queryFlowData()
    },
    addFLowDialog() {

      this.addOrEdit = '新增流程'
      this.isEditProcess = true
      this.nodeparams = {
        cityCode: '', // 当前市级Id
        type: '', // 当前流程类型
      }
      this.processTableData = [
        { name: '提交申请', extraConfig: 0 },
        { name: '地市负责人审批', extraConfig: 1 }
      ]
      this.tabActiveName = 'first'
      this.showPeopleCfg = true
    },
    async flowEdit(row) {
      this.addOrEdit = '编辑流程'

      if (row.sts == 1) {
        this.isEditProcess = false
      } else {
        this.isEditProcess = true
      }

      this.nodeparams = {
        cityCode: row.cityCode, // 当前市级Id
        type: row.type, // 当前流程类型
      }
      this.tabActiveName = 'first'
      await this.queryFlow(row.flowId)
      this.showPeopleCfg = true
    },

    flowreset() {
      this.flowQuery = {
        name: '', // 名称
        cityCode: '', // 地市
        type: '', // 类
      }
      this.flowcurrentPage = 1
      this.flowpageSize = 10
      this.queryFlowData()
      // this.role = sessionStorage.getItem('role')
      // this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
      // this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''

      // if (this.role == '1') {
      //   this.cityDisabled = false
      //   this.areaDisabled = false
      // }

      // if (['94', '95'].includes(this.role)) {
      //   this.cityDisabled = true
      // }
    },
    async queryFlowData() {
      this.tabLoading1 = true
      let params = {
        ...this.flowQuery,
        pageNum: this.flowcurrentPage,
        pageSize: this.flowpageSize
      }
      let res = await this.$api.queryFlowList(params)
      if (res.code == 200) {
        this.flowtableData = res.data.records
        this.flowtotal = res.data.total
      } else {
        this.$message.error(res.msg)
      }
      this.tabLoading1 = false

    },
    flowhandleSizeChange(val) {
      this.flowpageSize = val
      this.queryFlowData()
    },
    flowhandleCurrentChange(val) {
      this.flowcurrentPage = val
      this.queryFlowData()
    },

    // 查询用户
    getusers() {
      page({ user_id: this.user_id, limit: 999, page: 1 }).then(res => {
        this.userlist = res.data.rows;
      })
    },

    // 地市查询
    async queryCityList() {
      let params = {
        cityName: '',
        pageNum: 1,
        pageSize: 999
      }
      let res = await this.$api.queryCityList(params)
      if (res.code == 200) {
        this.regionOption = res.data.records
      } else {
        this.$message.error(res.msg)
      }
    },
    cityChange(val) {
      this.districtOption = this.regionOption.find(v => v.cityId == val)?.areaList || []
    },

    // 根据流程id查环节 
    async queryFlow(flowId) {
      let res = await this.$api.queryFlowById({ flowId: flowId })
      if (res.code == 200) {
        this.flowId = res.data.flowId
        this.processTableData = res.data.nodeList
        this.processOption = res.data.nodeList
      } else {
        this.$message.error(res.msg)
      }
    },
    // 新增流程
    async addFlow() {

      if (!this.nodeparams.cityCode || !this.nodeparams.type) return this.$message.error('请选择所属地市与流程类型')
      let params = this.processTableData.map((v, index) => {
        let obj = {
          sort: index + 1,
          name: v.name,
          extraConfig: v.extraConfig
        }
        return obj
      })
      let res = await this.$api.addFlow({
        ...this.nodeparams,
        flowId: this.flowId,
        cityCode: this.nodeparams.cityCode,
        cityName: this.regionOption.find(v => v.cityId == this.nodeparams.cityCode)?.cityName || '',
        nodeList: params
      })
      console.log(res);
      
      if (res.code == 200) {
        this.flowId = res.data[0].flowId
        this.processOption = res.data
        this.tabActiveName = 'second'
        this.$message.success('保存成功')
      } else {
        this.$message.error(res.msg ? res.msg : '保存失败')
      }
    },
    handleClick(val) {
      if (val.name == 'first') {

      }
      if (val.name == 'second') {
        if (!this.allpeopleConfigList.length) {
          this.queryallData()
        }
      }
    },
    // 删除环节
    deletePro(scope) {
      if (this.processTableData.length == 1) return this.$message.warning('至少需要1个环节')
      this.processTableData = this.processTableData.filter((item, i) => i !== scope.$index)
    },
    // 增加环节
    addPro() {
      if (this.processTableData.length >= 5) return this.$message.warning('最多支持增加5个环节')
      this.processTableData.push({
        name: `自定义流程`
      })
    },
    reset() {
      this.queryForm = {
        nodeId: '',
        cityId: '',
        areaId: ''
      }

      this.queryList = []
      this.queryData()
      this.role = sessionStorage.getItem('role')
      this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
      this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''

      if (this.role == '1') {
        this.cityDisabled = false
        this.areaDisabled = false
      }

      if (['94', '95'].includes(this.role)) {
        this.cityDisabled = true
      }
    },
    // 检索条件查询
    async queryData() {
      this.pageSize = 10
      this.currentPage = 1
      // this.peopleConfigList = this.allpeopleConfigList.filter(v => v.nodeName == this.queryForm.nodeName && v.cityName == this.queryForm.cityName && v.areaName == this.queryForm.areaName)
      this.queryList = this.allpeopleConfigList.filter(item => {
        // 遍历所有查询条件
        for (const [k, v] of Object.entries(this.queryForm)) {
          if (v) { // 如果条件不为空
            if (!item[k] || !item[k].includes(v)) {
              return false; // 如果当前项不包含查询条件，则过滤掉
            }
          }
        }
        return true; // 如果所有条件都满足，则保留
      })
      this.tableDataHandel()
      // let params = {
      //     flowId: this.flowId,
      //     ...this.queryForm,
      // }
      // let res = await this.$api.queryNodeConfig(params)
      // if (res.code == 200) {
      //     this.queryList = res.data.map(v => {
      //         v.userIds = v.userId.split(',')
      //         // v.users = this.userlist.filter(s => v.userIds.includes(s.user_id)).map(v => v.username)
      //         v.users = v.handleUsers.map(v => v.userName).join(',')
      //         return v
      //     })
      //     this.tableDataHandel()
      // } else {
      //     this.$message.error(res.msg)
      // }
    },
    // 查询全部人员配置表格数据
    async queryallData() {
      let params = {
        flowId: this.flowId,
        //...this.queryForm,
      }
      let res = await this.$api.queryNodeConfig(params)
      if (res.code == 200) {
        this.allpeopleConfigList = res.data.map(v => {
          v.userIds = v.userId.split(',')
          // v.users = this.userlist.filter(s => v.userIds.includes(s.user_id)).map(v => v.username)
          v.users = v.handleUsers.map(v => v.userName).join(',')
          return v
        })
        this.tableDataHandel()
      } else {
        this.$message.error(res.msg)
      }
    },
    //新增人员配置
    dialogcityChange(val) {
      console.log(val)
      this.dialogareaList = this.regionOption.find(v => v.cityId == val)?.areaList || []
    },
    closeDialog() {
      this.addAndEditData = {
        areaId: '',
        cityId: '',
        nodeId: '',
        nodeName: '',
        userIds: []
      },
        this.$refs['addAndEditData'].resetFields()
      this.dialogVisible = false
    },
    addConfig() {
      this.dialogTitle = '新增人员配置'
      this.addAndEditData.cityId = this.nodeparams.cityCode
      console.log(this.nodeparams, this.addAndEditData.cityId)
      this.dialogcityChange(this.addAndEditData.cityId)
      this.dialogVisible = true
    },
    verifyDuplicateData(data, type) {
      let dupNode = []
      if (type == '新增人员配置') {
        dupNode = this.allpeopleConfigList.filter(v => v.nodeId == data.nodeId)
      }
      if (type == '编辑人员配置') {
        dupNode = this.allpeopleConfigList.filter(v => v.nodeId == data.nodeId && data.nodeConfigId !== v.nodeConfigId)
      }
      if (!dupNode.length) return true
      let dupCityArea = dupNode.filter(v => v.areaId == data.areaId && v.cityId == data.cityId)
      let flag = dupCityArea.length ? false : true
      return flag
    },
    savePeolpleConfig() {
      let flag = this.verifyDuplicateData(this.addAndEditData, this.dialogTitle)
      if (!flag) {
        return this.$message.error('请勿重复添加，同一个环节的相同区域仅能存在一条')
      }
      this.$refs['addAndEditData'].validate((valid) => {
        if (valid) {
          let obj = JSON.parse(JSON.stringify(this.addAndEditData))
          obj.nodeName = this.processOption.find(v => v.nodeId == obj.nodeId)?.name || ''
          obj.cityName = this.regionOption.find(v => v.cityId == obj.cityId)?.cityName || ''
          obj.areaName = this.dialogareaList.find(v => v.areaId == obj.areaId)?.areaName || ''
          let _userList = this.userlist.filter(v => obj.userIds.includes(v.id)).map(v => v.name)
          obj.users = _userList.join(',')
          if (this.dialogTitle == '新增人员配置') {
            obj.nodeConfigId = new Date().getTime()
            this.allpeopleConfigList.unshift(obj)
          }
          if (this.dialogTitle == '编辑人员配置') {
            let index = this.allpeopleConfigList.findIndex(v => v.nodeConfigId == obj.nodeConfigId)
            this.$set(this.allpeopleConfigList, index, obj)
            console.log(obj, index, this.allpeopleConfigList)

          }
          // console.log(obj, this.allpeopleConfigList)
          this.$refs['addAndEditData'].resetFields()
          this.dialogVisible = false
          //this.tableDataHandel()
          this.queryData()
        } else {
          console.log('error submit!!');
          return false;
        }
      });


    },
    // 提交人员配置
    async saveAllConfig() {
      if (!this.flowId) return this.$message.error('流程ID为空')
      let data = this.allpeopleConfigList.map(v => {
        let obj = {
          cityId: v.cityId,
          areaId: v.areaId,
          nodeId: v.nodeId,
          nodeName: v.nodeName,
          userIds: v.userIds
        }
        return obj
      })
      let params = {
        flowId: this.flowId,
        nodeConfigList: data
      }
      let res = await this.$api.saveNodeConfig(params)
      if (res.code == 200) {
        this.$message.success('人员配置保存成功')
        this.queryallData()
      } else {
        this.$message.error(res.msg)
      }

    },
    editConfig(scope) {
      this.dialogTitle = '编辑人员配置'
      scope.row.userIds = scope.row.userIds.map(v => Number(v))
      this.addAndEditData = scope.row
      this.dialogcityChange(scope.row.cityId)
      this.dialogVisible = true
    },
    deleteConfig(row) {
      let data = JSON.parse(JSON.stringify(this.allpeopleConfigList))
      this.allpeopleConfigList = data.filter(v => v.nodeConfigId !== row.nodeConfigId)
      this.queryData()
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.tableDataHandel()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.tableDataHandel()
    },
    tableDataHandel(type) {
      let data = this.queryList.length ? this.queryList : this.allpeopleConfigList
      this.peopleConfigList = data.slice((this.currentPage - 1) * this.pageSize, this.currentPage * this.pageSize)
      this.total = data.length
    }
  },
}
</script>

<style lang="scss" scoped>
.title {
  font-size: 14px;
  font-weight: bold;
  margin: 18px 0 24px;
}

.ad {
  font-size: 18px;
  display: flex;

  i {
    margin-right: 16px;
    cursor: pointer;
  }
}

.mystep {
  display: flex;
  flex-direction: row;
  justify-content: center;
}

// .pag {
//     display: flex;
//     flex-direction: row-reverse;
//     margin-top: 24px;
// }</style>