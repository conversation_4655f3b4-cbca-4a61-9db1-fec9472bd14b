import CryptoJS from 'crypto-js'
// AES-128-CBC偏移量
const CBCIV = "hub<PERSON><PERSON><PERSON><PERSON><PERSON>";
export default {
  encrypt(data) {
    let key = CryptoJS.enc.Utf8.parse(CBCIV);
    let secretData = CryptoJS.enc.Utf8.parse(data);
    let encrypted = CryptoJS.AES.encrypt(
        secretData,
        key,
        {
            iv: CryptoJS.enc.Utf8.parse(CBCIV),
            mode:CryptoJS.mode.CBC,
            padding: CryptoJS.pad.Pkcs7
        }
    );
    return encrypted.toString();
  },
  decrypt(data) {
    let key = CryptoJS.enc.Utf8.parse(CBCIV);
        let decrypt = CryptoJS.AES.decrypt(
            data,
            key,
            {
                iv: CryptoJS.enc.Utf8.parse(CBCIV),
                mode: CryptoJS.mode.CBC,
                padding: CryptoJS.pad.Pkcs7
            });
        return CryptoJS.enc.Utf8.stringify(decrypt).toString();
  },
  get<PERSON>ey() {
    let s = ["\x61", "\x73", "\x69", "\x61", "\x69", "\x6E", "\x66", "\x6F", "\x40", "\x63", "\x6F", "\x6D", "\x31", "\x32", "\x33", "\x34"]
    return s.join("")
  }
}
