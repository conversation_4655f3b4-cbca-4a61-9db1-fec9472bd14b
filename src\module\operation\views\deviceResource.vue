<template>
  <div class="app-container">

    <template v-if="showContent == 'list'">
      <el-form :inline="true" :model="queryForm" size="small" label-width="100px" class="demo-form-inline">
        <el-form-item label="地市:">
          <el-select v-model="queryForm.cityCode" :disabled="cityDisabled" @change="cityChange" filterable clearable
            placeholder="请选择">
            <el-option v-for="(item, index) in citylist" v-show="index !== 0" :key="item.cityId" :label="item.cityName"
              :value="item.cityId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="区域:">
          <el-select v-model="queryForm.areaCode" :disabled="areaDisabled" @change="areaChange" filterable clearable
            placeholder="请选择">
            <el-option v-for="item in areaList" :key="item.areaId" :label="item.areaName"
              :value="item.areaId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="局站:">
          <el-select v-model="queryForm.siteId" @change="siteChange" :remote-method="getSite" filterable clearable
            placeholder="请选择" @click="getSite">
            <el-option v-for="item in siteList" filterable :key="`${item.area_id}-${item.site_id}`"
              :label="item.site_name" :value="item.site_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="机房:">
          <el-select v-model="queryForm.roomId" @change="roomChange" :remote-method="getRoom" filterable clearable
            placeholder="请选择">
            <el-option v-for="item in roomlist" :key="`${item.room_id}-${item.site_id}`" :label="item.room_name"
              :value="item.room_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备类型:">
          <el-select v-model="queryForm.deviceType" @change="devTypeChange" :remote-method="getDeviceType" filterable
            remote clearable placeholder="请输入">
            <el-option v-for="(item, index) in deviceTypeList" :key="`${index}-${item.id}`" :label="item.label"
              :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备名称:">
          <el-select v-model="queryForm.deviceName" filterable remote clearable placeholder="请输入"
          :remote-method="getDeviceName">
            <el-option v-for="(item, index) in devoptions" :key="`${index}-${item.id}-${item.label}`" :label="item.label"
              :value="item.label">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="SU_IP:">
          <el-input v-model="queryForm.suIp" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="">
          <el-button style="margin-left: 60px;" @click="queryFormReset">重置</el-button>
          <el-button type="primary" @click="queryData(1, 10)">查询</el-button>
          <el-button type="primary" @click="devExport" :loading="exportloading">导出</el-button>
        </el-form-item>
      </el-form>
      <el-table border :data="deviceTableData" header-row-class-name="myHeaderClass" size="small" v-loading="tableLoading">
        <el-table-column prop="cityName" align="center" label="地市"></el-table-column>
        <el-table-column prop="areaName" align="center" label="区县" show-overflow-tooltip></el-table-column>
        <el-table-column prop="siteName" align="center" label="局站" show-overflow-tooltip></el-table-column>
        <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
        <el-table-column prop="suIp" align="center" label="SU_IP" show-overflow-tooltip></el-table-column>
        <el-table-column prop="suId" align="center" label="mac地址" show-overflow-tooltip></el-table-column>
        <el-table-column prop="suVendor" align="center" label="SU厂家" show-overflow-tooltip>
          <template slot-scope="scope">
            <div v-if="scope.row.suVendor == 'ZNV'">力维</div>
            <div v-if="scope.row.suVendor == 'SAIERCOM'">赛尔</div>
          </template>
        </el-table-column>
        <el-table-column prop="deviceType" align="center" label="设备类型" show-overflow-tooltip></el-table-column>
        <el-table-column prop="devId" align="center" label="设备ID" show-overflow-tooltip></el-table-column>
        <el-table-column prop="deviceName" align="center" label="设备名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="deviceVendor" align="center" label="设备厂家" show-overflow-tooltip></el-table-column>
        <el-table-column prop="status" align="center" label="状态" show-overflow-tooltip>
          <template slot-scope="scope">
            <el-tag v-if="scope.row.status == 1" type="success">正常</el-tag>
            <el-tag v-if="scope.row.status == 2" type="danger">疑似退网</el-tag>
            <el-tag v-if="scope.row.status == 3" type="warning">已退网</el-tag>
          </template>
        </el-table-column>
        <el-table-column prop="numDayRetire" align="center" label="疑似退网天数" show-overflow-tooltip></el-table-column>
        <el-table-column prop="registTime" align="center" label="注册时间" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ formatDateTime(scope.row.registTime) }}
          </template>
        </el-table-column>
        <!-- <el-table-column align="center" label="操作">
          <template slot-scope="scope">
            <el-button style="color: skyblue; border: none;" size="mini">确认已退网</el-button>
          </template>
        </el-table-column> -->
      </el-table>
      <div class="pag">
        <el-pagination @size-change="handleSizeChange" background @current-change="handleCurrentChange"
          :current-page="currentPage" :page-sizes="[10, 20, 50]" :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="total">
        </el-pagination>
      </div>
    </template>

    <el-page-header v-if="['ZTDevice', 'ZZDevice'].includes(showContent)" @back="goback">
      <div slot="content" style="font-size: 14px;">
        {{ content }}
      </div>
    </el-page-header>
  </div>
</template>

<script>
import exportTableToExcel from './components/exportExcel.js'
export default {
  data() {
    return {
      // 查询条件下拉数据
      citylist: [],
      areaList: [],
      siteList: [],
      roomlist: [],
      deviceTypeList: [],
      cedianlist: [],
      cedianNameList: [],
      devoptions: [],
      tableLoading: false,
      queryForm: {
        areaCode: '',
        cityCode: '',

        roomId: '',
        siteId: '',
        deviceType: '',
        deviceName: '',
        suId: '',
        suIp: '',
        orderId: '',
        state: '',

      },
      deviceTableData: [],
      pageSize: 10,
      currentPage: 1,
      total: 0,
      content: '',
      currentRow: null,
      exportloading: false,
      role: '',
      cityDisabled: false,
      areaDisabled: false,

      showContent: 'list', // list 一级列表 ZTDevice 中台设备 ZZDevice 中资设备
      // 中资设备
      ZZDeviceList: [],
      ZZpageSize: 10,
      ZZcurrentPage: 1,
      ZZtotal: 0,

      // 中资设备
      ZTDeviceList: [],
      ZTpageSize: 10,
      ZTcurrentPage: 1,
      ZTtotal: 0,
    }
  },
  created() {
    this.queryCityList()
    this.getSite()
    this.getRoom()
    this.getDeviceType()
    this.queryData()
    this.getDeviceName()
  },
  methods: {
    // 格式化时间
    formatDateTime(dateTime) {
      if (!dateTime) return ''
      const date = new Date(dateTime)
      const year = date.getFullYear()
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const day = String(date.getDate()).padStart(2, '0')
      const hours = String(date.getHours()).padStart(2, '0')
      const minutes = String(date.getMinutes()).padStart(2, '0')
      const seconds = String(date.getSeconds()).padStart(2, '0')
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`
    },
    goback() {
      this.showContent = 'list'
      this.ZTcurrentPage = 1
      this.ZZcurrentPage = 1
      this.ZTtotal = 0
      this.ZZtotal = 0

    },
    // 一级列表导出
    async devExport() {
      this.exportloading = true
      let data = [
        ['地市', '区域', '局站', '机房', '中资设备数', '中台设备数', '核查时间']
      ]
      let params = {
        ...this.queryForm,
        current: 1,
        size: this.total
      }
      try {
        let res = await this.$api.checkSum(params)
        if (res.code == 200) {
          res.data.records.forEach(v => {
            let item = [
              v.cityName,
              v.areaName,
              v.stationName,
              v.roomName,
              v.ossNum,
              v.dhNum,
              v.statTime
            ]
            data.push(item)
          })
          exportTableToExcel(data, '中资资源核查表')
          this.exportloading = false
        } else {
          this.$message.error(res.msg)
          this.exportloading = false
        }
      } catch (error) {
        this.$message.error('导出异常')
        this.exportloading = false
      }

    },
    // 详情列表导出
    async detailExport(type) {
      this.exportloading = true

      let oss = [ // 中资
        ['地市', '区域', '局站', '机房', '资管编码', '设备编码', '设备名称', '投入使用时间']
      ]
      let ossKey = ['cityName', 'areaName', 'siteName', 'roomName', 'resManNo', 'deviceId', 'deviceName', 'registTime']
      let dh = [ // 中台
        ['地市', '区域', '局站', '机房', 'SU_ID', 'SU_IP', '设备ID', '设备名称', '注册时间']
      ]
      let dhKey = ['cityName', 'areaName', 'siteName', 'roomName', 'suId', 'suIp', 'deviceId', 'deviceName', 'registTime']
      let data = type == 'OSS' ? oss : dh
      let dataKey = type == 'OSS' ? ossKey : dhKey
      let fileName = type == 'OSS' ? '中资设备明细' : '中台设备明细'
      let params = {
        ...this.currentRow,
        type: type,
        current: 1,
        size: this.total
      }
      try {
        let res = await this.$api.checkDetail(params)
        if (res.code == 200) {
          res.data.records.forEach(v => {
            let item = []
            dataKey.forEach(key => {
              item.push(v[key])
            })
            data.push(item)
          })
          console.log(data);

          exportTableToExcel(data, fileName)
          this.exportloading = false
        } else {
          this.$message.error(res.msg)
          this.exportloading = false
        }
      } catch (error) {
        this.$message.error('导出异常')
        this.exportloading = false
      }

    },
    // 初始化地市
    queryCityList() {
      let params = {
        cityName: "",
        pageNum: 1,
        pageSize: 999
      }
      this.$api.queryCityList(params).then(res => {
        if (res.code == 200) {
          console.log(res.data.records);
          
          this.citylist = res.data.records
          this.cfgcitylist = res.data.records
          const temp = this.citylist.filter(v => v.cityName !== '陕西省')
          if (this.queryForm.cityCode) {
            this.areaList = this.cfgcitylist.find(v => v.cityCode == this.queryForm.cityCode).areaList
          } else {
            temp.forEach(v => {
              this.areaList.push(...v.areaList)
            })
          }

        }
      })
    },
    cityChange(val) {
      
      this.areaList = this.citylist.find(v => v.cityId == val).areaList
      this.getSite()
      this.getRoom()
      this.getDeviceType()
      this.getDeviceName()
    },
    devTypeChange() {
      this.getDeviceName()
    },
    async getDeviceType(val) {
      let res = await this.$api.queryDeviceType({
        deviceType: val,
        cityId: this.queryForm.cityId ? [this.queryForm.cityId] : [],
        areaId: this.queryForm.areaId ? [this.queryForm.areaId] : [],
        siteId: this.queryForm.siteId ? [this.queryForm.siteId] : [],
        roomId: this.queryForm.roomId ? [this.queryForm.roomId] : [],
        current: 1,
        size: 999
      })
      console.log("🚀 ~ getDeviceType ~ res:", res)
      if (res.code == 200) {
        console.log("🚀 ~ getDeviceType ~ res:", res)
        
        this.deviceTypeList = res.data.records
      } else {
        this.$message.error(res.msg)
      }
    },
    areaChange() {
      this.getSite()
      this.getDeviceType()
      this.getRoom()
      this.getDeviceName()
    },
    siteChange() {
      this.getRoom()
      this.getDeviceType()
      this.getDeviceName()
    },
    roomChange() {
      this.getDeviceType()
      this.getDeviceName()
    },

    getSite(val) {
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.siteList = []
      this.roomlist = []
      // this.deviceTypeList = []
      let params = {
        cityId: this.queryForm.cityCode,
        areaId: this.queryForm.areaCode,
        siteName: val,
        pageNum: 1,
        pageSize: 999
      }

      this.$api.querySiteInfo(params).then(res => {
        if (res.code == 200) {
          this.siteList = res.data.records
        }
      })
    },
    getRoom(val) {
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.roomlist = []
      // this.deviceTypeList = []
      let params = {
        cityId: this.queryForm.cityCode,
        areaId: this.queryForm.areaCode,
        siteId: this.queryForm.siteId,
        roomName: val,
        pageNum: 1,
        pageSize: 999
      }
      this.$api.queryRoomInfo(params).then(res => {
        console.log("🚀 ~ this.$api.queryRoomInfo ~ res:", res)
        if (res.code == 200) {
          this.roomlist = res.data
        }
      })
    },
    async getDeviceName(queryString) {
      let res = await this.$api.deviceNameQuery({
        deviceName: queryString,
        cityId: this.queryForm.cityId ? [this.queryForm.cityId] : [],
        areaId: this.queryForm.areaId ? [this.queryForm.areaId] : [],
        siteId: this.queryForm.siteId ? [this.queryForm.siteId] : [],
        roomId: this.queryForm.roomId ? [this.queryForm.roomId] : [],
        deviceType: this.queryForm.deviceType ? [this.queryForm.deviceType] : [],
        current: 1,
        size: 999
      })
      if (res.code == 200) {

        this.devoptions = res.data.records
      } else {
        this.$message.error(res.msg)

      }
    },
    queryFormReset() {
      this.areaList = []
      this.siteList = []
      this.roomlist = []
      this.queryForm = {
        areaCode: '',
        cityCode: '',
        roomId: '',
        siteId: '',
      }
      this.queryData()
      this.role = sessionStorage.getItem('role')
      this.queryForm.cityCode = sessionStorage.getItem('cityCode') ? sessionStorage.getItem('cityCode') : ''
      this.queryForm.areaCode = sessionStorage.getItem('areaCode') ? sessionStorage.getItem('areaCode') : ''

      if (this.role == '1') {
        this.cityDisabled = false
        this.areaDisabled = false
      }

      if (['94', '95'].includes(this.role)) {
        this.cityDisabled = true
      }
      this.queryCityList()
      this.getSite()
      this.getRoom()
      this.getDeviceType()
      this.getDeviceName()
      this.queryData()
      this.queryData(this.queryForm.current)

    },
    // 一级列表
    queryData(pageNum, pageSize) {
      this.tableLoading = true
      if (pageNum) this.currentPage = pageNum
      if (pageSize) this.pageSize = pageSize
      let params = {
        ...this.queryForm,
        pageNum: pageNum || this.currentPage,
        pageSize: pageSize || this.pageSize
      }
      this.$api.checkDevice(params).then(res => {
        if (res.code == 200) {
          this.deviceTableData = res.data.records
          this.tableLoading = false
          this.total = res.data.total
        } else {
          this.tableLoading = false
          this.$message.error(res.msg)
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.queryData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.queryData()
    },
    // 中资设备
    queryZZData(row) {
      this.currentRow = row
      this.ZZDeviceList = []
      this.ZZtotal = []
      this.showContent = 'ZZDevice'
      this.content = "中资设备明细"
      this.ZZtableLoading = true

      let params = {
        ...row,
        type: 'OSS',
        current: this.ZZcurrentPage,
        size: this.ZZpageSize
      }
      this.$api.checkDetail(params).then(res => {
        if (res.code == 200) {
          this.ZZDeviceList = res.data.records
          this.ZZtotal = res.data.total
          this.ZZtableLoading = false

        } else {
          this.ZZtableLoading = false
          this.$message.error(res.msg)
        }
      })
    },
    ZZhandleSizeChange(val) {
      this.ZZpageSize = val
      this.queryZZData(this.currentRow)
    },
    ZZhandleCurrentChange(val) {
      this.ZZcurrentPage = val
      this.queryZZData(this.currentRow)
    },
    // 中台设备
    queryZTData(row) {
      this.currentRow = row
      this.ZTDeviceList = []
      this.showContent = 'ZTDevice'
      this.content = "中台设备明细"
      this.ZTtableLoading = true
      let params = {
        ...row,
        type: 'DH',
        current: this.ZTcurrentPage,
        size: this.ZTpageSize
      }
      this.$api.checkDetail(params).then(res => {
        if (res.code == 200) {
          this.ZTDeviceList = res.data.records
          this.ZTtableLoading = false
          this.ZTtotal = res.data.total
        } else {
          this.ZTtableLoading = false
          this.$message.error(res.msg)
        }
      })
    },
    ZThandleSizeChange(val) {
      this.ZTpageSize = val
      this.queryZTData(this.currentRow)
    },
    ZThandleCurrentChange(val) {
      this.ZTcurrentPage = val
      this.queryZTData(this.currentRow)
    }
  }
}
</script>

<style lang="scss" scoped>
.ad {
  display: flex;
  gap: 6px;

  i {
    cursor: pointer;
  }
}

.pag {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 12px;
}

.selectWrap {
  width: 600px;
  height: 60px;
  margin-left: 100px;
  overflow: scroll;
  margin-bottom: 16px;
  //box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
  border: 1px solid #d7dae2;
}
</style>