<template>
  <div class=" calendar-list-container disposefiles addentry" v-loading="listLoading">
    <div class="filter-container">
      <el-form :inline="true" size="small" ref="listQuery" class="demo-form-inline" :model="listQuery"
        label-width="80px">
        <el-form-item class="disposeformitem" label="账户:">
          <el-input class="filter-item" v-model.trim="listQuery.userName" clearable placeholder="账户"></el-input>
        </el-form-item>
        <el-form-item class="disposeformitem" label="人员姓名:">
          <el-input class="filter-item" v-model.trim="listQuery.name" clearable placeholder="人员姓名"></el-input>
        </el-form-item>
        <el-form-item class="disposeformitem" style="margin-bottom:0px;">
          <el-button type="primary" @click="handleselect">查询</el-button>
          <el-button @click="handret">重置</el-button>
        </el-form-item>
      </el-form>


    </div>
    <div class="listTables">
      <el-table :key="tableKey" size="small" :data="listtable" v-loading="listLoadTable" border fit
        highlight-current-row style="width: 100%;height:auto;" height="400" ref="singleTable">
        <el-table-column type="selection" width="40"></el-table-column>
        <el-table-column prop="distric_name" label="地市" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="area_name" label="区县" min-width="80" show-overflow-tooltip></el-table-column>
        <el-table-column prop="username" label="账户" min-width="130" show-overflow-tooltip></el-table-column>
        <el-table-column prop="name" label="姓名" min-width="100" show-overflow-tooltip></el-table-column>
        <el-table-column prop="mobile_phone" min-width="150" show-overflow-tooltip label="手机号"></el-table-column>
        <el-table-column prop="description" show-overflow-tooltip label="备注" min-width="150"></el-table-column>
        <el-table-column label="操作" width="80" fixed="right" v-if="typflg == '1'">
          <template slot-scope="scope">
            <el-button @click="deleteSelected(scope.row)" type="text" size="small">删除</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 分页器 -->
    <div class="pagination cleafix tablPaginat" style="margin-top: 10px;">
      <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
        :page-sizes="[5, 10, 20, 50]" :page-size="pageSize" :current-page.sync="pageIndex"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </div>
    <div>
      <el-row style="margin: 10px 0px;">
        <el-col :span="24" class="text-center" v-show="typflg == '2'">
          <el-button type="primary" size="mini" v-waves @click="handleFilter('3')">提交并关闭</el-button>
          <el-button type="primary" size="mini" v-waves @click="handleFilter('2')">提交</el-button>
          <el-button size="mini" v-waves @click="handcancel">取消</el-button>
        </el-col>
      </el-row>
    </div>
  </div>
</template>

<script>
import {
  getUserByNotGroup,//查用户人员
  addGroupUser,//保存人员
  getUserByGroup,//查看用户
  delGroupUser,//删除关联的用户
} from "@/api/admin/group/index";
import { mapGetters } from 'vuex';
export default {
  name: 'addentry',
  props: {
    closeDiadl: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      tableKey: 0,
      listtable: [],//表格
      listQuery: {
        name: null,//人员姓名
        userName: null,//账户
      },
      listLoading: false,
      listLoadTable: false,
      pageIndex: 1,
      pageSize: 10,
      total: 0,
      departNo: null,
      typflg: null,
      groupIds: null,
    }
  },
  watch: {

  },
  created() {

  },
  mounted() {

  },
  destroyed() {

  },
  methods: {
    handlick(sencId, typ) {
      //  console.log(sencId);
      let that = this;
      that.departNo = sencId;
      that.groupIds = sencId;
      that.typflg = typ;
      // that.city_id = cityId;
      this.handleselect();
    },
    handleselect() {//查询
      let that = this;
      that.listtable = [];

      let params = {
        page: this.pageIndex,
        limit: this.pageSize,
        'name': that.listQuery.name,
        'username': that.listQuery.userName,
        'groupId': that.groupIds,
      };
      if (that.typflg == '1') {
        getUserByGroup(params).then(data => {
          if (data.status == 200) {
            that.listtable = data.data.rows;
            that.total = data.data.total;
          }

          //  console.log(data);

        }).catch(err => {
          // that.showloding = false;
        })

      } else if (that.typflg == '2') {
        getUserByNotGroup(params).then(data => {
          that.listtable = data.rows;
          that.total = data.total;
          //  console.log(data);

        }).catch(err => {
          // that.showloding = false;
        })
      }



    },
    // 分页pagesize
    handleSizeChange(val) {
      this.pageIndex = 1;
      this.pageSize = val;
      this.handleselect();
    },
    // 分页pageindex
    handleCurrentChange(val) {
      this.pageIndex = val;
      this.handleselect();
    },
    handret() {//重置
      let that = this;
      that.listQuery = {
        name: null,//人员姓名
        userName: null,//账户
      };
      that.pageIndex = 1;
      that.pageSize = 10;
      that.total = 0;
      that.listtable = [];
      //  this.handleselect();
      that.listLoading = false;

    },

    handcancel(ruleF) {//取消
      if (this.closeDiadl) {
        this.closeDiadl('1')
      }
    },
    handleFilter(typ) {
      let that = this;
      let arrlist = that.$refs.singleTable.selection
      if (!arrlist.length) {
        that.$message({
          showClose: true,
          message: '请选择一条信息',
          type: 'warning',
          duration: 1500
        });
        return;
      };
      that.listLoading = true;
      let userId = [];
      arrlist.forEach((item) => {
        userId.push(item.id)

      });
      userId = userId.toString();
      //  console.log(devId)
      let params = {
        'groupId': that.groupIds,
        'userId': userId,
      };

      addGroupUser(params).then(data => {
        // console.log(data);
        if (data.flag == '1') {
          this.$message({
            showClose: true,
            message: '添加成功',
            type: 'success',
            duration: 1000
          });

          setTimeout(function () {
            that.closeDiadl(typ);
            if (typ == '3') {
              that.handreset();
            } else {
              that.handleSizeChange(10);
            }
            that.listLoading = false;
          }, 500)
        } else {
          that.listLoading = false;
          this.$message({
            showClose: true,
            message: '添加失败',
            type: 'warning',
            duration: 1000
          });
        }

      }).catch(err => {
        // that.showloding = false;
        that.$message({
          showClose: true,
          message: '添加失败',
          type: 'warning',
          duration: 1000
        });
      })
    },
    deleteSelected(rows) {//删除人员
      let that = this;
      let obj = {
        'groupId': that.groupIds,
        'userId': rows.id,
      };
      delGroupUser(obj).then(data => {
        if (data.flag == '1') {
          this.$message({
            showClose: true,
            message: '删除成功',
            type: 'success',
            duration: 1000
          });
          that.handleselect();
        } else {
          this.$message({
            showClose: true,
            message: '删除失败',
            type: 'warning',
            duration: 1000
          });
        }
      }).catch((err) => {
        that.$message({
          showClose: true,
          message: '删除失败',
          type: 'warning',
          duration: 1000
        });
      })

    },
    //清空
    handreset() {
      let that = this;
      that.listQuery = {
        name: null,//人员姓名
        userName: null,//账户
      };
      that.pageIndex = 1;
      that.pageSize = 10;
      that.total = 0;
      that.listtable = [];
      // console.log(that.listtable)
      that.listLoading = false;

    },
  }
}
</script>

<!-- Add "scoped" attribute to limit CSS to this component only -->
<style scoped>
.listTables {
  margin-top: 10px;

}
</style>
<style lang="scss">
// @import "src/styles/common.scss";
.dialogFoot {
  text-align: right;
  margin: 10px 0px 20px 0px;
}

.disposeformitem {
  margin-bottom: 15px;

  .el-form-item__label {
    font-size: 13px;
    font-weight: 700;
  }

  .el-form-item__content {
    width: 180px;
  }

}

.disposeformitemb {
  .el-form-item__label {
    font-size: 13px;
    font-weight: 700;
  }

  width: 100%;
  text-align: center;
}

.addplandialog {
  .el-dialog__body {
    padding: 0px 20px 10px 20px;
  }

  .el-dialog__header {
    padding: 10px 20px;
  }

  .el-dialog__title {
    line-height: 20px;
    font-size: 16px;
    color: #303133;
  }

  .el-dialog__headerbtn {
    top: auto;
  }
}
</style>
