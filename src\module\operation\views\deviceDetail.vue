<template>
  <div style="height: 100%;">
    <el-tabs v-model="tabActiveName" @tab-click="handleClick">
      <el-tab-pane label="当前值" name="first1" :lazy="true"></el-tab-pane>
      <el-tab-pane label="告警级别设置" name="first2" :lazy="true"></el-tab-pane>
      <el-tab-pane label="阈值设置" name="first5" :lazy="true"></el-tab-pane>
      <el-tab-pane label="设备远程遥控" name="first6" :lazy="true"></el-tab-pane>
      <el-tab-pane label="活动告警" name="third" :lazy="true"></el-tab-pane>
      <el-tab-pane label="历史告警" name="second" :lazy="true"></el-tab-pane>
    </el-tabs>

    <!-- 当前值 -->
    <div v-if="tabActiveName == 'first1'">
      <!-- 一级列表 -->
      <div v-if="!historyDataFlag">
        <el-form :inline="true" :model="queryForm" size="small" class="demo-form-inline">

          <el-form-item label="测点名称">
            <el-input v-model="queryForm.signalName" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="queryData(1, 100, ['1'])">查询</el-button>
            <el-button @click="queryFormReset">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="deviceTableData1" header-row-class-name="myHeaderClass" size="small" v-loading="tableLoading">

          <el-table-column prop="signalName" align="center" label="测点名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="value" align="center" label="当前值"></el-table-column>
          <el-table-column prop="unit" align="center" label="单位"></el-table-column>
          <el-table-column prop="monitorId" align="center" label="监控ID" show-overflow-tooltip></el-table-column>
          <el-table-column prop="updateTime" align="center" label="更新时间" show-overflow-tooltip></el-table-column>
          <el-table-column prop="" label="操作" width="200">
            <template slot-scope="scope">
              <div class="ad">
                <!-- 
                遥测-采集 AI  1
                遥测-告警 AIAlarm 2
                遥信-开关 DI 3
                遥信-告警 DIAlarm  4
                遥控-动作 DO 5
                遥控-参致 AO 6
              -->
                <!-- {{btncloseDevice }} {{['5'].includes(scope.row.type)}} {{scope.row.statusCode}} -->
                <!-- 历史记录 1 3-->

                <el-tooltip v-if="btnshowHistory && ['1', '3'].includes(scope.row.type)" effect="dark" content="历史记录"
                  placement="top">
                  <i class="el-icon-time" style="font-size: 18px;" @click="showHistory(scope.row)"></i>
                </el-tooltip>

                <!-- 操作日志 2456 -->
                <el-tooltip v-if="btnoperateHistory && ['2', '4', '5', '6'].includes(scope.row.type)" effect="dark"
                  content="操作日志" placement="top">
                  <i @click="operateHistory(scope.row)" class="el-icon-tickets" style="font-size: 18px;"></i>
                </el-tooltip>

              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pag">
          <el-pagination @size-change="handleSizeChange" background @current-change="handleCurrentChange"
            :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="total">
          </el-pagination>
        </div>
      </div>

      <el-page-header v-if="historyDataFlag" @back=" goback(1)">
        <div slot="content" style="font-size: 14px;">
          {{ currentContent }}
        </div>
      </el-page-header>
      <!-- 历史性能数据 -->
      <div v-if="historyDataFlag" style="margin-top: 16px;">
        <el-form :inline="true" :model="htquery" size="small" ref="htquery" class="demo-form-inline">
          <el-form-item label="时间：">
            <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="htquery.date" type="datetimerange"
              range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>

          <el-form-item>
            <el-button @click="htquery.date = []; queryHtData()">重置</el-button>
            <el-button type="primary" @click="queryHtData">查询</el-button>
            <el-button type="primary" :loading="exportloading" @click="exportHistoryExcel('历史性能数据')">导出</el-button>
            <el-button v-if="!trendChartFlag" type="primary" @click="showTrend">趋势图数据</el-button>
            <el-button v-if="trendChartFlag" type="primary" @click="trendChartFlag = false">返回列表</el-button>
          </el-form-item>
        </el-form>

        <template v-if="!trendChartFlag">
          <el-table :data="historyTableData" v-loading="hyDataLoading" size="small" id="historyTableData">
            <el-table-column prop="city_name" align="center" label="地市"></el-table-column>
            <el-table-column prop="area_name" align="center" label="区域" show-overflow-tooltip></el-table-column>
            <el-table-column prop="site_name" align="center" label="局站" show-overflow-tooltip></el-table-column>
            <el-table-column prop="room_name" align="center" label="机房" show-overflow-tooltip></el-table-column>
            <el-table-column prop="device_name" align="center" label="设备类型"></el-table-column>
            <el-table-column prop="device_name" align="center" label="设备名称"></el-table-column>
            <el-table-column prop="remark" align="center" label="测点类型" show-overflow-tooltip></el-table-column>
            <el-table-column prop="signal_id" align="center" label="测点ID"></el-table-column>
            <el-table-column prop="signal_name" align="center" label="测点名称"></el-table-column>
            <el-table-column prop="define_value" align="center" label="测点值"></el-table-column>
            <el-table-column prop="report_time" align="center" label="上报时间" show-overflow-tooltip></el-table-column>
          </el-table>
          <div class="pag">
            <el-pagination @size-change="hthandleSizeChange" background @current-change="hthandleCurrentChange"
              :current-page="htcurrentPage" :page-sizes="[10, 20, 50]" :page-size="htpageSize"
              layout="total, sizes, prev, pager, next, jumper" :total="httotal">
            </el-pagination>
          </div>
        </template>
        <div id="trend" v-show="trendChartFlag" style="width: 100%; height: 400px;"> 暂无数据</div>
        <!-- <div v-if="!trendChartFlag" style="text-align: center;margin-top: 24px;font-size: 16px;">暂无数据</div> -->
      </div>
    </div>
    <!-- 告警级别设置 -->
    <div v-if="tabActiveName == 'first2'">
      <!-- 一级列表 -->
      <div v-if="!warnLevelSetFlag">
        <el-form :inline="true" :model="queryForm" size="small" class="demo-form-inline">

          <el-form-item label="测点名称">
            <el-input v-model="queryForm.signalName" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="queryData(1, 100, ['2', '4'])">查询</el-button>
            <el-button @click="queryFormReset">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="deviceTableData2" header-row-class-name="myHeaderClass" size="small" v-loading="tableLoading"
          :cell-class-name="warnLevelClassFn">

          <el-table-column prop="signalName" align="center" label="测点名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="monitorId" align="center" label="监控ID" show-overflow-tooltip></el-table-column>
          <!-- <el-table-column prop="alarmLevel" align="center" label="告警级别"></el-table-column> -->
          <el-table-column prop="alarmLevel" align="center" label="告警级别">
            <template slot-scope="scope">
              {{ warnLevelOptions.find(v => v.level == scope.row.alarmLevel)?.name || '六级告警' }}
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" align="center" label="更新时间" show-overflow-tooltip></el-table-column>
          <el-table-column prop="" label="操作" width="200">
            <template slot-scope="scope">
              <div class="ad">
                <!-- 
                遥测-采集 AI  1
                遥测-告警 AIAlarm 2
                遥信-开关 DI 3
                遥信-告警 DIAlarm  4
                遥控-动作 DO 5
                遥控-参致 AO 6
              -->
                <!-- {{btncloseDevice }} {{['5'].includes(scope.row.type)}} {{scope.row.statusCode}} -->
                <!-- 历史记录 1 3-->

                <!-- 操作日志 2456 -->
                <el-tooltip v-if="btnoperateHistory && ['2', '4', '5', '6'].includes(scope.row.type)" effect="dark"
                  content="操作日志" placement="top">
                  <i @click="operateHistory(scope.row)" class="el-icon-tickets" style="font-size: 18px;"></i>
                </el-tooltip>

                <!-- 告警级别设置 24-->
                <el-tooltip effect="dark" v-if="btnwarnLevelSet && ['2', '4'].includes(scope.row.type)" content="告警级别设置"
                  placement="top">
                  <i @click="warnLevelSet(scope.row)" class="el-icon-setting" style="font-size: 18px;"></i>
                </el-tooltip>

              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pag">
          <el-pagination @size-change="handleSizeChange" background @current-change="handleCurrentChange"
            :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="total">
          </el-pagination>
        </div>
      </div>

      <el-page-header v-if="warnLevelSetFlag" @back="goback(2)">
        <div slot="content" style="font-size: 14px;">
          {{ warnLevelContent }}
        </div>
      </el-page-header>

      <!-- 操作日志 -->
      <el-dialog :title="dialogTitle" :visible.sync="dialogTableVisible">
        <el-table size='small' :data="oprTableData" :height="250">
          <el-table-column property="updateUserName" label="操作人"></el-table-column>
          <el-table-column property="orderNum" label="工单编号"></el-table-column>
          <el-table-column property="orderType" label="操作动作">
            <template slot-scope="scope">
              <div v-if="scope.row.orderType == 1">单站阈值设置</div>
              <div v-if="scope.row.orderType == 2">单站告警级别设置</div>
              <div v-if="scope.row.orderType == 3">批量告警级别设置</div>
              <div v-if="scope.row.orderType == 4">批量阈值设置</div>
            </template>
          </el-table-column>
          <el-table-column property="sts" label="状态">
            <template slot-scope="scope">
              <div v-if="scope.row.sts == 0">初始化</div>
              <div v-if="scope.row.sts == 1">运行中</div>
              <div v-if="scope.row.sts == 2" style="color: green">结束</div>
              <div v-if="scope.row.sts == 3" style="color: red">撤销</div>
            </template>
          </el-table-column>
          <el-table-column property="startTime" label="操作时间"></el-table-column>
        </el-table>
        <div class="pag">
          <el-pagination @size-change="oprhandleSizeChange" background @current-change="oprhandleCurrentChange"
            :current-page="oprcurrentPage" :page-sizes="[10, 20, 50]" :page-size="oprpageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="oprtotal">
          </el-pagination>
        </div>
      </el-dialog>

      <!-- 告警级别设置 -->
      <div v-if="warnLevelSetFlag">

        <el-radio-group size="small" v-model="warnLevelSetActive" @change="warnLevelSetTabChange"
          style="margin: 12px 0;">
          <el-radio-button label="当前配置">当前配置</el-radio-button>
          <el-radio-button label="配置日志">配置日志</el-radio-button>
        </el-radio-group>

        <el-form v-if="warnLevelSetActive == '当前配置'" :model="warnLevelSetData" label-width="100px" size="small">
          <el-form-item style="margin-bottom: 0px;" label="地市："> {{ warnLevelSetData.cityName }}</el-form-item>
          <el-form-item style="margin-bottom: 0px;" label="区域："> {{ warnLevelSetData.areaName }}</el-form-item>
          <el-form-item style="margin-bottom: 0px;" label="局站："> {{ warnLevelSetData.siteName }}</el-form-item>
          <el-form-item style="margin-bottom: 0px;" label="机房："> {{ warnLevelSetData.roomName }}</el-form-item>
          <el-form-item style="margin-bottom: 0px;" label="设备类型："> {{ warnLevelSetData.devName }}</el-form-item>
          <el-form-item style="margin-bottom: 0px;" label="设备名称："> {{ warnLevelSetData.devName }}</el-form-item>
          <el-form-item style="margin-bottom: 0px;" label="监测点名称："> {{ warnLevelSetData.signalName }}</el-form-item>
          <el-row>
            <el-col :span="6">
              <el-form-item label="告警级别：">
                <el-select v-model="warnLevelSetData.alarmLevel" placeholder="请选择">
                  <el-option v-for="item in warnLevelOptions" :key="item.level" :label="item.name" :value="item.level">
                  </el-option>
                </el-select>
              </el-form-item>
            </el-col>
          </el-row>

          <el-form-item>
            <el-button @click="saveWarningConfig" :loading="submitWarnLevelLoading" type="primary">确定</el-button>
          </el-form-item>
        </el-form>

        <div v-if="warnLevelSetActive == '配置日志'">
          <el-table :data="warnLevelSetHistoryData">
            <el-table-column prop="orderNum" align="center" label="工单编号" width="200">
              <template slot-scope="scope">
                <div v-copy="scope.row.orderNum" style="cursor: pointer;">
                  {{ scope.row.orderNum }}
                </div>
              </template>

            </el-table-column>

            <el-table-column prop="orderSts" width="90" align="center" label="工单状态">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.orderSts == 1" size="small" type="success">运行中</el-tag>
                <el-tag v-if="scope.row.orderSts == 2" size="small" type="info">结束</el-tag>
                <el-tag v-if="scope.row.orderSts == 3" size="small" type="error">废弃</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="orderName" align="center" label="工单名称" width="200"></el-table-column>

            <!-- <el-table-column align="center" label="修改前" width="120">
              <template slot-scope="scope">
                <el-popover placement="right" width="300" trigger="click">
                  <el-descriptions title="修改前" :column="1">
                    <el-descriptions-item label="地市">{{ scope.row.beforeJson.cityName }}</el-descriptions-item>
                    <el-descriptions-item label="区县">{{ scope.row.beforeJson.areaName }}</el-descriptions-item>
                    <el-descriptions-item label="局站">{{ scope.row.beforeJson.siteName }}</el-descriptions-item>
                    <el-descriptions-item label="机房">{{ scope.row.beforeJson.roomName }}</el-descriptions-item>
                    <el-descriptions-item label="设备类型">{{ scope.row.beforeJson.devName }}</el-descriptions-item>
                    <el-descriptions-item label="设备名称">{{ scope.row.beforeJson.devName }}</el-descriptions-item>
                    <el-descriptions-item label="测点名称">{{ scope.row.beforeJson.signalName }}</el-descriptions-item>
                    <el-descriptions-item label="告警级别">{{ scope.row.beforeJson.alarmLevel }}</el-descriptions-item>
                  </el-descriptions>
                  <el-button type="text" slot="reference">详情</el-button>
                </el-popover>
              </template>
            </el-table-column> -->
            <el-table-column align="center" label="审批流程详情">
              <template slot-scope="scope">
                <el-steps :align-center="true" finish-status="success">
                  <template v-if="scope.row.orderSts == 3">
                    <el-step v-for="(item, index) in scope.row.transferInfos" :status="'wait'" :key="index"
                      :title="item.handlerUser || '-'">
                      <template slot="description">
                        <div> {{ item.handleStatus == 1 ? '' : item.handleTime }}</div>
                        <div v-if="item.handleStatus == 1">待审批</div>
                        <div> {{ item.transferNodeName || '-' }}</div>

                      </template>
                    </el-step>
                  </template>
                  <template v-else>
                    <el-step v-for="(item, index) in scope.row.transferInfos"
                      :status="[0, 2].includes(item.handleStatus) ? 'success' : item.handleStatus == 3 ? 'finish' : 'wait'"
                      :key="index" :title="item.handlerUser || '-'">
                      <template slot="description">
                        <div> {{ item.handleStatus == 1 ? '' : item.handleTime }}</div>
                        <div v-if="item.handleStatus == 1">待审批</div>
                        <div> {{ item.transferNodeName || '-' }}</div>
                      </template>
                    </el-step>
                  </template>
                </el-steps>
              </template>
            </el-table-column>
            <el-table-column align="center" label="操作" width="120">
              <template slot-scope="scope">
                <el-popover placement="right" :width="scope.row.beforeJson ? 540 : 240" trigger="click">
                  <div style="display: flex;">
                    <el-descriptions v-if="scope.row.beforeJson" title="修改前" :column="1">
                      <el-descriptions-item label="地市">{{ scope.row.beforeJson.cityName }}</el-descriptions-item>
                      <el-descriptions-item label="区县">{{ scope.row.beforeJson.areaName }}</el-descriptions-item>
                      <el-descriptions-item label="局站">{{ scope.row.beforeJson.siteName }}</el-descriptions-item>
                      <el-descriptions-item label="机房">{{ scope.row.beforeJson.roomName }}</el-descriptions-item>
                      <el-descriptions-item label="设备类型">{{ scope.row.beforeJson.devName }}</el-descriptions-item>
                      <el-descriptions-item label="设备名称">{{ scope.row.beforeJson.devName }}</el-descriptions-item>
                      <el-descriptions-item label="测点名称">{{ scope.row.beforeJson.signalName }}</el-descriptions-item>
                      <el-descriptions-item label="告警级别">{{
                        warnLevelOptions.find(s => s.level == scope.row.beforeJson.alarmLevel)?.name ||
                        scope.row.beforeJson.alarmLevel

                      }}</el-descriptions-item>
                    </el-descriptions>
                    <img :src="arrow" v-if="scope.row.beforeJson" style="opacity: 0.6;margin: 0 24px;" alt="">
                    <el-descriptions title="修改后" :column="1">
                      <el-descriptions-item label="地市">{{ scope.row.afterJson.cityName }}</el-descriptions-item>
                      <el-descriptions-item label="区县">{{ scope.row.afterJson.areaName }}</el-descriptions-item>
                      <el-descriptions-item label="局站">{{ scope.row.afterJson.siteName }}</el-descriptions-item>
                      <el-descriptions-item label="机房">{{ scope.row.afterJson.roomName }}</el-descriptions-item>
                      <el-descriptions-item label="设备类型">{{ scope.row.afterJson.devName }}</el-descriptions-item>
                      <el-descriptions-item label="设备名称">{{ scope.row.afterJson.devName }}</el-descriptions-item>
                      <el-descriptions-item label="测点名称">{{ scope.row.afterJson.signalName }}</el-descriptions-item>
                      <el-descriptions-item label="告警级别" v-if="scope.row.beforeJson"
                        :contentClassName="scope.row.afterJson.alarmLevel !== scope.row.beforeJson.alarmLevel ? 'my-content' : ''">
                        {{
                          warnLevelOptions.find(s => s.level == scope.row.afterJson.alarmLevel)?.name ||
                          scope.row.afterJson.alarmLevel
                        }}
                        <el-tag v-if="scope.row.afterJson.alarmLevel !== scope.row.beforeJson.alarmLevel" type="danger"
                          size="small">变动</el-tag>
                      </el-descriptions-item>
                      <el-descriptions-item v-else label="告警级别">{{
                        warnLevelOptions.find(s => s.level == scope.row.afterJson.alarmLevel)?.name ||
                        scope.row.afterJson.alarmLevel
                      }}</el-descriptions-item>
                    </el-descriptions>
                  </div>
                  <el-button type="text" slot="reference">详情</el-button>
                </el-popover>
              </template>
            </el-table-column>
          </el-table>
          <div class="pag">
            <el-pagination @size-change="warnLevelHandleSizeChange" background
              @current-change="warnLevelHandleCurrentChange" :current-page="warnLevelCurrentPage"
              :page-sizes="[10, 20, 50]" :page-size="warnLevelPageSize" layout="total, sizes, prev, pager, next, jumper"
              :total="warnLevelTotal">
            </el-pagination>
          </div>
        </div>
      </div>
    </div>
    <!-- 阈值设置 -->
    <div v-if="tabActiveName == 'first5'">
      <!-- 一级列表 -->
      <div v-if="!deviceThresholdFlag">
        <el-form :inline="true" :model="queryForm" size="small" class="demo-form-inline">

          <el-form-item label="测点名称">
            <el-input v-model="queryForm.signalName" clearable></el-input>
          </el-form-item>
          <el-form-item>
            <el-button type="primary" @click="queryData(1, 100, ['6'])">查询</el-button>
            <el-button @click="queryFormReset">重置</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="deviceTableData6" header-row-class-name="myHeaderClass" size="small" v-loading="tableLoading">

          <el-table-column prop="signalName" align="center" label="测点名称" show-overflow-tooltip></el-table-column>
          <el-table-column prop="HLimit" align="center" label="上限" show-overflow-tooltip></el-table-column>
          <el-table-column prop="LLimit" align="center" label="下限" show-overflow-tooltip></el-table-column>
          <el-table-column prop="SHLimit" align="center" label="过高上限" show-overflow-tooltip></el-table-column>
          <el-table-column prop="SLLimit" align="center" label="过低上限" show-overflow-tooltip></el-table-column>

          <el-table-column prop="monitorId" align="center" label="监控ID" show-overflow-tooltip></el-table-column>
          <el-table-column prop="updateTime" align="center" label="更新时间" show-overflow-tooltip></el-table-column>
          <el-table-column prop="" label="操作" width="200">
            <template slot-scope="scope">
              <div class="ad">
                <!--
                遥测-采集 AI  1
                遥测-告警 AIAlarm 2
                遥信-开关 DI 3
                遥信-告警 DIAlarm  4
                遥控-动作 DO 5
                遥控-参致 AO 6
              -->
                <!-- {{btncloseDevice }} {{['5'].includes(scope.row.type)}} {{scope.row.statusCode}} -->

                <!-- 历史记录 1 3-->
                <el-tooltip v-if="btnshowHistory && ['1', '3'].includes(scope.row.type)" effect="dark" content="历史记录"
                  placement="top">
                  <i class="el-icon-time" style="font-size: 18px;" @click="showHistory(scope.row)"></i>
                </el-tooltip>

                <!-- 操作日志 2456 -->
                <el-tooltip v-if="btnoperateHistory && ['2', '4', '5', '6'].includes(scope.row.type)" effect="dark"
                  content="操作日志" placement="top">
                  <i @click="operateHistory(scope.row)" class="el-icon-tickets" style="font-size: 18px;"></i>
                </el-tooltip>

                <!-- 阈值设置 6-->
                <el-tooltip v-if="btndeviceThreshold && ['6'].includes(scope.row.type)" effect="dark" content="阈值设置"
                  placement="top">
                  <i @click="deviceThreshold(scope.row)" class="el-icon-coordinate " style="font-size: 18px;"></i>
                </el-tooltip>

              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pag">
          <el-pagination @size-change="handleSizeChange" background @current-change="handleCurrentChange"
            :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="total">
          </el-pagination>
        </div>
      </div>

      <el-page-header v-if="deviceThresholdFlag" @back="goback(5)">
        <div slot="content" style="font-size: 14px;">
          {{ deviceContent }}
        </div>
      </el-page-header>

      <!-- 操作日志 -->
      <el-dialog :title="dialogTitle" :visible.sync="dialogTableVisible">
        <el-table size='small' :data="oprTableData" :height="250">
          <el-table-column property="updateUserName" label="操作人"></el-table-column>
          <el-table-column property="orderNum" label="工单编号"></el-table-column>
          <el-table-column property="orderType" label="操作动作">
            <template slot-scope="scope">
              <div v-if="scope.row.orderType == 1">单站阈值设置</div>
              <div v-if="scope.row.orderType == 2">单站告警级别设置</div>
              <div v-if="scope.row.orderType == 3">批量告警级别设置</div>
              <div v-if="scope.row.orderType == 4">批量阈值设置</div>
            </template>
          </el-table-column>
          <el-table-column property="sts" label="状态">
            <template slot-scope="scope">
              <div v-if="scope.row.sts == 0">初始化</div>
              <div v-if="scope.row.sts == 1">运行中</div>
              <div v-if="scope.row.sts == 2" style="color: green">结束</div>
              <div v-if="scope.row.sts == 3" style="color: red">撤销</div>
            </template>
          </el-table-column>
          <el-table-column property="startTime" label="操作时间"></el-table-column>
        </el-table>
        <div class="pag">
          <el-pagination @size-change="oprhandleSizeChange" background @current-change="oprhandleCurrentChange"
            :current-page="oprcurrentPage" :page-sizes="[10, 20, 50]" :page-size="oprpageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="oprtotal">
          </el-pagination>
        </div>
      </el-dialog>

      <!-- 设备阈值配置 -->
      <div v-if="deviceThresholdFlag">

        <el-radio-group size="small" v-model="deviceThresholdActive" @change="deviceThresholdtTabChange"
          style="margin: 12px 0;">
          <el-radio-button label="当前配置">当前配置</el-radio-button>
          <el-radio-button label="配置日志">配置日志</el-radio-button>
        </el-radio-group>

        <el-form v-if="deviceThresholdActive == '当前配置'" :model="deviceThresholdData" label-width="100px" size="small">
          <el-form-item style="margin-bottom: 0px;" label="地市："> {{ deviceThresholdData.cityName }}</el-form-item>
          <el-form-item style="margin-bottom: 0px;" label="区域："> {{ deviceThresholdData.areaName }}</el-form-item>
          <el-form-item style="margin-bottom: 0px;" label="局站："> {{ deviceThresholdData.siteName }}</el-form-item>
          <el-form-item style="margin-bottom: 0px;" label="机房："> {{ deviceThresholdData.roomName }}</el-form-item>
          <el-form-item style="margin-bottom: 0px;" label="设备类型："> {{ deviceThresholdData.deviceTypeName
            }}</el-form-item>
          <el-form-item style="margin-bottom: 0px;" label="设备名称："> {{ deviceThresholdData.devName }}</el-form-item>
          <el-form-item style="margin-bottom: 0px;" label="监测点名称："> {{ deviceThresholdData.signalName }}</el-form-item>
          <el-row>
            <el-col :span="6">
              <el-form-item label="上限：">
                <el-input v-model="deviceThresholdData.HLimit"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="下限：">
                <el-input v-model="deviceThresholdData.LLimit"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <el-row>
            <el-col :span="6">
              <el-form-item label="过高上限：">
                <el-input v-model="deviceThresholdData.SHLimit"></el-input>
              </el-form-item>
            </el-col>
            <el-col :span="6">
              <el-form-item label="过低下限：">
                <el-input v-model="deviceThresholdData.SLLimit"></el-input>
              </el-form-item>
            </el-col>
          </el-row>
          <!-- <el-row>
            <el-col :span="6">
              <el-form-item label="设置值：">
                <el-input v-model="deviceThresholdData.SetValue"></el-input>
              </el-form-item>
            </el-col>
          </el-row> -->
          <el-form-item>
            <el-button @click="sdeviceThresholdConfig" :loading="submitThresholdLoading" type="primary">确定</el-button>
          </el-form-item>
        </el-form>

        <div v-if="deviceThresholdActive == '配置日志'">
          <el-table :data="deviceThresholdHistoryData">
            <el-table-column prop="orderNum" align="center" label="工单编号" width="200">
              <template slot-scope="scope">
                <div v-copy="scope.row.orderNum" style="cursor: pointer;">
                  {{ scope.row.orderNum }}
                </div>
              </template>
            </el-table-column>
            <el-table-column prop="orderSts" width="90" align="center" label="工单状态">
              <template slot-scope="scope">
                <el-tag v-if="scope.row.orderSts == 1" size="small" type="success">运行中</el-tag>
                <el-tag v-if="scope.row.orderSts == 2" size="small" type="info">结束</el-tag>
                <el-tag v-if="scope.row.orderSts == 3" size="small" type="error">废弃</el-tag>
              </template>
            </el-table-column>
            <el-table-column prop="orderName" align="center" label="工单名称" width="200"></el-table-column>

            <!-- <el-table-column align="center" label="修改前" width="120">

              <template slot-scope="scope">
                <el-popover placement="right" width="300" trigger="click">
                  <el-descriptions title="修改前" :column="1">
                    <el-descriptions-item label="地市">{{ scope.row.beforeJson.cityName }}</el-descriptions-item>
                    <el-descriptions-item label="区县">{{ scope.row.beforeJson.areaName }}</el-descriptions-item>
                    <el-descriptions-item label="局站">{{ scope.row.beforeJson.siteName }}</el-descriptions-item>
                    <el-descriptions-item label="机房">{{ scope.row.beforeJson.roomName }}</el-descriptions-item>
                    <el-descriptions-item label="设备类型">{{ scope.row.beforeJson.devName }}</el-descriptions-item>
                    <el-descriptions-item label="设备名称">{{ scope.row.beforeJson.devName }}</el-descriptions-item>
                    <el-descriptions-item label="测点名称">{{ scope.row.beforeJson.signalName }}</el-descriptions-item>
                    <el-descriptions-item label="上限">{{ scope.row.beforeJson.HLimit }}</el-descriptions-item>
                    <el-descriptions-item label="下限">{{ scope.row.beforeJson.LLimit }}</el-descriptions-item>
                    <el-descriptions-item label="过高上限">{{ scope.row.beforeJson.SHLimit }}</el-descriptions-item>
                    <el-descriptions-item label="过低下限">{{ scope.row.beforeJson.SLLimit }}</el-descriptions-item>
                    <el-descriptions-item label="设置值">{{ scope.row.beforeJson.SetValue }}</el-descriptions-item>
                  </el-descriptions>
                  <el-button type="text" slot="reference">详情</el-button>
                </el-popover>
              </template>
            </el-table-column> -->
            <el-table-column align="center" label="审批流程详情">
              <template slot-scope="scope">
                <el-steps :align-center="true" finish-status="success">
                  <template v-if="scope.row.orderSts == 3">
                    <el-step v-for="(item, index) in scope.row.transferInfos" :status="'wait'" :key="index"
                      :title="item.handlerUser || '-'">
                      <template slot="description">
                        <div> {{ item.handleStatus == 1 ? '' : item.handleTime }}</div>
                        <div v-if="item.handleStatus == 1">待审批</div>
                        <div> {{ item.transferNodeName || '-' }}</div>
                      </template>
                    </el-step>
                  </template>
                  <template v-else>
                    <el-step v-for="(item, index) in scope.row.transferInfos"
                      :status="[0, 2].includes(item.handleStatus) ? 'success' : item.handleStatus == 3 ? 'finish' : 'wait'"
                      :key="index" :title="item.handlerUser || '-'">
                      <template slot="description">
                        <div> {{ item.handleStatus == 1 ? '' : item.handleTime }}</div>
                        <div v-if="item.handleStatus == 1">待审批</div>
                        <div> {{ item.transferNodeName || '-' }}</div>
                      </template>
                    </el-step>
                  </template>
                </el-steps>
              </template>
            </el-table-column>
            <el-table-column align="center" label="修改后" width="120">
              <template slot-scope="scope">
                <el-popover placement="right" :width="scope.row.beforeJson ? 540 : 240" trigger="click">
                  <div style="display: flex;">
                    <el-descriptions v-if="scope.row.beforeJson" title="修改前" :column="1">
                      <el-descriptions-item label="地市">{{ scope.row.beforeJson.cityName }}</el-descriptions-item>
                      <el-descriptions-item label="区县">{{ scope.row.beforeJson.areaName }}</el-descriptions-item>
                      <el-descriptions-item label="局站">{{ scope.row.beforeJson.siteName }}</el-descriptions-item>
                      <el-descriptions-item label="机房">{{ scope.row.beforeJson.roomName }}</el-descriptions-item>
                      <el-descriptions-item label="设备类型">{{ scope.row.beforeJson.deviceTypeName ?
                        scope.row.beforeJson.deviceTypeName : scope.row.beforeJson.devName
                        }}</el-descriptions-item>
                      <el-descriptions-item label="设备名称">{{ scope.row.beforeJson.devName }}</el-descriptions-item>
                      <el-descriptions-item label="测点名称">{{ scope.row.beforeJson.signalName }}</el-descriptions-item>
                      <el-descriptions-item label="上限">{{ scope.row.beforeJson.HLimit }}</el-descriptions-item>
                      <el-descriptions-item label="下限">{{ scope.row.beforeJson.LLimit }}</el-descriptions-item>
                      <el-descriptions-item label="过高上限">{{ scope.row.beforeJson.SHLimit }}</el-descriptions-item>
                      <el-descriptions-item label="过低下限">{{ scope.row.beforeJson.SLLimit }}</el-descriptions-item>
                      <!-- <el-descriptions-item label="设置值">{{ scope.row.beforeJson.SetValue }}</el-descriptions-item> -->
                    </el-descriptions>
                    <img :src="arrow" v-if="scope.row.beforeJson" style="opacity: 0.6;margin: 0 24px;" alt="">
                    <el-descriptions title="修改后" :column="1">
                      <el-descriptions-item label="地市">{{ scope.row.afterJson.cityName }}</el-descriptions-item>
                      <el-descriptions-item label="区县">{{ scope.row.afterJson.areaName }}</el-descriptions-item>
                      <el-descriptions-item label="局站">{{ scope.row.afterJson.siteName }}</el-descriptions-item>
                      <el-descriptions-item label="机房">{{ scope.row.afterJson.roomName }}</el-descriptions-item>
                      <el-descriptions-item label="设备类型">{{ scope.row.afterJson.deviceTypeName ?
                        scope.row.afterJson.deviceTypeName : scope.row.afterJson.devName }}</el-descriptions-item>
                      <el-descriptions-item label="设备名称">{{ scope.row.afterJson.devName }}</el-descriptions-item>
                      <el-descriptions-item label="测点名称">{{ scope.row.afterJson.signalName }}</el-descriptions-item>

                      <template v-if="scope.row.beforeJson">
                        <el-descriptions-item label="上限"
                          :contentClassName="scope.row.afterJson.HLimit !== scope.row.beforeJson.HLimit ? 'my-content' : ''">
                          {{ scope.row.afterJson.HLimit }}
                          <el-tag v-if="scope.row.afterJson.HLimit !== scope.row.beforeJson.HLimit" type="danger"
                            size="small">变动</el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="下限"
                          :contentClassName="scope.row.afterJson.LLimit !== scope.row.beforeJson.LLimit ? 'my-content' : ''">
                          {{ scope.row.afterJson.LLimit }}
                          <el-tag v-if="scope.row.afterJson.LLimit !== scope.row.beforeJson.LLimit" type="danger"
                            size="small">变动</el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="过高上限"
                          :contentClassName="scope.row.afterJson.SHLimit !== scope.row.beforeJson.SHLimit ? 'my-content' : ''">
                          {{ scope.row.afterJson.SHLimit }}
                          <el-tag v-if="scope.row.afterJson.SHLimit !== scope.row.beforeJson.SHLimit" type="danger"
                            size="small">变动</el-tag>
                        </el-descriptions-item>
                        <el-descriptions-item label="过低下限"
                          :contentClassName="scope.row.afterJson.SLLimit !== scope.row.beforeJson.SLLimit ? 'my-content' : ''">
                          {{ scope.row.afterJson.SLLimit }}
                          <el-tag v-if="scope.row.afterJson.SLLimit !== scope.row.beforeJson.SLLimit" type="danger"
                            size="small">变动</el-tag>
                        </el-descriptions-item>
                        <!-- <el-descriptions-item label="设置值"
                          :contentClassName="scope.row.afterJson.SetValue !== scope.row.beforeJson.SetValue ? 'my-content' : ''">
                          {{ scope.row.afterJson.SetValue }}
                          <el-tag v-if="scope.row.afterJson.SetValue !== scope.row.beforeJson.SetValue" type="danger"
                            size="small">变动</el-tag>
                        </el-descriptions-item> -->
                      </template>

                      <template v-else>
                        <el-descriptions-item label="上限">{{ scope.row.afterJson.HLimit }}</el-descriptions-item>
                        <el-descriptions-item label="下限">{{ scope.row.afterJson.LLimit }}</el-descriptions-item>
                        <el-descriptions-item label="过高上限">{{ scope.row.afterJson.SHLimit }}</el-descriptions-item>
                        <el-descriptions-item label="过低下限">{{ scope.row.afterJson.SLLimit }}</el-descriptions-item>
                        <!-- <el-descriptions-item label="设置值">{{ scope.row.afterJson.SetValue }}</el-descriptions-item> -->
                      </template>

                    </el-descriptions>
                  </div>

                  <el-button type="text" slot="reference">详情</el-button>
                </el-popover>
              </template>
            </el-table-column>
          </el-table>
          <div class="pag">
            <el-pagination @size-change="deviceThresholdHandleSizeChange" background
              @current-change="deviceThresholdHandleCurrentChange" :current-page="deviceThresholdCurrentPage"
              :page-sizes="[10, 20, 50]" :page-size="deviceThresholdPageSize"
              layout="total, sizes, prev, pager, next, jumper" :total="deviceThresholdTotal">
            </el-pagination>
          </div>
        </div>

      </div>
    </div>
    <!-- 设备远程遥控 -->
    <div v-if="tabActiveName == 'first6'">
      <!-- 一级列表 -->
      <el-form :inline="true" :model="queryForm" size="small" class="demo-form-inline">

        <el-form-item label="测点名称">
          <el-input v-model="queryForm.signalName" clearable></el-input>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="queryData(1, 100, ['5'])">查询</el-button>
          <el-button @click="queryFormReset">重置</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="deviceTableData5" header-row-class-name="myHeaderClass" size="small" v-loading="tableLoading">
        <el-table-column prop="signalName" align="center" label="测点名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="monitorId" align="center" label="监控ID" show-overflow-tooltip></el-table-column>
        <el-table-column prop="updateTime" align="center" label="更新时间" show-overflow-tooltip></el-table-column>
        <el-table-column prop="" label="操作" width="200">
          <template slot-scope="scope">
            <div class="ad">
              <!-- 开关控制 关闭 5-->
              <el-tooltip effect="dark" content="关闭设备"
                v-if="btnopenDevice && ['5'].includes(scope.row.type) && scope.row.statusCode == '00' && scope.row.monitorId.includes(4195010)"
                placement="top">
                <i @click="opencontroldevice(scope.row, '请谨慎操作设备，是否远程【关闭】此设备？')" class="el-icon-switch-button"
                  style="font-size: 18px;color: #f56c6c;"></i>
              </el-tooltip>

              <!-- 开关控制 开启 5-->
              <el-tooltip effect="dark" content="开启设备"
                v-if="btnopenDevice && ['5'].includes(scope.row.type) && scope.row.statusCode == '01' && scope.row.monitorId.includes(4195010)"
                placement="top">
                <i @click="opencontroldevice(scope.row, '请谨慎操作设备，是否远程【开启】此设备？')" class="el-icon-switch-button"
                  style="font-size: 18px;"></i>
              </el-tooltip>

              <!-- 设备远程遥控  5-->
              <el-tooltip effect="dark" :content="scope.row.signalName"
                v-if="btnopenDevice && ['5'].includes(scope.row.type) && !scope.row.monitorId.includes(4195010)"
                placement="top">
                <i @click="opencontroldevice(scope.row, `请谨慎操作设备，是否${scope.row.signalName}此设备？`)"
                  class="el-icon-s-tools" style="font-size: 18px;"></i>
              </el-tooltip>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pag">
        <el-pagination @size-change="handleSizeChange" background @current-change="handleCurrentChange"
          :current-page="currentPage" :page-sizes="[10, 20, 50, 100]" :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="total">
        </el-pagination>
      </div>
    </div>

    <!-- 历史告警 -->
    <div v-if="tabActiveName == 'second'">
      <el-form :inline="true" :model="warningQuery" size="small" class="demo-form-inline">

        <el-form-item label="告警标题">
          <el-input v-model="warningQuery.alarmDesc" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="时间">
          <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="warningQuery.date" type="datetimerange"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
          <el-form-item>
          </el-form-item>

          <el-button @click="actreset">重置</el-button>
          <el-button type="primary" @click="queryWarningData">查询</el-button>
          <el-button type="primary" :loading="waringexportloading" @click="warningExport">导出</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="warningTableData" class="myWarnTable" :cell-class-name="warnLevelClassFn" size="small"
        v-loading="warningLoading">

        <el-table-column prop="cityName" align="center" label="地市"></el-table-column>
        <el-table-column prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
        <el-table-column prop="stationName" align="center" label="局站" show-overflow-tooltip></el-table-column>
        <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
        <el-table-column prop="deviceName" align="center" label="设备类型"></el-table-column>
        <el-table-column prop="deviceName" align="center" label="设备名称"></el-table-column>
        <el-table-column prop="signalName" align="center" label="告警标题" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmDesc" align="center" label="告警描述" show-overflow-tooltip></el-table-column>
        <el-table-column prop="triggerVal" align="center" label="告警触发值"></el-table-column>
        <el-table-column prop="alarmLevel" align="center" label="告警级别">
          <template slot-scope="scope">
            {{ warnLevelOptions.find(v => v.level == scope.row.alarmLevel)?.name || '六级告警' }}
          </template>
        </el-table-column>
        <el-table-column prop="alarmTime" align="center" label="告警发生时间" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmClearTime" align="center" label="告警清除时间" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmDuration" align="center" label="告警历时（分）" show-overflow-tooltip></el-table-column>
      </el-table>
      <div class="pag">
        <el-pagination @size-change="warninghandleSizeChange" background @current-change="warninghandleCurrentChange"
          :current-page="warningcurrentPage" :page-sizes="[10, 20, 50]" :page-size="warningpageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="warningtotal">
        </el-pagination>
      </div>
    </div>

    <!-- 活动告警 -->
    <div v-if="tabActiveName == 'third'">
      <el-form :inline="true" :model="actwarningQuery" size="small" class="demo-form-inline">

        <el-form-item label="告警标题">
          <el-input v-model="actwarningQuery.alarmDesc" placeholder="请输入" clearable></el-input>
        </el-form-item>
        <el-form-item label="">

          <el-button @click="reset('warningQuery')">重置</el-button>
          <el-button type="primary" @click="actqueryWarningData">查询</el-button>
        </el-form-item>
      </el-form>
      <el-table :data="actwarningTableData" class="myWarnTable" :cell-class-name="warnLevelClassFn" size="small"
        v-loading="actwarningLoading">

        <el-table-column prop="cityName" align="center" label="地市"></el-table-column>
        <el-table-column prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
        <el-table-column prop="stationName" align="center" label="局站" show-overflow-tooltip></el-table-column>
        <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
        <el-table-column prop="deviceName" align="center" label="设备类型"></el-table-column>
        <el-table-column prop="deviceName" align="center" label="设备名称"></el-table-column>
        <el-table-column prop="signalName" align="center" label="告警标题" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmDesc" align="center" label="告警描述" show-overflow-tooltip></el-table-column>
        <el-table-column prop="triggerVal" align="center" label="告警触发值"></el-table-column>
        <el-table-column prop="alarmLevel" align="center" label="告警级别">
          <template slot-scope="scope">
            {{ warnLevelOptions.find(v => v.level == scope.row.alarmLevel)?.name || "六级告警" }}
          </template>
        </el-table-column>
        <el-table-column prop="alarmTime" align="center" label="告警发生时间" show-overflow-tooltip></el-table-column>
        <!-- <el-table-column prop="clearTime" align="center" label="告警清除时间" show-overflow-tooltip></el-table-column> -->
        <!-- <el-table-column prop="alarmDuration" align="center" label="告警历时（分）" show-overflow-tooltip></el-table-column> -->
      </el-table>
      <div class="pag">
        <el-pagination @size-change="actwarninghandleSizeChange" background
          @current-change="actwarninghandleCurrentChange" :current-page="actwarningcurrentPage"
          :page-sizes="[10, 20, 50]" :page-size="actwarningpageSize" layout="total, sizes, prev, pager, next, jumper"
          :total="actwarningtotal">
        </el-pagination>
      </div>
    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import { getSysUserId } from '@/utils/auth';
import dayjs from 'dayjs'
import { mapGetters } from 'vuex';
import { Message } from 'element-ui'
import exportTableToExcel from './components/exportExcel.js'
export default {
  props: {
    suId: String,
    devId: String
  },
  directives: {
    copy: {
      bind(el, { value }) {
        el.handler = () => {
          if (value) {
            navigator.clipboard.writeText(value).then(
              () => {
                Message({
                  message: '复制成功',
                  type: 'success',
                  offset: 60,
                  duration: 1000
                })
              },
              () => { /* 失败 */ }
            );
          } else {
            navigator.clipboard.writeText(el.textContent).then(
              () => {
                Message({
                  message: '复制成功',
                  type: 'success',
                  offset: 60,
                  duration: 1000
                })
              },
              () => { /* 失败 */ }
            );
          }
        };
        el.addEventListener('click', el.handler);
      },
      unbind(el) {
        el.removeEventListener('click', el.handler);
      },
    },
  },
  data() {
    return {
      warnLevelOptions: [
        { level: 1, name: '一级告警' },
        { level: 2, name: '二级告警' },
        { level: 3, name: '三级告警' },
        { level: 4, name: '四级告警' },
        { level: 5, name: '五级告警' },
        { level: 6, name: '六级告警' },
      ],
      submitThresholdLoading: false,
      submitWarnLevelLoading: false,

      tableLoading: false,
      warningLoading: false,
      hyDataLoading: false,
      arrow: require('./img/arrow.svg'),
      opricon: require('./../views/img/齿轮组.svg'),
      tabActiveName: 'first1',
      queryForm: {
        signalName: ''
      },
      deviceTableData1: [],
      deviceTableData2: [],
      deviceTableData3: [],
      deviceTableData4: [],
      deviceTableData5: [],
      deviceTableData6: [],
      pageSize: 100,
      currentPage: 1,
      total: 0,
      content: '',
      currentRow: null,

      // 历史告警相关
      waringexportloading: false,
      warningQuery: {
        alarmDesc: '',
        date: [dayjs().subtract('30', 'day').format("YYYY-MM-DD HH:mm:ss"), dayjs().format("YYYY-MM-DD HH:mm:ss")]
      },
      warningTableData: [],
      warningpageSize: 10,
      warningcurrentPage: 1,
      warningtotal: 0,

      // 活动告警相关
      actwaringexportloading: false,
      actwarningLoading: false,
      actwarningQuery: {
        alarmDesc: '',
      },
      actwarningTableData: [],
      actwarningpageSize: 10,
      actwarningcurrentPage: 1,
      actwarningtotal: 0,

      // 历史数据相关
      historyDataFlag: false,
      exportloading: false,
      historyTableData: [],
      htpageSize: 10,
      htcurrentPage: 1,
      httotal: 0,
      htquery: {
        date: [dayjs().subtract('7', 'day').format("YYYY-MM-DD HH:mm:ss"), dayjs().format("YYYY-MM-DD HH:mm:ss")]
      },
      trendChartFlag: false,
      openFlag: false,
      closeFlag: false,

      // 操作日志
      dialogTitle: '',
      oprTableData: [],
      oprpageSize: 10,
      oprcurrentPage: 1,
      oprtotal: 0,
      dialogTableVisible: false,

      // 设备阈值配置
      deviceThresholdFlag: false,
      deviceThresholdActive: '当前配置',
      deviceThresholdData: {},
      olddeviceThresholdData: {},
      deviceThresholdHistoryData: [],
      deviceThresholdPageSize: 10,
      deviceThresholdCurrentPage: 1,
      deviceThresholdTotal: 0,

      // 告警级别设置
      warnLevelSetFlag: false,
      warnLevelSetActive: '当前配置',
      warnLevelSetData: {},
      oldwarnLevelSetData: {},
      warnLevelSetHistoryData: [],
      warnLevelPageSize: 10,
      warnLevelCurrentPage: 1,
      warnLevelTotal: 0,

      // 权限按钮
      btnshowHistory: false,
      btnoperateHistory: false,
      btndeviceThreshold: false,
      btnwarnLevelSet: false,
      btnopenDevice: false,
      btncloseDevice: false,
    }
  },
  watch: {
    devId() {
      this.currentPage = 1
      this.queryForm.signalName = ''
      this.goback(1)
      this.tableLoading = true

      // this.queryData()
      this.$api.deviceInfo({ suId: this.suId, devId: this.devId, current: 1, size: 100 }).then(res => {
        if (res.code == 200) {
          this.queryData()
        } else {
          this.deviceTableData1 = []
          this.deviceTableData2 = []
          this.deviceTableData3 = []
          this.deviceTableData4 = []
          this.deviceTableData5 = []
          this.deviceTableData6 = []
          this.total = 0
          this.$message.error(res.msg)
        }
      })
    },
    suId() {
      this.currentPage = 1
      this.queryForm.signalName = ''
      this.goback(1)
      this.tableLoading = true

      // this.queryData()
      this.$api.deviceInfo({ suId: this.suId, devId: this.devId, current: 1, size: 100 }).then(res => {
        if (res.code == 200) {
          this.queryData()
        } else {
          this.deviceTableData1 = []
          this.deviceTableData2 = []
          this.deviceTableData3 = []
          this.deviceTableData4 = []
          this.deviceTableData5 = []
          this.deviceTableData6 = []
          this.total = 0
          this.$message.error(res.msg)
        }
      })
    }
  },
  computed: {
    ...mapGetters([
      'elements'
    ]),
    alarmLevelText() {
      return (level) => this.warnLevelOptions[level] || '未知';
    },

  },
  created() {

    this.btnshowHistory = this.elements['btnshowHistory']
    this.btnoperateHistory = this.elements['btnoperateHistory']
    this.btndeviceThreshold = this.elements['btndeviceThreshold']
    this.btnwarnLevelSet = this.elements['btnwarnLevelSet']
    this.btnopenDevice = this.elements['btnopenDevice']
    this.btncloseDevice = this.elements['btncloseDevice']

    // console.log(this.suId, this.devId)
    this.tableLoading = true

    //  this.queryData()

    this.$api.deviceInfo({ suId: this.suId, devId: this.devId, current: 1, size: 100 }).then(res => {
      if (res.code == 200) {
        this.queryData(1, 100, ['1'])
      }
    })
  },
  methods: {
    warnLevelClassFn({ row, column }) {
      // console.log(row,column)
      if (column.property == 'alarmLevel') {
        if (row.alarmLevel == "1") {
          return 'warn1'
        }
        if (row.alarmLevel == "2") {
          return 'warn2'
        }
        if (row.alarmLevel == "3") {
          return 'warn3'
        }
      }
    },
    opencontroldevice(row, title) {
      this.$confirm(title, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.controldevice(row)
      }).catch(() => {

      });

    },
    handleClick(val) {
      //console.log(val.name)
      if (val.name == 'first1') {
        this.queryData(1, 100, ['1'])
      }

      if (val.name == 'first2') {
        this.queryData(1, 100, ['2', '4'])
      }

      if (val.name == 'first5') {
        this.queryData(1, 100, ['6'])
      }

      if (val.name == 'first6') {
        this.queryData(1, 100, ['5'])
      }

      if (val.name == 'second') {
        this.queryWarningData()
      }
      if (val.name == 'third') {
        this.actqueryWarningData()
      }
    },
    queryFormReset() {
      this.queryForm = {
        signalName: ''
      }
      this.queryData()
    },

    // 监控量
    queryData(pageNum, pageSize, queryType) {
      this.tableLoading = true
      if (pageNum) this.currentPage = pageNum
      if (pageSize) this.pageSize = pageSize
      if (queryType) this.queryType = queryType
      let params = {
        suId: this.suId,
        devId: this.devId,
        signalName: this.queryForm.signalName,
        current: pageNum || this.currentPage,
        size: pageSize || this.pageSize,
        type: this.queryType || 1
      }
      this.$api.deviceInfoList(params).then(res => {
        const { code, msg, data } = res
        if (code == 200) {
          try {
            if (data.total != 0) {
              switch (data.records[0].type) {
                case '1':
                  this.deviceTableData1 = data.records
                  this.tableLoading = false
                  this.total = res.data.total
                  break;
                case '2':
                  this.deviceTableData2 = data.records
                  this.tableLoading = false
                  this.total = res.data.total
                  break;
                case '3':
                  this.deviceTableData3 = data.records
                  this.tableLoading = false
                  this.total = res.data.total
                  break;
                case '4':
                  this.deviceTableData2 = data.records
                  this.tableLoading = false
                  this.total = res.data.total
                  break;
                case '5':
                  this.deviceTableData5 = data.records
                  this.tableLoading = false
                  this.total = res.data.total
                  break;
                case '6':
                  this.deviceTableData6 = data.records
                  this.tableLoading = false
                  this.total = res.data.total
                  break;
                default:
                  break;
              }
            } else {
              this.total = 0;
              this.tableLoading = false;
            }
          } catch (error) {
            console.log(error);

          }
        } else {
          this.tableLoading = false
          this.$message.error(res.msg)
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.queryData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.queryData()
    },

    // 活动告警
    actreset() {
      this.actwarningQuery = {
        alarmDesc: '',
      }
      this.actwarningcurrentPage = 1
      this.actwarningpageSize = 10
      this.actqueryWarningData()
    },
    async actqueryWarningData() {
      this.actwarningLoading = true
      let params = {
        suId: this.suId,
        devId: this.devId,
        signalId: '',
        alarmDesc: this.actwarningQuery.alarmDesc,
        // createTimeStart: this.actwarningQuery.date[0] || '',
        // createTimeEnd: this.actwarningQuery.date[1] || '',
        current: this.actwarningcurrentPage,
        size: this.actwarningpageSize
      }
      let res = await this.$api.activeAlarm(params)
      if (res.code == 200) {

        this.actwarningTableData = res.data.records
        this.actwarningtotal = res.data.total
      } else {
        this.$message.error(res.msg)
      }
      this.actwarningLoading = false

    },
    actwarninghandleSizeChange(val) {
      this.actwarningpageSize = val
      this.actqueryWarningData()
    },
    actwarninghandleCurrentChange(val) {
      this.actwarningcurrentPage = val
      this.actqueryWarningData()
    },

    // 历史告警
    reset() {
      this.warningQuery = {
        alarmDesc: '',
        date: [dayjs().subtract('7', 'day').format("YYYY-MM-DD HH:mm:ss"), dayjs().format("YYYY-MM-DD HH:mm:ss")]
      }
      this.warningcurrentPage = 1
      this.warningpageSize = 10
      this.queryWarningData()
    },
    async queryWarningData() {
      this.warningLoading = true
      let params = {
        suId: this.suId,
        devId: this.devId,
        signalId: '',
        alarmDesc: this.warningQuery.alarmDesc,
        createTimeStart: this.warningQuery.date[0] || '',
        createTimeEnd: this.warningQuery.date[1] || '',
        current: this.warningcurrentPage,
        size: this.warningpageSize
      }
      let res = await this.$api.historyAlarm(params)
      if (res.code == 200) {

        this.warningTableData = res.data.records
        this.warningtotal = res.data.total
      } else {
        this.$message.error(res.msg)
      }
      this.warningLoading = false

    },
    warninghandleSizeChange(val) {
      this.warningpageSize = val
      this.queryWarningData()
    },
    warninghandleCurrentChange(val) {
      this.warningcurrentPage = val
      this.queryWarningData()
    },
    async warningExport() {
      this.waringexportloading = true
      let data = [
        ['地市', '区域', '局站', '机房', '设备类型', '设备名称', '告警标题', '告警描述', '告警触发值', '告警级别', '告警发生时间', '告警清除时间', '告警历时（分）']
      ]
      let params = {
        suId: this.suId,
        devId: this.devId,
        signalId: '',
        alarmDesc: '',
        createTimeStart: '',
        createTimeEnd: '',
        current: 1,
        size: this.warningtotal
      }
      try {
        let res = await this.$api.historyAlarm(params)
        if (res.code == 200) {
          res.data.records.forEach(v => {
            let item = [
              v.cityName,
              v.areaName,
              v.stationName,
              v.roomName,
              v.deviceName,
              v.deviceName,
              v.signalName,
              v.alarmDesc,
              v.triggerVal,
              v.alarmLevel,
              v.alarmTime,
              v.alarmClearTime,
              v.alarmDuration,
            ]
            data.push(item)
          })
          exportTableToExcel(data, '告警数据')
          this.waringexportloading = false
        } else {
          this.$message.error(res.msg)
          this.waringexportloading = false
        }
      } catch (error) {
        this.$message.error('导出异常')
        this.waringexportloading = false
      }

    },
    // 操作日志
    async operateHistory(row) {
      this.currentRow = row
      let params = {
        suId: this.currentRow.suId,
        devId: this.currentRow.devId,
        signalId: this.currentRow.monitorId,
        current: this.oprcurrentPage,
        size: this.oprpageSize
      }
      let res = await this.$api.operateHistory(params)
      if (res.code == 200) {

        let typedict = [
          { name: '遥测', type: '1' },
          { name: '遥测-告警', type: '2' },
          { name: '遥信-开关', type: '3' },
          { name: '遥信-告警', type: '4' },
          { name: '遥控-动作', type: '5' },
          { name: '遥控-参数', type: '6' },
        ]
        this.dialogTitle = `${row.signalName}-操作日志`
        this.oprTableData = res.data.records.map(v => {
          v.startTime = v.startTime.replace('T', ' ')
          return v
        })
        this.dialogTableVisible = true
        this.oprtotal = res.data.total
      } else {
        this.$message.error(res.msg)
      }
    },
    oprhandleSizeChange(val) {
      this.oprpageSize = val
      this.operateHistory(this.currentRow)
    },
    oprhandleCurrentChange(val) {
      this.oprcurrentPage = val
      this.operateHistory(this.currentRow)
    },
    // 返回
    goback(item) {
      this.tabActiveName = `first${item}`
      this.currentRow = null
      this.historyDataFlag = false
      this.deviceThresholdFlag = false
      this.warnLevelSetFlag = false
      this.warningQuery = {
        alarmDesc: '',
        date: [dayjs().subtract('30', 'day').format("YYYY-MM-DD HH:mm:ss"), dayjs().format("YYYY-MM-DD HH:mm:ss")]
      }
      this.htquery = {
        date: [dayjs().subtract('7', 'day').format("YYYY-MM-DD HH:mm:ss"), dayjs().format("YYYY-MM-DD HH:mm:ss")]
      }
    },
    // 展示历史数据相关
    showHistory(row) {
      this.historyDataFlag = true
      this.trendChartFlag = false
      this.currentRow = row
      this.currentContent = '性能历史数据'
      this.queryHtData()
    },
    // 查询历史数据
    async queryHtData() {
      this.hyDataLoading = true
      let params = {
        suId: this.suId,
        devId: this.devId,
        signalId: this.currentRow.monitorId,
        isPage: 'true',
        createTimeStart: this.htquery.date[0] || '',
        createTimeEnd: this.htquery.date[1] || '',
        current: this.htcurrentPage,
        size: this.htpageSize
      }
      let res = await this.$api.devicePmHistory(params)
      if (res.code == 200) {
        this.historyTableData = res.data.records.map(v => {
          v.report_time = dayjs(v.report_time).format("YYYY-MM-DD HH:mm:ss")
          return v
        })
        this.httotal = res.data.total
      } else {
        this.$message.error(res.msg)
      }
      this.hyDataLoading = false

    },
    hthandleSizeChange(val) {
      this.htpageSize = val
      this.queryHtData()
    },
    hthandleCurrentChange(val) {
      this.htcurrentPage = val
      this.queryHtData()
    },
    // 导出
    async exportHistoryExcel(fileName) {
      // const data = [
      //   ["姓名", "年龄", "职业"],
      //   ["Alice", 28, "前端开发"],
      //   ["Bob", 22, "后端开发"]
      // ];
      this.exportloading = true
      let data = [
        ['地市', '区域', '局站', '机房', '设备类型', '设备名称', '测点类型', '测点ID', '测点名称', '测点值', '上报时间']
      ]
      let params = {
        suId: this.suId,
        devId: this.devId,
        signalId: this.currentRow.monitorId,
        isPage: 'false',
        createTimeStart: '',
        createTimeEnd: '',
        current: 1,
        size: this.total
      }
      let res = await this.$api.devicePmHistory(params)
      if (res.code == 200) {
        res.data.forEach(v => {
          let item = [
            v.city_name,
            v.area_name,
            v.site_name,
            v.room_name,
            v.device_name,
            v.device_name,
            v.remark,
            v.signal_id,
            v.signal_name,
            v.define_value,
            dayjs(v.report_time).format("YYYY-MM-DD HH:mm:ss")
          ]
          data.push(item)
        })
        exportTableToExcel(data, fileName)
        this.exportloading = false
      } else {
        this.$message.error(res.msg)
        this.exportloading = false
      }
    },
    // 趋势图
    async showTrend() {
      let xData = []
      let Data = []
      let params = {
        suId: this.suId,
        devId: this.devId,
        isPage: 'false',
        signalId: this.currentRow.monitorId,
        createTimeStart: this.htquery.date[0] || '',
        createTimeEnd: this.htquery.date[1] || '',
        current: 1,
        size: 99999
      }
      let res = await this.$api.devicePmHistory(params)
      if (res.code == 200) {
        res.data.forEach(v => {

          xData.unshift(dayjs(v.report_time).format("YYYY-MM-DD HH:mm:ss"))
          Data.unshift(v.define_value)
        })
        this.trendChartFlag = true
      } else {
        this.$message.error(res.msg)
      }
      this.$nextTick().then(() => {
        // 初始化echarts实例
        const myChart = echarts.init(document.getElementById('trend'));
        // 图表配置项
        const options = {
          title: {
            text: this.currentRow.signalName,

            left: "center",
            textStyle: {
              color: "#7b93a7",
              fontSize: 16,
              align: "center",
            },
          },
          tooltip: {
            show: true,
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xData,
            axisLabel: {
              width: 50
            }
          },
          dataZoom: [
            {
              type: 'inside',
              start: 90,
              end: 100
            },
            {
              start: 90,
              end: 100
            }
          ],
          yAxis: {
            type: 'value',

          },
          series: [
            {
              type: 'line',
              data: Data
            },
          ],
        };
        // 使用配置项显示图表
        myChart.setOption(options);
      })
    },
    async controldevice(row) {

      let params = {
        areaId: row.areaId || "",
        cityId: row.cityId || "",
        orderType: 1,
        flowType: 0, // 0 普通 1 核心
        applyUser: "test-user",
        businessInfoReq: {
          beforeJson: JSON.stringify(row),
          afterJson: JSON.stringify(row),
          signalName: row.signalName,
          signalId: row.monitorId,
          suId: row.suId,
          devId: row.devId
        }
      }
      let res = await this.$api.orderCreate(params)
      if (res.code == 200) {
        this.$message.success('保存成功')
        this.queryData(1, 100)

      } else {
        this.$message.error(res.msg)
      }

      // let params = {
      //   id: row.id,
      //   suId: row.suId,
      //   devId: row.devId,
      //   signalName: row.signalName,
      //   signalCode: row.signalCode,
      //   signalId:row.monitorId
      // }
      // let res = await this.$api.handelDeviceSwitch(params)
      // if (res.code == 200) {
      //   this.$message.success('操作成功')
      //   this.queryData(1, 10)
      // } else {
      //   this.$message.error(res.msg)
      // }
    },
    async openDevice(row) {
      let params = {
        id: row.id,
        signalCode: row.signalCode,
      }
      let res = await this.$api.handelDeviceSwitch(params)
      if (res.code == 200) {
        this.$message.success('操作成功')
        this.queryData(1, 100)
      } else {
        this.$message.error(res.msg)
      }
    },
    async closeDevice(row) {
      let params = {
        id: row.id,
        signalCode: row.signalCode,
      }
      let res = await this.$api.handelDeviceSwitch(params)
      if (res.code == 200) {
        this.$message.success('操作成功')
        this.queryData(1, 100)
      } else {
        this.$message.error(res.msg)
      }
    },

    // 设备阈值配置
    deviceThreshold(row) {
      this.deviceThresholdPageSize = 10
      this.deviceThresholdCurrentPage = 1
      this.currentRow = JSON.parse(JSON.stringify(row))
      this.deviceThresholdData = JSON.parse(JSON.stringify(row))
      // console.log(this.deviceThresholdData)
      this.olddeviceThresholdData = JSON.parse(JSON.stringify(row))
      this.deviceThresholdFlag = true
      this.deviceThresholdActive = '当前配置'
      this.deviceContent = '设备阈值配置'
    },
    deviceThresholdtTabChange(val) {
      if (val == '配置日志') {
        this.queryDeviceConfigHistory()
      }
    },
    async sdeviceThresholdConfig() {
      if (
        this.deviceThresholdData.HLimit == this.olddeviceThresholdData.HLimit &&
        this.deviceThresholdData.LLimit == this.olddeviceThresholdData.LLimit &&
        this.deviceThresholdData.SHLimit == this.olddeviceThresholdData.SHLimit &&
        this.deviceThresholdData.SLLimit == this.olddeviceThresholdData.SLLimit &&
        this.deviceThresholdData.SetValue == this.olddeviceThresholdData.SetValue
      ) {
        return this.$message.error('请变更参数后再保存！')
      }
      let changeParams = []
      if (this.deviceThresholdData.HLimit !== this.olddeviceThresholdData.HLimit) changeParams.push('上限')
      if (this.deviceThresholdData.LLimit !== this.olddeviceThresholdData.LLimit) changeParams.push('下限')
      if (this.deviceThresholdData.SHLimit !== this.olddeviceThresholdData.SHLimit) changeParams.push('过高上限')
      if (this.deviceThresholdData.SLLimit !== this.olddeviceThresholdData.SLLimit) changeParams.push('过低下限')
      // if (this.deviceThresholdData.SetValue !== this.olddeviceThresholdData.SetValue) changeParams.push('设置值')
      let title = `请确认是否修改【${changeParams.join('、')}】参数，确认后将发起审批流程`
      this.$confirm(title, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        hideIcon: true
      }).then(async () => {
        this.submitThresholdLoading = true

        let params = {
          areaId: this.deviceThresholdData.areaId || "",
          cityId: this.deviceThresholdData.cityId || "",
          orderType: 1,
          flowType: 0, // 0 普通 1 核心
          applyUser: "test-user",
          businessInfoReq: {
            beforeJson: JSON.stringify(this.olddeviceThresholdData),
            afterJson: JSON.stringify(this.deviceThresholdData),
            signalName: this.deviceThresholdData.signalName,
            signalId: this.deviceThresholdData.monitorId,
            suId: this.deviceThresholdData.suId,
            devId: this.deviceThresholdData.devId
          }
        }
        try {
          let res = await this.$api.orderCreate(params)
          if (res.code == 200) {
            this.$message.success('保存成功')
          } else {
            this.$message.error(res.msg)
          }
          this.submitThresholdLoading = false
        } catch (error) {
          this.submitThresholdLoading = false
        }
      }).catch(() => {

      });


    },
    deviceThresholdHandleSizeChange(val) {
      this.deviceThresholdPageSize = val
      this.queryDeviceConfigHistory()
    },
    deviceThresholdHandleCurrentChange(val) {
      this.deviceThresholdCurrentPage = val
      this.queryDeviceConfigHistory()
    },

    // 告警级别设置
    warnLevelSet(row) {
      row.alarmLevel = row.alarmLevel && typeof row.alarmLevel == 'string' ? Number(row.alarmLevel) : ''
      this.warnLevelPageSize = 10
      this.warnLevelCurrentPage = 1
      this.currentRow = JSON.parse(JSON.stringify(row))
      this.warnLevelSetData = JSON.parse(JSON.stringify(row))
      this.oldwarnLevelSetData = JSON.parse(JSON.stringify(row))
      console.log(this.oldwarnLevelSetData)
      this.warnLevelSetFlag = true
      this.warnLevelSetActive = '当前配置'
      this.warnLevelContent = '告警级别设置'
    },
    warnLevelSetTabChange(val) {
      console.log(val)
      if (val == '配置日志') {
        this.queryWarnLevelConfigHistory()
      }
    },
    async saveWarningConfig() {
      if (this.warnLevelSetData.alarmLevel == this.oldwarnLevelSetData.alarmLevel) {
        return this.$message.error('请变更参数后再保存！')
      }
      let title = `请确认是否修改【告警级别】参数，确认后将发起审批流程`
      this.$confirm(title, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async () => {
        this.submitWarnLevelLoading = true
        let params = {
          areaId: this.oldwarnLevelSetData.areaId || "",
          cityId: this.oldwarnLevelSetData.cityId || "",
          orderType: 2,
          flowType: 0,
          businessInfoReq: {
            beforeJson: JSON.stringify(this.oldwarnLevelSetData),
            afterJson: JSON.stringify(this.warnLevelSetData),
            signalName: this.warnLevelSetData.signalName,
            signalId: this.warnLevelSetData.monitorId,
            suId: this.warnLevelSetData.suId,
            devId: this.warnLevelSetData.devId
          }
        }
        try {
          let res = await this.$api.orderCreate(params)
          if (res.code == 200) {
            this.$message.success('保存成功')
          } else {
            this.$message.error(res.msg)
          }
          this.submitWarnLevelLoading = false
        } catch (error) {
          this.submitWarnLevelLoading = false

        }
      }).catch(() => {

      });


    },
    warnLevelHandleSizeChange(val) {
      this.warnLevelPageSize = val
      this.queryWarnLevelConfigHistory()
    },
    warnLevelHandleCurrentChange(val) {
      this.warnLevelCurrentPage = val
      this.queryWarnLevelConfigHistory()
    },

    // 查询告警配置历史 
    async queryWarnLevelConfigHistory() {
      let params = {
        suId: this.currentRow.suId,
        devId: this.currentRow.devId,
        signalId: this.currentRow.monitorId,
        queryTrans: true,
        pageNum: this.warnLevelCurrentPage,
        pageSize: this.warnLevelPageSize
      }
      let res = await this.$api.configHistory(params)
      if (res.code == 200) {
        this.warnLevelSetHistoryData = res.data.records.map(v => {
          v.afterJson = JSON.parse(v.afterJson)
          v.beforeJson = JSON.parse(v.beforeJson)
          return v
        })
        this.warnLevelTotal = res.data.total


      } else {
        this.$message.error(res.msg)
      }
    },

    // 查询阈值配置历史 
    async queryDeviceConfigHistory() {
      let params = {
        suId: this.currentRow.suId,
        devId: this.currentRow.devId,
        signalId: this.currentRow.monitorId,
        queryTrans: true,
        pageNum: this.deviceThresholdCurrentPage,
        pageSize: this.deviceThresholdPageSize
      }
      let res = await this.$api.configHistory(params)
      if (res.code == 200) {
        console.log(res);

        this.deviceThresholdHistoryData = res.data.records.map(v => {
          v.afterJson = JSON.parse(v.afterJson)
          v.beforeJson = JSON.parse(v.beforeJson)
          return v
        })
        this.deviceThresholdTotal = res.data.total
      } else {
        this.$message.error(res.msg)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.ad {
  display: flex;
  gap: 6px;

  i {
    cursor: pointer;
  }
}

.pag {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 12px;
}
</style>