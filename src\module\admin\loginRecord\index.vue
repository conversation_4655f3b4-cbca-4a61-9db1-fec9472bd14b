<!-- 定制任务页面 -->
<template>
  <div class="app-container calendar-list-container disposefiles handFilter">
    <div class="filter-container">
      <div class="search-box">
        <el-form label-position="right" inline size="small" :model="formDatalist" label-width="80px" label-suffix=":"
          class="demo-form-inline">

          <el-form-item label="用户名" prop="QRZT_NAME">
            <el-input class="filter-item" placeholder="用户名" clearable=""
              v-model.trim="formDatalist.userName"></el-input>
          </el-form-item>
          <el-form-item label="时间">
            <el-date-picker v-model="formDatalist.DATE" type="daterange" value-format="yyyy-MM-dd"
              :picker-options="dateOptions" format="yyyy-MM-dd" range-separator="至" start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
          <el-form-item class="formbtn">
            <el-button type="primary" @click="submitForm('formDatalist')">查询</el-button>
            <el-button @click="resetForm('formDatalist')">重置</el-button>
            <!-- <el-button :loading="btnloading" type="primary" @click="handlEexport"> 导出</el-button> -->
          </el-form-item>
          
        </el-form>
      </div>
      <!-- 表格 -->
      <div class="listTables" v-loading="lodin">
        <el-table :data="tableData" :height="tableheight" style="width: 100%" :fit=true>
          <el-table-column v-for="(row, index) in columnDatas" :key="index" :prop="row.field" :label="row.title"
            :min-width="row.columnWidth || 120" show-overflow-tooltip>
          </el-table-column>
        </el-table>
        <div class="pag" style="margin-top: 15px;margin-bottom:0px; ">
          <el-pagination background @size-change="handleSizeChange" @current-change="handleCurrentChange"
            :page-sizes="[10, 15, 20, 50]" :current-page.sync="pageIndex" :page-size.sync="pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="totalCount">
          </el-pagination>
        </div>
      </div>
      <!-- 编辑dilog -->

    </div>
  </div>
</template>

<script>
import server from '@/api/http'
import qs from "qs";
function Format(date, fmt) {
  var o = {
    'M+': date.getMonth() + 1, // 月份
    'd+': date.getDate(), // 日
    'h+': date.getHours(), // 小时
    'm+': date.getMinutes(), // 分
    's+': date.getSeconds(), // 秒
    'q+': Math.floor((date.getMonth() + 3) / 3), // 季度
    'S': date.getMilliseconds() // 毫秒
  }
  if (/(y+)/.test(fmt)) {
    fmt = fmt.replace(RegExp.$1, (date.getFullYear() + '').substr(4 - RegExp.$1.length))
  }
  for (var k in o) {
    if (new RegExp('(' + k + ')').test(fmt)) {
      fmt = fmt.replace(RegExp.$1, (RegExp.$1.length === 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
    }
  }
  return fmt
}
export default {
  data() {
    return {
      formDatalist: {
        START_DATE: '',
        END_DATE: '',
        userName: '',
        DATE: [],
      },
      formDatalist1: {},
      pageIndex: 1,
      pageSize: 10,
      totalCount: 0,
      tablelist: {
        pageIndex: 1,
        pageSize: 10,
        totalCount: 0,
      },
      tableData: [],
      columnDatas: [
        //   {'field': 'city_name', title: '地市','columnWidth':'80'},
        //   {'field': 'region_name', title: '管理区域','columnWidth':'150'},
        { 'field': 'user_id', title: '用户名', 'columnWidth': '100' },
        { 'field': 'user_name', title: '昵称', 'columnWidth': '150' },
        { 'field': 'tel_number', title: '手机号', 'columnWidth': '130' },
        //   {'field': 'object_name', title: '邮箱','columnWidth':'150'},
        { 'field': 'login_time', title: '登录时间', 'columnWidth': '150' },
      ],
      btnloading: false,
      dateOptions: {
        disabledDate(time) {
          // return time >= Date.now()
          return time.getTime() > Date.now();  //如果没有后面的-8.64e6就是不可以选择今天的 
        }
      },
      tableheight: 390,
      lodin: false,
    }
  },
  components: {},
  created() {
    let bodyheight = document.documentElement.clientHeight;
    this.tableheight = parseInt(bodyheight) > 800 ? 565 : 390;
    this.tablelist.pageSize = parseInt(bodyheight) > 800 ? 15 : 10;
    this.pageSize = this.tablelist.pageSize;

  },
  mounted() {
    // this.queryDateInit()
    // this.queryData()
    this.submitForm();
  },
  computed: {

  },
  methods: {
    handlEexport() {//导出
      let that = this;
      that.btnloading = true;

      if (that.tableData.length == 0) {
        that.$message({
          showClose: true,
          message: "请查询后导出",
          type: "warning",
          duration: 1500
        });
        that.btnloading = false;
        return;
      }
      let params = {
        'EXPORT_TYPE': 'ALL',
        'menuName': that.$route.name,
        user_id: that.formDatalist1.userName,
        beginDate: that.formDatalist1.DATE.length ? that.formDatalist1.DATE[0] + ' 00:00:00' : '',
        endDate: that.formDatalist1.DATE.length ? that.formDatalist1.DATE[1] + ' 23:59:59' : '',
        'page': 1,
        'rows': 10000,
      };
      params = { ...params, ...that.formDatalist1 };
      //  console.log(params);
      //dynamic/topo/selSubnetList
      let qparms = qs.stringify(params);
      //  return;
      window.location.href = 'http://10.19.239.203:9777/dynamic/download/loginLog?' + qparms;
      let time = that.totalCount > 2000 ? 4000 : 2000;
      setTimeout(() => {
        that.btnloading = false;
      }, time);

    },
    // 查询默认最近一个月
    queryDateInit() {
      var end = new Date();
      var start = new Date();
      start.setTime(start.getTime() - 3600 * 1000 * 24 * 30);
      this.taskfrom.START_DATE = Format(start, 'yyyy-MM-dd');
      this.taskfrom.END_DATE = Format(end, 'yyyy-MM-dd');
      // this.taskfrom.DATE = [Format(start, 'yyyy-MM-dd'), Format(end, 'yyyy-MM-dd')]
    },
    // 分页pagesize
    handleSizeChange(val) {
      this.pageIndex = 1;
      this.pageSize = val;
      this.queryData()
    },
    // 分页pageindex
    handleCurrentChange(val) {
      this.pageIndex = val;
      this.queryData()
    },
    // 查询数据
    queryData() {
      let that = this;
      that.formDatalist1 = that.formDatalist;
      console.log(that.formDatalist);
      var params = {
        page: this.pageIndex,
        rows: this.pageSize,
        user_id: that.formDatalist.userName,
        //  user_name:that.formDatalist.userName,
        beginDate: that.formDatalist.DATE && that.formDatalist.DATE.length ? that.formDatalist.DATE[0] + ' 00:00:00' : '',
        endDate: that.formDatalist.DATE && that.formDatalist.DATE.length ? that.formDatalist.DATE[1] + ' 23:59:59' : '',
      }
      // return;
      that.lodin = true;
      server.get('/api/auth/jwt/queryLoginHistory', { 'params': params }).then((res) => {
        // console.log(res)
        this.totalCount = res.total;
        this.tableData = res.rows;
        that.lodin = false;
      }).catch((err) => {
        console.log(err)
        that.lodin = false;
      })
    },
    // 查询
    submitForm(formName) {
      let that = this;
      this.pageIndex = 1;
      that.pageSize = this.tablelist.pageSize;
      this.queryData()
    },
    resetForm(formName) {
      //this.queryDateInit()
      this.pageIndex = 1;
      this.pageSize = this.tablelist.pageSize;
      this.formDatalist.userName = '';
      this.formDatalist.DATE = [];
      this.formDatalist1 = {};
      this.queryData()
    }
  }
}
</script>

<style lang='scss' scoped>
.search-tile {
  line-height: 34px;
  color: #999;
  margin-bottom: 30px;
  border-bottom: 1px solid #ddd;
  font-size: 16px;
}

.table-box {
  margin-top: 10px;
}

.tablebtn {
  padding: 5px !important;
}

.tablebtn.el-button+.tablebtn.el-button {
  margin-left: 5px;
}
</style>
<style lang="scss">
// @import "src/styles/common.scss";</style>