<template>
  <div class="app-container">

    <el-form :inline="true" :model="queryForm" size="small" label-width="100px" class="demo-form-inline">
      <el-form-item label="地市:">
        <el-select v-model="queryForm.cityId" :disabled="cityDisabled" @change="cityChange" filterable clearable
          placeholder="请选择">
          <el-option v-for="(item, index) in citylist" v-show="index !== 0" :key="item.cityId" :label="item.cityName"
            :value="item.cityId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="区域:">
        <el-select v-model="queryForm.areaId" :disabled="areaDisabled" @change="areaChange(queryForm.areaId)" filterable
          clearable placeholder="请选择">
          <el-option v-for="item in areaList" :key="item.areaId" :label="item.areaName"
            :value="item.areaName"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="局站:">
        <el-select v-model="queryForm.siteId" @change="siteChange" :remote-method="getSite" filterable clearable
          placeholder="请选择">
          <el-option v-for="(item, index) in siteList" filterable :key="`${index}-${item.siteId}`"
            :label="item.siteName" :value="item.siteId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="机房:">
        <el-select v-model="queryForm.roomId" @change="roomChange" :remote-method="getRoom" filterable clearable
          placeholder="请选择">
          <el-option v-for="(item, index) in roomlist" :key="`${index}-${item.roomId}`" :label="item.roomName"
            :value="item.roomId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备类型:">
        <el-select v-model="queryForm.deviceType" @change="devTypeChange" :remote-method="getDeviceType" filterable
          remote clearable placeholder="请输入">
          <el-option v-for="(item, index) in deviceTypeList" :key="`${index}-${item.id}`" :label="item.label"
            :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备名称:">
        <el-select v-model="queryForm.deviceName" filterable remote clearable placeholder="请输入"
          :remote-method="getDeviceName">
          <el-option v-for="(item, index) in devoptions" :key="`${index}-${item.id}-${item.label}`" :label="item.label"
            :value="item.label">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="告警标题:">
        <!-- alarmTitleList: [], -->
        <el-select v-model="queryForm.alarmDesc" filterable remote clearable placeholder="请输入"
          :remote-method="getAlarmTitle">
          <el-option v-for="(item, index) in alarmTitleList" :key="`${index}-${item}`" :label="item" :value="item">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="告警级别:">
        <el-select v-model="queryForm.alarmLevel" placeholder="请选择" clearable>
          <el-option v-for="item in [1, 2, 3, 4, 5, 6, 7]" :key="item" :label="item" :value="item"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="时间：">
        <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="date" type="datetimerange" range-separator="至"
          start-placeholder="开始日期" end-placeholder="结束日期">
        </el-date-picker>
      </el-form-item>
      <el-form-item label="">
        <el-button style="margin-left: 80px;" @click="queryFormReset">重置</el-button>
        <el-button type="primary" v-if="!showDetail" @click="queryData(1, 10)">查询</el-button>
        <el-button type="primary" v-if="showDetail" @click="queryDetial(1, 10)">查询</el-button>
        <el-button type="primary" v-if="!showDetail" @click="waringExport" :loading="exportloading">导出</el-button>
        <el-button type="primary" v-if="showDetail" @click="waringDetialExport"
          :loading="exportDetailloading">导出</el-button>
        <el-button type="primary" v-if="showDetail" @click="goback">返回</el-button>
      </el-form-item>
    </el-form>
    <el-table v-if="!showDetail" :data="deviceTableData" header-row-class-name="myHeaderClass" size="small"
      v-loading="tableLoading">
      <el-table-column prop="cityName" align="center" label="地市"></el-table-column>
      <el-table-column prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
      <el-table-column prop="siteName" align="center" label="局站" show-overflow-tooltip></el-table-column>
      <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
      <el-table-column align="center" label="告警总数" show-overflow-tooltip>
        <template slot-scope="scope">
          <div style="cursor: pointer;text-decoration-line: underline;color: #409eff;"
            @click="showDetailFn(scope.row, '')">{{ scope.row.alarmTotal }}</div>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="noClearAlarm" align="center" label="未清除告警数" show-overflow-tooltip>
        <template slot-scope="scope">
          <div style="cursor: pointer;text-decoration-line: underline;color: #409eff;"
            @click="showDetailFn(scope.row, 'noClear')">{{ scope.row.noClearAlarm }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="clearAlarm" align="center" label="已清除告警数">
        <template slot-scope="scope">
          <div style="cursor: pointer;text-decoration-line: underline;color: #409eff;"
            @click="showDetailFn(scope.row, 'clear')">{{ scope.row.clearAlarm }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="clearRate" align="center" label="清除率"></el-table-column> -->
    </el-table>
    <div v-if="!showDetail" class="pag">
      <el-pagination @size-change="handleSizeChange" background @current-change="handleCurrentChange"
        :current-page="currentPage" :page-sizes="[10, 20, 50]" :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </div>


    <div v-if="showDetail">
      <el-table :data="deviceDetailTableData" header-row-class-name="myHeaderClass" size="small"
        v-loading="detailLoading">
        <el-table-column prop="cityName" align="center" label="地市"></el-table-column>
        <el-table-column prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
        <el-table-column prop="siteName" align="center" label="局站" show-overflow-tooltip></el-table-column>
        <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
        <el-table-column prop="deviceTypeName" align="center" label="设备类型" show-overflow-tooltip></el-table-column>
        <el-table-column prop="deviceName" align="center" label="设备名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmLevel" align="center" label="告警级别" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmTime" align="center" label="告警发生时间" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmClearTime" align="center" label="告警清除时间" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmDuration" align="center" label="告警历时（分）" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmDesc" align="center" label="告警标题" show-overflow-tooltip></el-table-column>
        <el-table-column prop="alarmDesc" align="center" label="告警描述" show-overflow-tooltip></el-table-column>
        <el-table-column prop="triggerVal" align="center" label="触发值" show-overflow-tooltip></el-table-column>
      </el-table>
      <div class="pag">
        <el-pagination @size-change="detailehandleSizeChange" background @current-change="detailhandleCurrentChange"
          :current-page="detailCurrentPage" :page-sizes="[10, 20, 50]" :page-size="detailPageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="detailTotal">
        </el-pagination>
      </div>
    </div>

  </div>
</template>

<script>
import dayjs from 'dayjs'

export default {
  data() {
    return {

      showDetail: false,
      exportDetailloading: false,
      exportloading: false,
      // 查询条件下拉数据
      citylist: [],
      areaList: [],
      siteList: [],
      roomlist: [],
      deviceTypeList: [],
      cedianlist: [],
      cedianNameList: [],
      devoptions: [],
      alarmTitleList: [],
      tableLoading: false,
      queryForm: {
        alarmDesc: '',
        alarmLevel: '',
        areaId: '',
        cityId: '',
        deviceName: '',
        roomId: '',
        siteId: '',
        deviceTypeCode: ''
      },
      date: [dayjs().subtract(7, 'day').format("YYYY-MM-DD HH:mm:ss"), dayjs().format("YYYY-MM-DD HH:mm:ss")],

      deviceTableData: [],
      pageSize: 10,
      currentPage: 1,
      total: 0,

      deviceDetailTableData: [],
      detailLoading: false,
      detailPageSize: 10,
      detailCurrentPage: 1,
      detailTotal: 0,
      content: '',
      currentRow: null,
      clearType: '',
      role: '',
      cityDisabled: true,
      areaDisabled: true,
    }
  },
  created() {
    this.role = sessionStorage.getItem('role')
    this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
    this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''

    if (this.role == '1') {
      this.cityDisabled = false
      this.areaDisabled = false
    }

    if (['94', '95'].includes(this.role)) {
      this.cityDisabled = true
      this.areaDisabled = this.queryForm.areaId ? true : false
    }
    this.queryData()

    setTimeout(() => {
      this.queryCityList()
      this.getArea()
      this.getSite()
      this.getRoom()
      this.getDeviceType()
      this.getDeviceName()
      this.getAlarmTitle()
    }, 1000)

  },
  methods: {
    showDetailFn(row, clearType) {
      this.showDetail = true
      this.clearType = clearType
      this.currentRow = row
      this.queryDetial()
    },
    // 查询详情
    queryDetial(pageNum, pageSize) {
      this.detailLoading = true
      if (pageNum) this.detailCurrentPage = pageNum
      if (pageSize) this.detailPageSize = pageSize
      let temp;
      try {
        temp = this.areaList.filter(i => {
          return i.areaName == this.queryForm.areaName
        })
        // this.queryForm.areaId = temp[0].areaId
      } catch (error) {

      }
      let params = {
        ...this.queryForm,
        // ...temp[0],
        clear: this.clearType,
        areaId: this.currentRow.areaId,
        cityId: this.currentRow.cityId,
        roomId: this.currentRow.roomId,
        siteId: this.currentRow.siteId,
        startReportTime: this.date[0],
        endReportTime: this.date[1],
        pageNum: pageNum || this.detailCurrentPage,
        pageSize: pageSize || this.detailPageSize
      }
      this.$api.alarmDetail(params).then(res => {
        if (res.code == 200) {
          this.deviceDetailTableData = res.data.records
          this.detailLoading = false
          this.detailTotal = res.data.total
        } else {
          this.detailLoading = false
          this.$message.error(res.msg)
        }
      })
    },
    detailehandleSizeChange(val) {
      this.detailPageSize = val
      this.queryDetial()
    },
    detailhandleCurrentChange(val) {
      this.detailCurrentPage = val
      this.queryDetial()
    },
    goback() {
      this.detailCurrentPage = 1
      this.showDetail = false
    },
    async waringDetialExport() {
      this.exportDetailloading = true
      // let temp;
      // try {
      //   temp = this.areaList.filter(i => {
      //     return i.areaName == this.queryForm.areaName
      //   })
      //   // this.queryForm.areaId = temp[0].areaId
      // } catch (error) {

      // }
      let params = {
        ...this.queryForm,
        // ...temp[0],
        areaId: this.currentRow.areaId,
        cityId: this.currentRow.cityId,
        roomId: this.currentRow.roomId,
        siteId: this.currentRow.siteId,
        startReportTime: this.date[0],
        endReportTime: this.date[1],
        // pageNum: 1,
        // pageSize: 9999
      }
      try {
        let res = await this.$api.alarmdetailExport(params)
        // console.log(302011, response);
        var blob = new Blob([res], { type: "application/octet-stream" });
        var link = document.createElement('a');//a标签下载
        link.href = window.URL.createObjectURL(blob);
        link.download = '告警明细统计.xlsx';
        link.click();
        window.URL.revokeObjectURL(link.href);
      } catch (error) {
        this.$message.error('导出异常')
      } finally {
        this.exportDetailloading = false

      }

    },
    async waringExport() {
      this.exportloading = true
      let temp;
      try {
        temp = this.areaList.filter(i => {
          return i.areaName == this.queryForm.areaName
        })
        // this.queryForm.areaId = temp[0].areaId
      } catch (error) {

      }
      let params = {
        ...this.queryForm,
        ...temp[0],
        clear: '',
        startReportTime: this.date[0],
        endReportTime: this.date[1],
        // pageNum: 1,
        // pageSize: 9999
      }
      try {
        let res = await this.$api.alarmExport(params)
        // console.log(302011, response);
        var blob = new Blob([res], { type: "application/octet-stream" });
        var link = document.createElement('a');//a标签下载
        link.href = window.URL.createObjectURL(blob);
        link.download = '告警统计.xlsx';
        link.click();
        window.URL.revokeObjectURL(link.href);
      } catch (error) {
        this.$message.error('导出异常')
      } finally {
        this.exportloading = false
      }

    },
    // 初始化地市
    queryCityList() {
      let params = {
        cityName: "",
        pageNum: 1,
        pageSize: 999
      }
      this.$api.getAlarmCitylist(params).then(res => {
        if (res.code == 200) {
          this.citylist = res.data.records
        }
      })
    },
    cityChange(val) {
      // this.queryForm.areaId = ''
      // this.queryForm.siteId = ''
      // this.queryForm.roomId = ''
      // this.queryForm.deviceType = ''
      // this.queryForm.deviceName = ''
      // this.areaList = []
      // this.siteList = []
      // this.roomlist = []
      // this.deviceTypeList = []
      // this.devoptions = []
      this.areaList = this.citylist.find(v => v.cityId == val).areaList
      this.getArea()
      this.getSite()
      this.getRoom()
      this.getDeviceName()
      this.getDeviceType()
    },
    areaChange(val) {
      // console.log(val);

      // this.queryForm.siteId = ''
      // this.queryForm.roomId = ''
      // this.queryForm.deviceType = ''
      // this.queryForm.deviceName = ''
      // this.siteList = []
      // this.roomlist = []
      // this.deviceTypeList = []
      // this.devoptions = []      
      this.getSite()
      this.getRoom()
      this.getDeviceName()
      this.getDeviceType()
    },
    siteChange() {
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.queryForm.deviceName = ''
      this.roomlist = []
      this.deviceTypeList = []
      this.devoptions = []
      this.getRoom()
      this.getDeviceName()
      this.getDeviceType()
    },
    roomChange() {
      this.queryForm.deviceType = ''
      this.queryForm.deviceName = ''
      this.deviceTypeList = []
      this.devoptions = []
      this.getDeviceName()
      this.getDeviceType()
    },
    devTypeChange() {
      this.queryForm.deviceName = ''
      this.devoptions = []
      this.getDeviceName()
    },
    getArea(val) {
      if (!sessionStorage.getItem('areaId')) {
        this.queryForm.areaId = ''
      }
      // this.queryForm.siteId = ''
      // this.queryForm.roomId = ''
      // this.queryForm.deviceType = ''
      // this.areaList = []
      // this.siteList = []
      // this.roomlist = []
      // this.deviceTypeList = []
      let params = {
        cityId: this.queryForm.cityId,
        areaName: val,
        pageNum: 1,
        pageSize: 999
      }
      this.$api.getAlarmArealist(params).then(res => {
        if (res.code == 200) {
          this.areaList = res.data.records
        }
      })
    },
    getSite(val) {      
      let params = {
        cityId: this.queryForm.cityId,
        areaId: this.queryForm.areaId,
        siteName: val,
        pageNum: 1,
        pageSize: 999
      }

      this.$api.getAlarmSitelist(params).then(res => {
        if (res.code == 200) {
          this.siteList = res.data.records
        }
      })
    },
    getRoom(val) {
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.roomlist = []
      this.deviceTypeList = []
      let temp;
      // try {
      //   temp = this.areaList.filter(i => {
      //     return i.areaName == this.queryForm.areaName
      //   })
      //   // this.queryForm.areaId = temp[0].areaId
      // } catch (error) {

      // }
      let params = {
        cityId: this.queryForm.cityId,
        areaId: this.queryForm.areaId,
        siteId: this.queryForm.siteId,
        roomName: val,
        pageNum: 1,
        pageSize: 999
      }
      this.$api.getAlarmRoomlist(params).then(res => {
        if (res.code == 200) {
          this.roomlist = res.data.records
        }
      })
    },
    getAlarmTitle(val) {
      let params = {
        columnName: 'alarm_desc',
        columnValue: val,
        pageNum: 1,
        pageSize: 999
      }
      this.$api.getAlarmColumnNamelist(params).then(res => {
        if (res.code == 200) {
          this.alarmTitleList = res.data.records
        }
      })
    },
    async getDeviceName(queryString) {
      
      let res = await this.$api.deviceNameQuery({
        deviceName: queryString,
        cityId: this.queryForm.cityId ? [this.queryForm.cityId] : [],
        areaId: this.queryForm.areaId ? [this.queryForm.areaId] : [],
        siteId: this.queryForm.siteId ? [this.queryForm.siteId] : [],
        roomId: this.queryForm.roomId ? [this.queryForm.roomId] : [],
        deviceType: this.queryForm.deviceType ? [this.queryForm.deviceType] : [],
        current: 1,
        size: 999
      })
      if (res.code == 200) {
        this.devoptions = res.data.records
      } else {
        this.$message.error(res.msg)

      }
    },
    async getDeviceType(val) {
      let res = await this.$api.queryDeviceType({
        deviceType: val,
        cityId: this.queryForm.cityId ? [this.queryForm.cityId] : [],
        areaId: this.queryForm.areaId ? [this.queryForm.areaId] : [],
        siteId: this.queryForm.siteId ? [this.queryForm.siteId] : [],
        roomId: this.queryForm.roomId ? [this.queryForm.roomId] : [],
        current: 1,
        size: 999
      })
      if (res.code == 200) {
        this.deviceTypeList = res.data.records
      } else {
        this.$message.error(res.msg)
      }
    },
    queryFormReset() {
      location.reload()
      /*
      this.areaList = []
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      this.devoptions = []

      this.queryForm = {
        alarmDesc: '',
        alarmLevel: '',
        areaId: '',
        cityId: '',
        deviceName: '',
        roomId: '',
        siteId: '',
      },
        this.date = [dayjs().subtract(7, 'day').format("YYYY-MM-DD HH:mm:ss"), dayjs().format("YYYY-MM-DD HH:mm:ss")]
      this.queryData(1, 10)

      this.queryCityList()
      this.getArea()
      this.getSite()
      this.getRoom()
      this.getDeviceType()
      this.getDeviceName()
      this.getAlarmTitle()
      this.role = sessionStorage.getItem('role')
      this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
      this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''

      if (this.role == '1') {
        this.cityDisabled = false
        this.areaDisabled = false
      }

      if (['94', '95'].includes(this.role)) {
        this.cityDisabled = true
      }
      this.queryData(1, 10)
      */

    },

    // 监控量
    queryData(pageNum, pageSize) {
      this.tableLoading = true
      if (pageNum) this.currentPage = pageNum
      if (pageSize) this.pageSize = pageSize
      let temp;
      try {
        temp = this.areaList.filter(i => {
          return i.areaName == this.queryForm.areaName
        })
        // this.queryForm.areaId = temp[0].areaId
      } catch (error) {

      }

      let params = {
        ...this.queryForm,
        ...temp[0],
        startReportTime: this.date[0],
        endReportTime: this.date[1],
        pageNum: pageNum || this.currentPage,
        pageSize: pageSize || this.pageSize
      }
      this.$api.deviceAlarm(params).then(res => {
        if (res.code == 200) {
          this.deviceTableData = res.data.records
          this.tableLoading = false
          this.total = res.data.total
        } else {
          this.tableLoading = false
          this.$message.error(res.msg)
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.queryData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.queryData()
    }
  }
}
</script>

<style lang="scss" scoped>
.ad {
  display: flex;
  gap: 6px;

  i {
    cursor: pointer;
  }
}

.pag {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 12px;
}

.selectWrap {
  width: 600px;
  height: 60px;
  margin-left: 100px;
  overflow: scroll;
  margin-bottom: 16px;
  //box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
  border: 1px solid #d7dae2;
}
</style>