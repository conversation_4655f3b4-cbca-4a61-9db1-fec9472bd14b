import http from '@/api/http'

export function page(query) {
  return http({
    url: '/api/flowable/formDesign/pageFormDesignList',
    method: 'get',
    params: query
  });
}


export function pageSave(temp) {
  return http({
    url: '/api/flowable/formDesign/pageFormDesignSave',
    method: 'post',
    params: temp
  });
}

export function getEditFormDesign(temp) {
  return http({
    url: '/api/flowable/formDesign/getEditFormDesign',
    method: 'post',
    params: temp
  });
}

export function updateEditFormDesign(temp) {
  return http({
    url: '/api/flowable/formDesign/updateEditFormDesign',
    method: 'put',
    params: temp
  });
}
