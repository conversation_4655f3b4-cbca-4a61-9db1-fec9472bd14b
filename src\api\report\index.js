import http from '@/api/http'
import {
  Message,
  MessageBox,
  Confirm
} from 'element-ui';

///api/report/dataTable/1 http://172.17.13.109/api/report/report/dataTable/1
export function getReportData(data,typ) {
  return http({
    url: '/api/report/report/dataTable/'+typ,
    method: 'post',
    data: data
  });
}
//二期报表表头 api/report/report/2021120802
export function getreporTiTlist(obj,data) {
  return http({
    url: '/api/report/report/'+data,
    method: 'get',
    params: obj
  });
}
/// 清单表头
export function getreportlisttitle(data) {
  return http({
    url: '/api/report/report/reportlisttitle',
    method: 'post',
    data: data
  });
}
///获取清单表格数据
export function getdataTable(query,pams,dat) {
  return http({
    url: '/api/report/report/dataTable/'+pams,
    method: 'post',
    data: query,
    'headers':dat,
  });
}
//关键性能数据导出 
export function downloadxnReport(data,typ) {
  return http({
    url: '/api/report/report/downloadxnReport/'+typ,
    method: 'post',
    data: data,
    responseType: 'blob'
  });
}

//
export function downReport(data,typ) {
  return http({
    url: '/api/report/report/downReport/'+typ,
    method: 'post',
    data: data,
    responseType: 'blob'
  });
}
///api/report/report/downloadReport
export function downloadReport(data,typ) {
  return http({
    url: '/api/report/report/downloadReport/'+typ,
    method: 'post',
    data: data,
    responseType: 'blob'
  });
}
export function downloadfilter(data,typ){
  return new Promise((resolve, reject) => {
    downloadReport(data,typ).then(res => {
     // console.log(res)
      const resData = res
      if (resData.type === 'application/json') {
        // 说明是普通对象数据，读取信息
        const fileReader = new FileReader()
        fileReader.onloadend = () => {
          const jsonData = JSON.parse(fileReader.result)
          // 后台信息
         // console.log(jsonData)
         Message({
                showClose: true,
                message: jsonData.message,
                type: 'warning',
                duration: 2000
              });
        }
        fileReader.readAsText(res)  //或者读取response.data
      }else{
        //console.log(111)
           var blob = new Blob([res], {type: "application/vnd.ms-excel;charset=utf-8"});
         
          if (window.navigator.msSaveOrOpenBlob) {//msSaveOrOpenBlob方法返回bool值
            navigator.msSaveBlob(blob, data.fileName+ '.xlsx');//本地保存
          } else {
            var link = document.createElement('a');//a标签下载
            link.href = window.URL.createObjectURL(blob);
            link.download = data.fileName+ '.xlsx';
            link.click();
            window.URL.revokeObjectURL(link.href);
          }

        }
    
    
      resolve(res);
    }).catch(error => {
      reject(error);
    });
  });
}
export function downloReport(data,typ){

 
  // return;
  return new Promise((resolve, reject) => {
    MessageBox.confirm('是否附加报表清单(清单可能数据较大，导出较慢)?', '提示', {
      distinguishCancelAndClose: true,
      confirmButtonText: '附加',
      cancelButtonText: '不附加',
      type: 'warning'
    }).then(() => {
      Message({
        type: 'success',
        message: '正在导出'
      });
      data['attachlist'] = 'true';
     // downloadfilter(data,typ)
     downloadReport(data,typ).then(res => {
      // console.log(res)
       const resData = res
       if (resData.type === 'application/json') {
         // 说明是普通对象数据，读取信息
         const fileReader = new FileReader()
         fileReader.onloadend = () => {
           const jsonData = JSON.parse(fileReader.result)
           // 后台信息
          // console.log(jsonData)
          Message({
                 showClose: true,
                 message: jsonData.message,
                 type: 'warning',
                 duration: 2000
               });
         }
         fileReader.readAsText(res)  //或者读取response.data
       }else{
         //console.log(111)
            var blob = new Blob([res], {type: "application/vnd.ms-excel;charset=utf-8"});
          
           if (window.navigator.msSaveOrOpenBlob) {//msSaveOrOpenBlob方法返回bool值
             navigator.msSaveBlob(blob, data.fileName+ '.xlsx');//本地保存
           } else {
             var link = document.createElement('a');//a标签下载
             link.href = window.URL.createObjectURL(blob);
             link.download = data.fileName+ '.xlsx';
             link.click();
             window.URL.revokeObjectURL(link.href);
           }
 
         }
     
     
       resolve(res);
     }).catch(error => {
       reject(error);
     });
    }).catch((action) => {
     // console.log(action) //cancel
      if(action == 'cancel'){

      
      Message({
        type: 'success',
        message: '正在导出'
      });
      data['attachlist'] = 'false';
      //downloadfilter(data,typ)
      downloadReport(data,typ).then(res => {
        // console.log(res)
         const resData = res
         if (resData.type === 'application/json') {
           // 说明是普通对象数据，读取信息
           const fileReader = new FileReader()
           fileReader.onloadend = () => {
             const jsonData = JSON.parse(fileReader.result)
             // 后台信息
            // console.log(jsonData)
            Message({
                   showClose: true,
                   message: jsonData.message,
                   type: 'warning',
                   duration: 2000
                 });
           }
           fileReader.readAsText(res)  //或者读取response.data
         }else{
           //console.log(111)
              var blob = new Blob([res], {type: "application/vnd.ms-excel;charset=utf-8"});
            
             if (window.navigator.msSaveOrOpenBlob) {//msSaveOrOpenBlob方法返回bool值
               navigator.msSaveBlob(blob, data.fileName+ '.xlsx');//本地保存
             } else {
               var link = document.createElement('a');//a标签下载
               link.href = window.URL.createObjectURL(blob);
               link.download = data.fileName+ '.xlsx';
               link.click();
               window.URL.revokeObjectURL(link.href);
             }
   
           }
       
       
         resolve(res);
       }).catch(error => {
         reject(error);
       });
      }else{
        Message({
          type: 'info',
          message: '取消导出',
          duration: 2000
            
        });
        resolve('res');
      }
    });
   
  });
 
}

export function downloReportxn(data,typ){
  // return;
  return new Promise((resolve, reject) => {
    MessageBox.confirm('是否附加报表清单(清单可能数据较大，导出较慢)?', '提示', {
      distinguishCancelAndClose: true,
      confirmButtonText: '附加',
      cancelButtonText: '不附加',
      type: 'warning'
    }).then(() => {
      Message({
        type: 'success',
        message: '正在导出'
      });
      data['attachlist'] = 'true';
     // downloadfilter(data,typ)
     downloadxnReport(data,typ).then(res => {
      // console.log(res)
       const resData = res
       if (resData.type === 'application/json') {
         // 说明是普通对象数据，读取信息
         const fileReader = new FileReader()
         fileReader.onloadend = () => {
           const jsonData = JSON.parse(fileReader.result)
           // 后台信息
          // console.log(jsonData)
          Message({
                 showClose: true,
                 message: jsonData.message,
                 type: 'warning',
                 duration: 2000
               });
         }
         fileReader.readAsText(res)  //或者读取response.data
       }else{
         //console.log(111)
            var blob = new Blob([res], {type: "application/vnd.ms-excel;charset=utf-8"});
          
           if (window.navigator.msSaveOrOpenBlob) {//msSaveOrOpenBlob方法返回bool值
             navigator.msSaveBlob(blob, data.fileName+ '.zip');//本地保存
           } else {
             var link = document.createElement('a');//a标签下载
             link.href = window.URL.createObjectURL(blob);
             link.download = data.fileName+ '.zip';
             link.click();
             window.URL.revokeObjectURL(link.href);
           }
 
         }
     
     
       resolve(res);
     }).catch(error => {
       reject(error);
     });
    }).catch((action) => {
     // console.log(action) //cancel
      if(action == 'cancel'){

      
      Message({
        type: 'success',
        message: '正在导出'
      });
      data['attachlist'] = 'false';
      //downloadfilter(data,typ)
      downloadReport(data,typ).then(res => {
        // console.log(res)
         const resData = res
         if (resData.type === 'application/json') {
           // 说明是普通对象数据，读取信息
           const fileReader = new FileReader()
           fileReader.onloadend = () => {
             const jsonData = JSON.parse(fileReader.result)
             // 后台信息
            // console.log(jsonData)
            Message({
                   showClose: true,
                   message: jsonData.message,
                   type: 'warning',
                   duration: 2000
                 });
           }
           fileReader.readAsText(res)  //或者读取response.data
         }else{
           //console.log(111)
              var blob = new Blob([res], {type: "application/vnd.ms-excel;charset=utf-8"});
            
             if (window.navigator.msSaveOrOpenBlob) {//msSaveOrOpenBlob方法返回bool值
               navigator.msSaveBlob(blob, data.fileName+ '.xlsx');//本地保存
             } else {
               var link = document.createElement('a');//a标签下载
               link.href = window.URL.createObjectURL(blob);
               link.download = data.fileName+ '.xlsx';
               link.click();
               window.URL.revokeObjectURL(link.href);
             }
   
           }
       
       
         resolve(res);
       }).catch(error => {
         reject(error);
       });
      }else{
        Message({
          type: 'info',
          message: '取消导出',
          duration: 2000
            
        });
        resolve('res');
      }
    });
   
  });
 
}

