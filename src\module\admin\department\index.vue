<template>
 <div class="app-container calendar-list-container disposefiles">
   <div class="filter-container">
     <el-button-group>
    <el-button type="primary" size="small"  @click="handlerAdd">添加</el-button>
    <el-button type="primary" size="small"   @click="handlerEdit">编辑</el-button>
    <el-button type="primary" size="small"   @click="handleDelete">删除</el-button>
    <el-button type="primary" size="small"  @click="handleadd(1)">部门人员</el-button>
   <!-- <el-button type="primary" size="small"  @click="handleadd(2)">添加部门人员</el-button>-->
  </el-button-group>

<el-row :gutter="10">
  <el-col :span="8" style='margin-top:10px;'>
    <el-input
      placeholder="输入关键字进行过滤"
      v-model="filterText">
    </el-input>
   <div class="departree" ref="treedv">
    <el-tree
      class="filter-tree"
      :data="treeData"
      node-key="DEPART_NO"
      highlight-current
      :props="defaultProps"
      :filter-node-method="filterNode"
      ref="departTree"
      @node-click="getNodeData"
      
      >
    </el-tree>
      </div>
  </el-col>
  <el-col :span="16" style='margin-top:10px;'>
     <el-card class="box-card">
    <el-form :label-position="labelPosition" label-width="80px" :model="form" ref="form" label-suffix=":">
      <el-form-item label="部门编号" prop="code" v-show="formStatus !='create'">
          <el-input v-model="form.code" :disabled="true" placeholder="请输入部门编号"></el-input>
      </el-form-item>
      <el-form-item label="部门名称" prop="title">
          <el-input v-model="form.title" :disabled="formEdit"  placeholder="请输入部门名称"></el-input>
      </el-form-item>
      <el-form-item label="部门等级" prop="departLevel"  v-show="formStatus !='create'">
          <el-input v-model="form.departLevel" :disabled="true" placeholder="请输入部门等级" readonly></el-input>
      </el-form-item>
      <el-form-item label="地市">
        <el-select class="filter-item" :disabled="formEdit"  v-model="form.cityCode" placeholder="请选择地市" @change="getfindCitylist">
          <el-option v-for="(item,inde) in citieCounty" :key="inde" :label="item.VALUE" :value="item.KEYSS"></el-option>
        </el-select>
      </el-form-item>
<!--      <el-form-item label="管理区域">
        <el-select class="filter-item" multiple :disabled="formEdit"   v-model="form.areaCode" placeholder="请选择管理区域">
          <el-option v-for="(item,inde) in countydata" :key="inde" :label="item.VALUE" :value="item.KEYSS"></el-option>
        </el-select>
      </el-form-item>-->
      <el-form-item label="区县">
        <el-select class="filter-item" :disabled="formEdit"  v-model="form.areaId" placeholder="请选择区县">
          <el-option v-for="(item,inde) in areaCounty" :key="inde" :label="item.city_name" :value="item.city_code"></el-option>
        </el-select>
      </el-form-item>

      <el-form-item label="排序" prop="orderNum">
          <el-input v-model="form.orderNum" :disabled="formEdit" placeholder="请输入排序"></el-input>
      </el-form-item>
      <el-form-item label="描述"   prop="remark">
          <el-input v-model="form.remark" :disabled="formEdit" placeholder="请输入描述"></el-input>
      </el-form-item>
       <el-form-item v-if="formStatus == 'update'">
        <el-button  :loading="updateload" type="primary" @click="update">更新</el-button>
        <el-button @click="onCancel">取消</el-button>
      </el-form-item>
      <el-form-item v-if="formStatus == 'create'">
        <el-button :loading="createload" type="primary" @click="create">保存</el-button>
        <el-button @click="onCancel">取消</el-button>
      </el-form-item>
    </el-form>
     </el-card>
  </el-col>
</el-row>
<!-- 添加人员-------strat-->
   <el-dialog :title="addPoptitle" width="1200px" :visible.sync="dialogAddVisible1" top="10vh"  @close="closeAdd1" :close-on-click-modal="false">
      <add-pop :closeDiadl="closeDiadl1" ref="addPop" ></add-pop>
    </el-dialog>
 </div>
  </div>
</template>

<script>
import {
  getDepartAll,//获取全部部门
  addDepart,//添加部门
  delDepart,//删除部门
  updDepart,//跟新部门
  getReqionList,//区域
  getComCityList,//地市
  getDepartInfo ,//获取节点数据进行编辑
} from '@/api/admin/department/index';
 import {
       
        getfindCityArealist,//区县
        } from '@/api/resource/baseStation/index';
import { mapGetters } from 'vuex';
export default {
  name: 'departTrees',
  components: {
    'add-pop': () => import('./commpont/add'),
  },
  data() {
    return {
      filterText: '',
      list: null,
      total: null,
      formEdit: true,
      formAdd: true,
      formStatus: '',
      showElement: false,
      typeOptions: ['menu', 'dirt'],
      listQuery: {
        name: undefined
      },
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'DEPART_NAME'
      },
      labelPosition: 'right',
      form: {
        code: undefined,
        title: undefined,
        departLevel:undefined,//部门等级
        cityCode:null,//地市
        areaCode:[],//管理区域
        orderNum: undefined,
        remark: undefined,
        areaId:null,//区县
      },
      currentId: -1,
      pardepartLevel:0,
      parentDepartNo:null,
      formshow:true,
      citieCounty:[],//地市
      countydata:[],//区域
      city_id: '330100',//地市
      updateload:false,//更新加载
      createload:false,//添加加载
      dialogAddVisible1:false,
      addPopflg:null,
      addPoptitle:'添加部门人员',
      menuManager_btn_add: false,
      menuManager_btn_edit: false,
      menuManager_btn_del: false,
      areaCounty:[],
    }
  },
  watch: {
    filterText(val) {
      this.$refs.departTree.filter(val);
    }
  },
  created() {
    this.getList();
    this.menuManager_btn_add = this.elements['menuManager:btn_add'];
    this.menuManager_btn_del = this.elements['menuManager:btn_del'];
    this.menuManager_btn_edit = this.elements['menuManager:btn_edit'];
  },
  computed: {
    ...mapGetters([
      'elements'
    ])
  },
  mounted(){
    this.$refs.treedv.style.height = (document.documentElement.clientHeight - 227) + 'px';
    this.getCitiList();
  },
  methods: {
       //获取地市
    getCitiList(){
      let that = this;
      let obj = {
          id:'123'
      }
      getComCityList(obj).then(data => {
        let dat = data;
        dat.push({'VALUE':dat[0].area_name,'KEYSS':dat[0].parent_city_code})
        that.citieCounty = dat;
       // that.form.cityCode = parseInt(that.city_id);

      }).catch(err =>{
      })
    },
     getfindCitylist(typ,code){//获取区县
     let that = this;
        let pams = {
          'area_code':this.form.cityCode,
        }
       getfindCityArealist(pams).then(res => {
         this.areaCounty = res;
          if(typ == '02'){
           that.form.areaId = code?code:null;
          };
      }).catch((erro)=>{

      });
     },
    getpostcounuty(typ,code){
      let that = this;
       that.form.areaCode = [];
       that.countydata = [];
      let obj = {
           cityId:that.form.cityCode,
           VALUE:that.form.cityCode,
      }
      /*getReqionList(obj).then(data => {
        that.countydata = data;
        if(typ == '02'){
           that.form.areaCode = code;
        };

      }).catch(err =>{
      })*/
    },
    getList() {
      getDepartAll(this.listQuery).then(data => {
       //  console.log(data)
        this.treeData = data;
      });
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.DEPART_NAME.indexOf(value) !== -1;
    },
    getNodeData(datat) {//获取 节点对应的数据
      let that = this;
       //console.log(datat)
       let childr = datat.children.length;
       that.addPopflg = childr;
      if (!this.formEdit) {
        this.formStatus = 'update';
      };

       this.pardepartLevel = datat.DEPART_NO;
       that.currentId = datat.DEPART_LEVEL;
       let obj = {
           'departNo':datat.DEPART_NO
       }
      getDepartInfo(obj).then((data) => {
        //  console.log(data)
       // this.form = response.data;
       that.form.code = data.depart_no,//部门编号
       that.form.title = data.depart_name,//部门名称
       that.form.departLevel = data.depart_level;//部门等级
       that.form.orderNum = data.order_list?data.order_list:'';//排序
       that.form.remark = data.remark?data.remark:'';//备注描述
      // that.form.cityCode = parseInt(data.area_name);
        that.form.cityCode = data.area_code;
      //  let regList = data.regionList;
      //  let arrlist = [];
      //  if(regList.length){
      //     regList.forEach((item)=>{
      //       arrlist.push(item.region_id);
      //     })
      //  }else{
      //      that.form.areaCode = [];
      //  }
       if(that.form.cityCode){
           that.getfindCitylist('02',data.city_code);
       }


      });
    },
    handlerEdit() {//编辑
     let that = this;
      //   console.log(this.form.id)
      if (this.form.code) {
        this.formEdit = false;
        this.formStatus = 'update';
      }else{
        that.$message({
            showClose: true,
            message: "请选择编辑节点",
            type: "warning",
            duration: 1500
        });
      }
    },
    handlerAdd() {//添加
    let that = this;
      if(that.pardepartLevel==0 && !that.form.departLevel){
        this.$confirm('添加根节点吗?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
         this.resetForm();
         this.formEdit = false;
         this.formStatus = 'create';
       });
      }else{
         this.resetForm();
         this.formEdit = false;
         this.formStatus = 'create';
      }

    },
    handleDelete() {
      let that = this;
     //   console.log(this.currentId)
      this.$confirm('此操作将永久删除, 是否继续?', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        let obj = {
            'departNo':this.pardepartLevel,//当前的departNo
        }
        delDepart(obj).then((data) => {
           if(data.flag == '1'){
                this.getList();
                this.resetForm();
                this.onCancel();
                that.$message({
                    showClose: true,
                    message: "删除成功",
                    type: "success",
                    duration: 1500
                });
           }else{
                that.$message({
                showClose: true,
                message: data.msg,
                type: "warning",
                duration: 1500
            });
           }

        }).catch((err)=>{
           that.$message({
            showClose: true,
            message: "删除失败",
            type: "warning",
            duration: 1500
          });

        });
      }).catch(()=>{
        
      })
    },
    update() {//更新部门信息
     let that = this;
     if(!that.form.title){
        that.$message({
            showClose: true,
            message: "请填写部门名称",
            type: "warning",
            duration: 1500
        });
        return;
     };
       let regionlist = that.form.areaCode;
           regionlist = regionlist.join(',');
/*     if(!regionlist){
        that.$message({
            showClose: true,
            message: "请选择管理区域",
            type: "warning",
            duration: 1500
        });
        return;
     };*/
     that.updateload = true;

     let obj = {
          'departNo':that.form.code,//当前的Id
          'departName':that.form.title,//部门名称
          'cityId':that.form.cityCode,//地市
          'regions':regionlist,//管理区域
          'areaId':that.form.areaId,//区县
          'orderList':that.form.orderNum,//排序
          'remark':that.form.remark,//描述
     }
      updDepart(obj).then((data) => {
        that.updateload = false;
        if(data.flag == '1'){
          that.$message({
            showClose: true,
            message: "更新成功",
            type: "success",
            duration: 1500
        });
          this.getList();
          that.resetForm();
          that.formEdit = true;
          that.formStatus = '';
          that.form.departLevel = '';
          that.pardepartLevel = 0;
        }else{
            that.$message({
            showClose: true,
            message:data.msg,
            type: "warning",
            duration: 2000
        });
        }

      }).catch((err)=>{
        that.$message({
            showClose: true,
            message:'更新信息失败',
            type: "warning",
            duration: 2000
        });
         that.updateload = false;
      });
    },
    create() {//添加保存
       let that = this;
       if(!that.form.title){
        that.$message({
            showClose: true,
            message: "请填写部门名称",
            type: "warning",
            duration: 1500
        });
        return;
       }
      let regionlist = that.form.areaCode;
           regionlist = regionlist.join(',');
 /*    if(!regionlist){
        that.$message({
            showClose: true,
            message: "请选择管理区域",
            type: "warning",
            duration: 1500
        });
        return;
     };*/
       that.createload = true;
      // console.log(regionlist);
      // return;
       let params = {
          'parentDepartNo':that.pardepartLevel,//父的id
          'departName':that.form.title,//部门名称
          'cityId':that.form.cityCode,
          'regions':regionlist,
          'areaId':that.form.areaId,//区县
          'orderList':that.form.orderNum,//排序
          'remark':that.form.remark,//描述
       }
      addDepart(params).then((data) => {
           that.createload = false;
         if(data.flag == '1'){
            that.$message({
                    showClose: true,
                    message: "创建成功",
                    type: "success",
                    duration: 2000
                });
            this.getList();
            that.resetForm();
            that.formEdit = true;
            that.formStatus = '';
            that.form.departLevel = '';
            that.pardepartLevel = 0;
         }else{
            that.$message({
                showClose: true,
                message:data.msg,
                type: "warning",
                duration: 2000
            });
         }

      }).catch((err)=>{
        that.$message({
            showClose: true,
            message:'添加失败联系支撑人员',
            type: "warning",
            duration: 2000
        });
        that.createload = false;
      });
    },
    onCancel() {
      this.formEdit = true;
      this.formStatus = '';
      //清空数据
      this.resetForm();
    },
    resetForm() {
      let that = this;
      this.form = {
        code: undefined,
        title: undefined,
        cityCode:that.city_id,//地市
        areaCode:[],//区域
        //parentId: this.currentId,
        departLevel:undefined,
        orderNum: undefined,
        remark: undefined,
      };
      that.addPopflg = null;//添加人员判断
    },
    handleadd(typ){//添加人员
      let that = this;
      if(that.addPopflg!=0){
          that.$message({
            showClose: true,
            message:'当前节点不可添加人员',
            type: "warning",
            duration: 2000
        });
        return;
      }
      if(typ == '2'){
        that.addPoptitle = "添加部门人员"
      }else if(typ == '1'){
         that.addPoptitle = "查看部门人员"
      }
      that.dialogAddVisible1 = true;
      that.$nextTick(() => {
         that.$refs.addPop.handlick(that.form.code,typ);
      });

    },
     closeDiadl1(typ){//添加明细关闭
      let that = this;
       // console.log(typ)
     if(typ ==="1"){
         that.dialogAddVisible1 = false;
        // console.log('取消')
      }else if(typ === "2"){//查询
       // console.log('确定')
        //that.getList();

      }else if(typ ==="3"){//只查询并关闭
         that.dialogAddVisible1 = false;
        // that.searchReinsuranceDetailData();
      }

      },
      closeAdd1(){
      this.$nextTick(()=>{
        this.$refs.addPop.handreset()
      })
    },
  }
}
</script>
<style scoped lang="scss">
   .departree{
       padding:10px 5px;
       background-color: #fff;
       overflow:auto;
       border: solid 1px #EBEEF5;
       border-top:none;
   }
</style>
<style lang="scss" >
// @import "src/styles/common.scss";

</style>

