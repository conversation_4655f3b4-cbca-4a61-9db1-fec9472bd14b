import { saveAs } from 'file-saver';
import * as XLSX from 'xlsx';
function exportTableToExcel(data, fileName) {
    

    // 将数据转换为工作表
    const worksheet = XLSX.utils.aoa_to_sheet(data);

    // 创建工作簿并添加工作表
    const workbook = XLSX.utils.book_new();
    XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1');

    // 生成Excel文件
    const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' });

    // 使用blob和FileReader创建一个Blob URL
    const dataBlob = new Blob([excelBuffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet;charset=UTF-8' });
    const blobUrl = window.URL.createObjectURL(dataBlob);

    // 使用saveAs下载文件
    saveAs(dataBlob, `${fileName}.xlsx`);

    // 清理
    window.URL.revokeObjectURL(blobUrl);
}
export default exportTableToExcel