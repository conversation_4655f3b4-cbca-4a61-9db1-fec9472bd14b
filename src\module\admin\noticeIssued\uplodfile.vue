<!-- 定制任务页面 -->
<template>
  <div class="calendar-list-container disposefiles handFilter">
    <div class="filter-container">
      <div class='martop10' v-if="rowtyp != 'toview'">
        <el-upload ref="upload1" :multiple="true" :show-file-list="false" :file-list="fileList1"
          :on-change="handleChange" class="upload-demo" action="" :http-request="myUpload" :headers="headers">
          <el-button size="mini" type="primary" icon="el-icon-upload"
            :style="{ background: color, borderColor: color }">
            上传附件
          </el-button>
          <span class="el-upload__tip uploawar">上传附件不超过30M</span>
        </el-upload>
      </div>

      <div class="zlistTablecom listTables martop10">
        <el-table size="small" stripe border :fit="true" highlight-current-row :data="tableData" max-height="150"
          style="width: 100%">
          <el-table-column type="index" align="left" width="50" label="序号"></el-table-column>
          <el-table-column align="left" label="文件名称" min-width="200%">
            <template slot-scope="scope">
              <a :download="scope.row.file_name"  :href="`/upload/${scope.row.file_id}.${scope.row.file_name.split('.')[1]}`" style="text-decoration: underline; font-style: italic"><span>{{
        scope.row.file_name }}</span></a>
            </template>
          </el-table-column>
          <el-table-column align="left" label="上传人" prop="upload_user">
          </el-table-column>
          <el-table-column align="left" label="上传时间" prop="upload_time">
          </el-table-column>
          <el-table-column align="left" label="操作">
            <template slot-scope="scope">
              <a @click="handleDelFile(scope.row)"
                style="text-decoration: underline; color: red; font-weight: bold; font-style: italic;">删&nbsp;除</a>
            </template>
          </el-table-column>
        </el-table>
      </div>

      <el-dialog class="addplandialog" title="图片预览" width="1000px" :visible.sync="dialogEditVisible" top="5vh"
        :append-to-body="true">
        <div class="diagimg">
          <img :src="imgsrc" />
        </div>
      </el-dialog>
    </div>
  </div>
</template>

<script>

import {
  getUserId,
} from '@/utils/auth';
import { downFile, queryImage } from '@/api/require/sgzl/upload_file';
import { uploadFile, saveOrderAddFile, deleteFile } from '@/api/require/sgzl/upload_file';
import { getFilesTabObj } from '@/api/form/form/index';
import qs from "qs";

export default {
  props: ['fileOrderNu', 'rowtyp'],
  data() {
    return {
      content: null,
      listQuery: {

        createUserId: this.$store.state.user.staffId,
      },

      fileList1: [],
      upload1: false,
      color: '#1890ff',
      headers: {},
      fileOrderNum: '',
      tableData: [],
      imgsrc: '',
      dialogEditVisible: false,
      rowtyp1: '',

    }
  },
  components: {},
  watch: {
    fileOrderNu: {
      handler(newValue, oldValue) {
        console.log(newValue)
        //父组件updateTime对象改变会触发此函数
        this.fileOrderNum = newValue;
        //this.getFilesTabeData()
      },
      deep: true

    },
    rowtyp: {
      handler(newValue, oldValue) {
        //父组件updateTime对象改变会触发此函数
        this.rowtyp1 = newValue;
      },
      deep: true

    },
  },
  created() {

  },
  mounted() {
    // this.queryDateInit()
    // this.queryData()


  },
  computed: {
    //  editor() {
    //       return this.$refs.myTextEditor.quillEditor;
    //     }

  },
  methods: {
    

    handleChange(file, fileList) {//附件上传

    },
    myUpload(file) {
      //  console.log(file)
      let filSiz = file.file;
      filSiz = filSiz.size;
      let filnum = parseFloat(filSiz / 1024 / 1024);
      if (filnum > 30) {
        this.$message({
          message: '请上传小于30M的文件！！！',
          showClose: true,
          type: 'error',
          duration: 2000
        });
        this.$refs.upload1.clearFiles();
        return;
      }
      if (!this.fileOrderNum) {
        this.$message({
          message: '请先获取附件ID',
          showClose: true,
          type: 'error',
          duration: 2000
        });

        this.$refs.upload1.clearFiles();
        return;
      }

      let fd = new FormData();
      fd.append('file', file.file);//传文件
      fd.append('noticeId', this.fileOrderNum)
      //1.上传文件
      uploadFile(fd).then(res => {
        console.log(res)
        if (res.code == 200) {
          this.getFilesTabeData()
        } else {
          this.$message({
            title: '失败',
            message: '保存附件失败，失败原因：' + res.msg,
            showClose: true,
            type: 'error',
            duration: 2000
          });

        }

        // if(JSON.parse(JSON.stringify(response)).result === '2'){
        //   this.$message({
        //     title: '失败',
        //     message: '保存附件失败，失败原因：'+JSON.parse(JSON.stringify(response)).msg,
        //     showClose: true,
        //     type: 'error',
        //     duration: 2000
        //   });

        //   this.$refs.upload1.clearFiles();
        //   return;
        // }
        // //2.获取文件fileId
        // let fileId = JSON.parse(JSON.stringify(response)).fileId;

        // //3.保存附件到业务表(tm_order_add)
        // let saveObj = {
        //   relaId: this.fileOrderNum,
        //  // orderNum: this.orderNum.orderNum,
        //   attrId:'tfjzlc_001',
        //   attrValue: fileId,
        //   remark: this.listQuery.createUserId
        // };
        // saveOrderAddFile(saveObj)
        //   .then(response => {
        //     if (response.flag === "1") {
        //       this.$message({
        //         title: '成功',
        //         message: '保存附件成功',
        //         showClose: true,
        //         type: 'success',
        //         duration: 2000
        //       });
        //       this.$refs.upload1.clearFiles();
        //       this.getFilesTabeData();

        //     } else if (response.flag === "0") {
        //       this.$message({
        //         title: '错误',
        //         message: '保存附件失败...',
        //         showClose: true,
        //         type: 'error',
        //         duration: 2000
        //       });

        //       this.$refs.upload1.clearFiles();
        //     }
        //   });
      });
    },
    handledownFile(obj) {
      let that = this;
      let data = { fileId: obj.file_id }
      let lename = obj.file_name
      let filName = lename.substring(lename.length - 3);
      if (filName == 'jpg' || filName == 'png' || filName == 'peg') {//预览图片
        //  console.log(123456)
        queryImage(data).then(response => {
          let filpath = response.encode;
          if (response.flag) {
            let str1 = 'data:image/jpeg;base64,' + filpath;
            that.imgsrc = str1;
            that.dialogEditVisible = true;
          } else {
            this.$message({
              message: '图片获取失败',
              showClose: true,
              type: 'error',
              duration: 2500
            });
          }


        }).catch((error) => {
          console.log(error)
        })

      } else {
        downFile(data).then(response => {
          // console.log(302011, response);
          var blob = new Blob([response], { type: "application/octet-stream" });
          //  console.log(302013, blob);
          if (window.navigator.msSaveOrOpenBlob) {//msSaveOrOpenBlob方法返回bool值
            navigator.msSaveBlob(blob, obj.file_name);//本地保存
          } else {
            var link = document.createElement('a');//a标签下载
            link.href = window.URL.createObjectURL(blob);
            link.download = obj.file_name;
            link.click();
            window.URL.revokeObjectURL(link.href);
          }
        }).catch((error) => {
          console.log(error)
        });
      }
    },
    handleDelFile(obj) {
      let that = this;
      let data = {
        noticeId: that.fileOrderNum,
        fileId: obj.file_id,
      };
      deleteFile(data).then(response => {
        if (response.code === 200) {
          this.$message({
            title: '成功',
            message: '删除附件成功',
            showClose: true,
            type: 'success',
            duration: 2000
          });
          this.getFilesTabeData();
        } else {
          this.$message({
            title: '错误',
            message: res.msg,
            showClose: true,
            type: 'error',
            duration: 2000
          });
        }
      });
    },
    getFilesTabeData() {//查询附件
      //let orde = this.orderNum.split(',') || [];
      // console.log(12345)
      let that = this;
      let obj = { noticeId: that.fileOrderNum }
      getFilesTabObj(obj).then(res => {
        if (res.code == 200) {
          this.tableData = res.data
        }
        // let json = JSON.parse(JSON.stringify(response));
        // this.tableData = json.selectFilesTableData;
      });
    },

  }
}
</script>

<style lang="scss" scoped>
.diagimg {
  width: 100%;
  height: 600px;
  overflow: auto;
  // img{
  //   // width:100%;
  //   // height:100%;
  // }
}
</style>
<style lang="scss">
// @import "src/styles/common.scss";</style>