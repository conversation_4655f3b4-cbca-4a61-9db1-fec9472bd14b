<template>
    <div>
        <div class="app-container" v-if="!isShowDetail">
            <!-- <div class="title">工单审批工作台</div> -->
            <el-form :inline="true" :model="workQuery" size="small" class="demo-form-inline">
                <el-row>
                    <el-col :span="24">
                        <el-form-item label="工单编号">
                            <el-input v-model="workQuery.orderNum" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="申请人">
                            <el-input v-model="workQuery.applyUser" clearable></el-input>
                        </el-form-item>
                        <el-form-item label="工单类型" >
                            <el-select v-model="workQuery.orderType" clearable>
                                <el-option v-for="item in typeList" :key="item.type" :label="item.value"
                                    :value="item.type"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="流程名称" style="margin: 0 24px">
                            <el-select v-model="workQuery.flowId" filterable clearable @change="flowChange">
                                <el-option v-for="(item) in flowtableData" :key="item.flowId" :label="`${item.cityName}-${item.name}`"
                                    :value="item.flowId"></el-option>
                            </el-select>
                        </el-form-item>
                        <el-form-item label="环节名称">
                            <el-select v-model="workQuery.nodeName" filterable clearable>
                                <el-option v-for="(item, index) in processOption" :key="index" :label="item.name"
                                    :value="item.name"></el-option>
                            </el-select>
                        </el-form-item>
                    </el-col>

                    <el-col :span="24">
                        <el-form-item label="时间">
                            <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="workQuery.date"
                                type="datetimerange" range-separator="至" start-placeholder="开始日期"
                                end-placeholder="结束日期">
                            </el-date-picker>
                        </el-form-item>
                        <el-form-item style="margin: 0 24px">
                            <el-button @click="reset('workQuery')">重置</el-button>
                            <el-button type="primary" @click="queryData(1,10)">查询</el-button>
                            <el-button v-if="warking_btn_export" type="primary" :loading="exportloading"
                                @click="exportData">导出</el-button>
                        </el-form-item>
                    </el-col>

                </el-row>
            </el-form>
            <el-table :data="tableData" v-loading="tabLoding" size="small">
                <el-table-column prop="orderNum" align="center" label="工单编号" width="200"></el-table-column>
                <el-table-column prop="startDate" align="center" label="工单申请时间"></el-table-column>
                <el-table-column prop="flowName" align="center" show-overflow-tooltip label="流程名称"></el-table-column>
                <el-table-column prop="nodeName" align="center" label="环节名称"></el-table-column>
                <el-table-column prop="handleUser" align="center" label="环节处理人"></el-table-column>
                <el-table-column prop="applyUser" align="center" label="申请人" width="240"></el-table-column>
                <el-table-column align="center" label="操作" width="100">
                    <template slot-scope="scope">
                        <el-button v-if="warking_btn_view" @click="handleClick(scope.row)" type="text"
                            size="small">详情</el-button>
                    </template>
                </el-table-column>
            </el-table>
            <div class="pag">
                <el-pagination @size-change="handleSizeChange" background @current-change="handleCurrentChange"
                    :current-page="currentPage" :page-sizes="[10, 20, 50]" :page-size="pageSize"
                    layout="total, sizes, prev, pager, next, jumper" :total="total">
                </el-pagination>
            </div>
        </div>
        <div v-else class="detialWrap">
            <!-- <div class="title"></div> -->
            <el-page-header content="工单详情" @back="goback"></el-page-header>
            <div class="title">审批详情</div>
            <div class="step-wrap">
                <el-steps :space="200" finish-status="success">
                    <el-step v-for="(item, index) in detail.transferInfos"
                        :status="[0, 2].includes(item.handleStatus) ? 'success' : item.handleStatus == 3 ? 'finish' : 'wait'"
                        :key="index" :title="item.handlerUser || '-'">
                        <template slot="description">
                            <div> {{ item.handleStatus == 1 ? '' : item.handleTime }}</div>
                            <div v-if="item.handleStatus == 1">待审批</div>
                            <div> {{ item.transferNodeName || '-' }}</div>
                        </template>
                    </el-step>
                </el-steps>
            </div>

            <div class="title">流程详情</div>
            <el-descriptions style="width: 100%;" :column="7" direction="vertical" border>
                <el-descriptions-item label="工单编号">{{ detail.orderNum }}</el-descriptions-item>
                <el-descriptions-item label="申请人">{{ detail.applyUser }}</el-descriptions-item>
                <el-descriptions-item label="申请时间">{{ detail.startDate }}</el-descriptions-item>
                <el-descriptions-item label="流程名称">{{ detail.flowName }}</el-descriptions-item>
                <el-descriptions-item label="当前环节名称">{{ detail.nodeName }}</el-descriptions-item>
                <el-descriptions-item label="下一环节名称">{{ detail.nextNodeName }}</el-descriptions-item>
                <el-descriptions-item label="下一环节审批人">{{ detail.nextHandlerUsers }}</el-descriptions-item>
            </el-descriptions>

            <template v-if="['1', '2', '3', '4', '5', '6'].includes(detail.orderType)">
                <div class="title">
                    <span v-if="!detail.beforeJson">批量配置详情</span>
                    <span v-else>{{ ['2', '4', '6'].includes(detail.afterJson.type) ? '配置修改详情' : '操作详情' }}</span>
                </div>
                <div style="display: flex;flex-direction: column;">

                    <!-- 批量配置 -->
                    <template v-if="!detail.beforeJson">

                        <div style="width: 100%">

                            <div style="display: flex; margin: 12px 0;"
                                v-if="'cpuUsage' in detail.afterJson.signalList[0]">
                                <el-button style="margin-right: 12px;" type="primary" size="small"
                                    @click="handleClick(currentRow)" :loading="refreshLoading">刷新</el-button>
                                <el-button size="small" @click="fsuResetBatch">批量重启</el-button>
                            </div>

                            <el-table :data="detail.afterJson.signalList" @selection-change="selectFsuFn"
                                header-row-class-name="myHeaderClass" size="small" border>
                                <el-table-column v-if="'cpuUsage' in detail.afterJson.signalList[0]"
                                    :selectable="selectableFn" type="selection" width="55"></el-table-column>

                                <el-table-column prop="cityName" align="center" label="地市"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="areaName" align="center" label="区域"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="siteName" align="center" label="局站"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="roomName" align="center" label="机房"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="devTypeName" align="center" label="设备类型"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="devName" align="center" label="设备名称"
                                    show-overflow-tooltip></el-table-column>
                                <el-table-column prop="label" align="center" label="测点名称"
                                    show-overflow-tooltip></el-table-column>

                                <template v-if="'cpuUsage' in detail.afterJson.signalList[0]">
                                    <el-table-column prop="suId" align="center" label="FSU-MAC"
                                        show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="suIp" align="center" label="FSU-IP"
                                        show-overflow-tooltip></el-table-column>
                                    <!-- <el-table-column prop="cpuUsage" align="center" label="CPU使用率"
                                    :formatter="cpuformatter"></el-table-column>
                                <el-table-column prop="memUsage" align="center" label="内存利用率" :formatter="memformatter"
                                    show-overflow-tooltip></el-table-column> -->
                                    <el-table-column prop="registTime" align="center" label="最近注册时间"
                                        :formatter="registTimeformatter" show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="resetTime" align="center" label="最近重启时间"
                                        :formatter="resetTimeformatter" show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="resetSts" align="center" label="重启状态"
                                        :formatter="resetStsformatter" show-overflow-tooltip></el-table-column>
                                </template>
                                <template v-if="'HLimit' in detail.afterJson.signalList[0]">
                                    <el-table-column prop="HLimit" align="center" label="上限"
                                        show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="SHLimit" align="center" label="过高上限"
                                        show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="LLimit" align="center" label="下限"
                                        show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="SLLimit" align="center" label="过低下限"
                                        show-overflow-tooltip></el-table-column>
                                    <!-- <el-table-column prop="SetValue" align="center" label="设置值"
                                        show-overflow-tooltip></el-table-column> -->
                                </template>
                                <template v-if="'cpuUsage' in detail.afterJson.signalList[0]">
                                    <el-table-column prop="" label="操作" width="100">
                                        <template slot-scope="scope">
                                            <div class="ad">
                                                <!-- 操作日志 -->
                                                <el-tooltip effect="dark" content="操作日志" placement="top">
                                                    <i @click="operateHistory(scope.row)" class="el-icon-tickets"
                                                        style="font-size: 18px;"></i>
                                                </el-tooltip>
                                                <!--重启设备-->
                                                <el-tooltip v-if="scope.row.resetSts !== '1'" effect="dark"
                                                    content="重启设备" placement="top" :disabled="disabledReset">
                                                    <i @click="restartDialog(scope.row, '确认重启FSU设备吗？')"
                                                        :class="['el-icon-refresh']" style="font-size: 18px;"></i>
                                                </el-tooltip>
                                            </div>
                                        </template>
                                    </el-table-column>
                                </template>
                                <template v-if="'alarmLevel' in detail.afterJson.signalList[0]">
                                    <el-table-column prop="alarmLevel" align="center" label="告警级别"
                                        show-overflow-tooltip>
                                        <template slot-scope="scope">
                                            <div>{{ warnLevelOptions.find(s => s.level == scope.row.alarmLevel)?.name }}
                                            </div>
                                        </template>
                                    </el-table-column>
                                </template>
                            </el-table>
                        </div>
                    </template>

                    <!-- 单站配置 -->
                    <template v-if="['2', '4', '6'].includes(detail.afterJson.type)">

                        <div class="mycard">
                            <div class="cardTitle">修改前内容</div>
                            <el-descriptions style="padding: 16px 16px 0;" direction="horizontal" :column="6">
                                <el-descriptions-item label="地市">{{ detail.beforeJson.cityName }}</el-descriptions-item>
                                <el-descriptions-item label="区县">{{ detail.beforeJson.areaName }}</el-descriptions-item>
                                <el-descriptions-item label="局站">{{ detail.beforeJson.siteName }}</el-descriptions-item>
                                <el-descriptions-item label="机房">{{ detail.beforeJson.roomName }}</el-descriptions-item>
                                <el-descriptions-item label="设备类型">{{ detail.beforeJson.devName
                                    }}</el-descriptions-item>
                                <el-descriptions-item label="设备名称">{{ detail.beforeJson.devName
                                    }}</el-descriptions-item>
                                <el-descriptions-item label="测点名称">{{ detail.beforeJson.signalName
                                    }}</el-descriptions-item>
                                <el-descriptions-item v-if="['2', '4'].includes(detail.beforeJson.type)" label="告警级别">{{
                                    warnLevelOptions.find(s => s.level == detail.beforeJson.alarmLevel)?.name ||
                                    detail.beforeJson.alarmLevel }}</el-descriptions-item>
                                <template v-if="['6'].includes(detail.beforeJson.type)">
                                    <el-descriptions-item label="上限">{{ detail.beforeJson.HLimit
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="下限">{{ detail.beforeJson.LLimit
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="过高上限">{{ detail.beforeJson.SHLimit
                                        }}</el-descriptions-item>
                                    <el-descriptions-item label="过低下限">{{ detail.beforeJson.SLLimit
                                        }}</el-descriptions-item>
                                    <!-- <el-descriptions-item label="设置值">{{ detail.beforeJson.SetValue
                                        }}</el-descriptions-item> -->
                                </template>

                            </el-descriptions>
                        </div>
                        <div class="myArrow">
                            <img :src="arrow" alt="">
                        </div>
                        <div class="mycard">
                            <div class="cardTitle">修改后内容</div>
                            <el-descriptions style="padding: 16px 16px 0;" direction="horizontal" :column="6">
                                <el-descriptions-item label="地市">{{ detail.afterJson.cityName }}</el-descriptions-item>
                                <el-descriptions-item label="区县">{{ detail.afterJson.areaName }}</el-descriptions-item>
                                <el-descriptions-item label="局站">{{ detail.afterJson.siteName }}</el-descriptions-item>
                                <el-descriptions-item label="机房">{{ detail.afterJson.roomName }}</el-descriptions-item>
                                <el-descriptions-item label="设备类型">{{ detail.afterJson.devName }}</el-descriptions-item>
                                <el-descriptions-item label="设备名称">{{ detail.afterJson.devName }}</el-descriptions-item>
                                <el-descriptions-item label="测点名称">{{ detail.afterJson.signalName
                                    }}</el-descriptions-item>
                                <el-descriptions-item v-if="['2', '4'].includes(detail.afterJson.type)"
                                    :contentClassName="detail.afterJson.alarmLevel !== detail.beforeJson.alarmLevel ? 'my-content' : ''"
                                    label="告警级别">
                                    {{ warnLevelOptions.find(s => s.level == detail.afterJson.alarmLevel)?.name ||
                                        detail.afterJson.alarmLevel
                                    }}
                                    <el-tag v-if="detail.afterJson.alarmLevel !== detail.beforeJson.alarmLevel"
                                        type="danger" size="small">变动</el-tag>
                                </el-descriptions-item>
                                <template v-if="['6'].includes(detail.afterJson.type)">
                                    <el-descriptions-item label="上限"
                                        :contentClassName="detail.afterJson.HLimit !== detail.beforeJson.HLimit ? 'my-content' : ''">
                                        {{ detail.afterJson.HLimit }}
                                        <el-tag v-if="detail.afterJson.HLimit !== detail.beforeJson.HLimit"
                                            type="danger" size="small">变动</el-tag>
                                    </el-descriptions-item>
                                    <el-descriptions-item label="下限"
                                        :contentClassName="detail.afterJson.LLimit !== detail.beforeJson.LLimit ? 'my-content' : ''">
                                        {{ detail.afterJson.LLimit }}
                                        <el-tag v-if="detail.afterJson.LLimit !== detail.beforeJson.LLimit"
                                            type="danger" size="small">变动</el-tag>
                                    </el-descriptions-item>
                                    <el-descriptions-item label="过高上限"
                                        :contentClassName="detail.afterJson.SHLimit !== detail.beforeJson.SHLimit ? 'my-content' : ''">
                                        {{ detail.afterJson.SHLimit }}
                                        <el-tag v-if="detail.afterJson.SHLimit !== detail.beforeJson.SHLimit"
                                            type="danger" size="small">变动</el-tag>
                                    </el-descriptions-item>
                                    <el-descriptions-item label="过低下限"
                                        :contentClassName="detail.afterJson.SLLimit !== detail.beforeJson.SLLimit ? 'my-content' : ''">
                                        {{ detail.afterJson.SLLimit }}
                                        <el-tag v-if="detail.afterJson.SLLimit !== detail.beforeJson.SLLimit"
                                            type="danger" size="small">变动</el-tag>
                                    </el-descriptions-item>
                                    <!-- <el-descriptions-item label="设置值"
                                        :contentClassName="detail.afterJson.SetValue !== detail.beforeJson.SetValue ? 'my-content' : ''">
                                        {{ detail.afterJson.SetValue }}
                                        <el-tag v-if="detail.afterJson.SetValue !== detail.beforeJson.SetValue"
                                            type="danger" size="small">变动</el-tag>
                                    </el-descriptions-item> -->
                                </template>
                            </el-descriptions>
                        </div>
                    </template>
                    <!-- 开关控制 -->
                    <template v-if="['5'].includes(detail.afterJson.type)">
                        <el-tag type="danger" style="margin-bottom: 24px;">{{ detail.beforeJson.signalName }}</el-tag>
                    </template>

                </div>
                <template v-if="detail.fsuInfo && detail.beforeJson">
                    <div class="title" style="margin: 12px 0; ">
                        <span>fsu详情（此模块可重启fsu）</span>
                    </div>
                    <div v-if="detail.fsuInfo.length == 1" style="font-size: 14px; color: rgba(0,0,0,.45);">FSU主要信息：{{
                        detail.fsuInfo[0].roomInfo }}</div>
                    <el-button style="margin: 12px 0;" type="primary" size="small" @click="handleClick(currentRow)"
                        :loading="refreshLoading">刷新</el-button>

                    <el-table :data="detail.fsuInfo" header-row-class-name="myHeaderClass" border size="small">

                        <el-table-column prop="suId" align="center" label="mac地址"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="cpuUsage" align="center" label="CPU使用率"></el-table-column>
                        <el-table-column prop="memUsage" align="center" label="内存利用率"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="registTime" align="center" label="最近注册时间"></el-table-column>
                        <el-table-column prop="resetTime" align="center" label="最近重启时间"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="resetSts" align="center" label="状态"
                            show-overflow-tooltip></el-table-column>

                        <el-table-column prop="" label="操作" width="100">
                            <template slot-scope="scope">
                                <div class="ad">
                                    <!-- 操作日志 -->
                                    <el-tooltip effect="dark" content="操作日志" placement="top">
                                        <i @click="operateHistory(scope.row)" class="el-icon-tickets"
                                            style="font-size: 18px;"></i>
                                    </el-tooltip>
                                    <!--重启设备-->
                                    <el-tooltip v-if="scope.row.resetSts == '未重启'" effect="dark" content="重启设备"
                                        placement="top" :disabled="disabledReset">
                                        <i @click="restartDialog(scope.row, '确认重启FSU设备吗？')"
                                            :class="['el-icon-refresh', disabledReset ? 'disabledReset' : '']"
                                            style="font-size: 18px;"></i>
                                    </el-tooltip>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </template>
            </template>

            <!-- 操作日志 -->
            <el-dialog :title="dialogTitle" :visible.sync="dialogTableVisible">
                <el-table size='small' :data="oprTableData" :height="250">
                    <el-table-column property="userName" label="操作人"></el-table-column>
                    <el-table-column property="oper" label="操作动作">
                        <template slot-scope="scope">
                            <div>重启FSU</div>
                        </template>
                    </el-table-column>
                    <el-table-column property="operStatus" label="操作状态">
                        <template slot-scope="scope">
                            <div v-if="scope.row.operStatus == 'SUCCESS'" style="color: green;">成功</div>
                            <div v-if="scope.row.operStatus == 'FAILURE'" style="color: red;">失败</div>
                        </template>
                    </el-table-column>
                    <el-table-column property="operateTime" label="操作时间"></el-table-column>
                </el-table>
                <div class="pag">
                    <el-pagination @size-change="oprhandleSizeChange" background
                        @current-change="oprhandleCurrentChange" :current-page="oprcurrentPage"
                        :page-sizes="[10, 20, 50]" :page-size="oprpageSize"
                        layout="total, sizes, prev, pager, next, jumper" :total="oprtotal">
                    </el-pagination>
                </div>
            </el-dialog>

            <template v-if="['7'].includes(detail.orderType)">
                <div class="title" style="display: flex;flex-direction: row;justify-content: space-between;">
                    <span>FSU核查详情</span>
                    <div style="display: flex;">
                        <el-button type="primary" size="small" @click="updateStsBatchFsuAndDevice(3, '确定进行批量【退网】操作吗？')"
                            :disabled="!fsuCheckedList.length">批量确认已退网</el-button>
                        <el-button type="primary" size="small" @click="updateStsBatchFsuAndDevice(1, '确定进行批量【未退网】操作吗？')"
                            :disabled="!fsuCheckedList.length">批量确认未退网</el-button>
                    </div>

                </div>
                <div>

                    <el-table :data="deviceCheckedList" @selection-change="selectFsuUpdateFn"
                        header-row-class-name="myHeaderClass" size="small" border>
                        <el-table-column type="expand">
                            <template slot-scope="scope">
                                <div style="font-size: 14px;font-weight: bold;margin-left: 12px;">关联设备:</div>
                                <el-table :data="scope.row.deviceList" style="margin: 12px auto;"
                                    header-row-class-name="myHeaderClass" size="small" border height="300">
                                    <el-table-column prop="deviceName" align="center" label="设备名称"
                                    show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="deviceType" align="center" label="设备类型"
                                    show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="deviceVendor" align="center" label="设备厂家"
                                        show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="suId" align="center" label="FSU-MAC"
                                        show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="suIp" align="center" label="FSU-IP"
                                        show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="suVendor" align="center" label="SU厂家"
                                        show-overflow-tooltip></el-table-column>
                                    <el-table-column prop="status" align="center" label="状态" show-overflow-tooltip>
                                        <template slot-scope="scope">
                                            {{ deviceState.find(v => v.type == scope.row.status)?.name ||
                                            scope.row.status }}
                                        </template>
                                    </el-table-column>
                                    <el-table-column v-if="['8'].includes(detail.orderType)" prop="registTime"
                                        align="center" label="注册时间" :formatter="registTimeformatter"
                                        show-overflow-tooltip></el-table-column>
                                    <el-table-column v-if="['9'].includes(detail.orderType)" prop="statTime"
                                        align="center" label="核查时间" :formatter="registTimeformatter"
                                        show-overflow-tooltip></el-table-column>

                                </el-table>
                            </template>
                        </el-table-column>
                        <el-table-column :selectable="selecFsutableFn" type="selection" width="55"></el-table-column>

                        <el-table-column type="index" width="50" label="序号">
                        </el-table-column>
                        <el-table-column prop="cityName" align="center" label="地市"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="areaName" align="center" label="区域"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="siteName" align="center" label="局站"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="roomName" align="center" label="机房"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="suId" align="center" label="FSU-MAC"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="suIp" align="center" label="FSU-IP"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="suVendor" align="center" label="FSU厂家"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="status" align="center" label="状态" show-overflow-tooltip>
                            <template slot-scope="scope">
                                {{ deviceState.find(v => v.type == scope.row.status)?.name || scope.row.status }}
                            </template>
                        </el-table-column>

                        <el-table-column prop="registTime" align="center" label="注册时间" :formatter="registTimeformatter"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="firstCheckTime" align="center" label="首次核查时间"
                            :formatter="registTimeformatter" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="checkTime" align="center" label="最后核查时间" :formatter="registTimeformatter"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="" label="操作" width="200">
                            <template slot-scope="scope">
                                <div class="ad">
                                    <el-tooltip v-if="scope.row.status == '2'" effect="dark" content="确定已退网"
                                        placement="top" :disabled="disabledReset">
                                        <el-button type="text"
                                            @click="updateFsuAndDeviceSts(scope.row, 3, '确定进行【退网】操作吗？')">确定已退网</el-button>
                                    </el-tooltip>
                                    <el-tooltip v-if="scope.row.status == '2'" effect="dark" content="确定未退网"
                                        placement="top" :disabled="disabledReset">
                                        <el-button type="text"
                                            @click="updateFsuAndDeviceSts(scope.row, 1, '确定进行【未退网】操作吗？')">确定未退网</el-button>
                                    </el-tooltip>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>
            </template>

            <template v-if="['8', '9'].includes(detail.orderType)">
                <div class="title" style="display: flex;flex-direction: row;justify-content: space-between;">
                    <span>设备核查详情</span>
                    <div style="display: flex;" v-if="detail.orderType == '8'">
                        <el-button type="primary" size="small" @click="updateStsBatchFsuAndDevice(3, '确定进行批量【退网】操作吗？')"
                            :disabled="!fsuCheckedList.length">批量确认已退网</el-button>
                        <el-button type="primary" size="small" @click="updateStsBatchFsuAndDevice(1, '确定进行批量【未退网】操作吗？')"
                            :disabled="!fsuCheckedList.length">批量确认未退网</el-button>
                    </div>
                </div>
                <div>
                    <el-table :data="deviceCheckedList" @selection-change="selectFsuUpdateFn"
                        header-row-class-name="myHeaderClass" size="small" border height="500">
                        <el-table-column v-if="detail.orderType == '8'" :selectable="selecFsutableFn" type="selection"
                            width="55"></el-table-column>
                        <el-table-column type="index" width="50" label="序号">
                        </el-table-column>
                        <el-table-column prop="cityName" align="center" label="地市"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="areaName" align="center" label="区域"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="siteName" align="center" label="局站"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="roomName" align="center" label="机房"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="suId" align="center" label="FSU-MAC"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="suIp" align="center" label="FSU-IP"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="suVendor" align="center" label="SU厂家"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="deviceType" align="center" label="设备类型"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="deviceName" align="center" label="设备名称"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="deviceVendor" align="center" label="设备厂家"
                            show-overflow-tooltip></el-table-column>
                        <el-table-column prop="status" align="center" label="状态" show-overflow-tooltip>
                            <template slot-scope="scope">
                                {{ deviceState.find(v => v.type == scope.row.status)?.name || scope.row.status }}
                            </template>
                        </el-table-column>
                        <el-table-column v-if="['8'].includes(detail.orderType)" prop="registTime" align="center"
                            label="注册时间" :formatter="registTimeformatter" show-overflow-tooltip></el-table-column>
                        <el-table-column v-if="['9'].includes(detail.orderType)" prop="statTime" align="center"
                            label="核查时间" :formatter="registTimeformatter" show-overflow-tooltip></el-table-column>
                        <el-table-column prop="" label="操作" width="200" v-if="detail.orderType == '8'">
                            <template slot-scope="scope">
                                <div class="ad" >
                                    <el-tooltip v-if="scope.row.status == '2'" effect="dark" content="确定已退网"
                                        placement="top" :disabled="disabledReset">
                                        <el-button type="text"
                                            @click="updateFsuAndDeviceSts(scope.row, 3, '确定进行【退网】操作吗？')">确定已退网</el-button>
                                    </el-tooltip>
                                    <el-tooltip v-if="scope.row.status == '2'" effect="dark" content="确定未退网"
                                        placement="top" :disabled="disabledReset">
                                        <el-button type="text"
                                            @click="updateFsuAndDeviceSts(scope.row, 1, '确定进行【未退网】操作吗？')">确定未退网</el-button>
                                    </el-tooltip>
                                </div>
                            </template>
                        </el-table-column>
                    </el-table>
                </div>

            </template>


            <div class="btns">
                <el-button size="small" v-if="warking_btn_pass" type="primary" @click="pass">通过</el-button>
                <el-button size="small" v-if="warking_btn_revoke" type="primary" @click="revoke">废弃</el-button>
                <el-button size="small" @click="goback">取消</el-button>
            </div>
        </div>
    </div>
</template>

<script>
import exportTableToExcel from './components/exportExcel.js'
import { mapGetters } from 'vuex';
export default {
    data() {
        return {
            typeList: [
                // { type: '0', value: '普通' },
                // { type: '1', value: '核心' },
                // { type: '3', value: '批量告警' },
                // { type: '4', value: '批量阈值' },
                { type: '1', value: '普通单站阈值' },
                { type: '2', value: '普通单站告警' },
                { type: '3', value: '批量阈值' },
                { type: '4', value: '批量告警' },
                { type: '5', value: '核心单站阈值' },
                { type: '6', value: '核心单站告警' },
                { type: '7', value: 'FSU核查流程' },
                { type: '8', value: '设备资源核查' },
                { type: '9', value: '未注册资源核查' },

            ],
            // 1：正常，2：疑似退网，3：已退网
            deviceState: [
                { type: 1, name: '正常' },
                { type: 2, name: '疑似退网' },
                { type: 3, name: '已退网' },
                { type: 4, name: '疑似退网' },
            ],
            warnLevelOptions: [
                { level: 1, name: '一级告警' },
                { level: 2, name: '二级告警' },
                { level: 3, name: '三级告警' },
                { level: 4, name: '四级告警' },
                { level: 5, name: '五级告警' },
                { level: 6, name: '六级告警' },
            ],
            oper: '',
            tabLoding: false,
            exportloading: false,
            arrow: require('./img/arrow.svg'),
            isShowDetail: false,
            workQuery: {
                orderNum: '',
                orderType: "",
                applyUser: '', // 申请人
                flowName: '', // 流程名称
                flowId: '',
                nodeName: '', // 环节名称
                date: []
            },
            tableData: [],
            pageSize: 10,
            currentPage: 1,
            total: 0,
            currentRow: null,
            detail: {},
            warking_btn_export: false,
            warking_btn_view: false,
            warking_btn_pass: false,
            warking_btn_revoke: false,

            flowtableData: [],
            processOption: [],

            fsumsg: '',
            refreshLoading: false,

            // 操作日志
            dialogTitle: '',
            oprTableData: [],
            oprpageSize: 10,
            oprcurrentPage: 1,
            oprtotal: 0,
            dialogTableVisible: false,

            // FSU重启
            disabledReset: false,
            fsuCurrentRow: null,
            selectFsuList: [],

            // //FSU核查详情
            fsuCheckedList: [],

            //设备核查详情
            selectdeviceCheckedList: [],
            deviceCheckedList: []
        }
    },
    created() {
        this.queryData()
        this.queryFlowData()
        this.warking_btn_export = this.elements['warking:btn_export']
        this.warking_btn_view = this.elements['warking:btn_view']
        this.warking_btn_pass = this.elements['warking:btn_pass']
        this.warking_btn_revoke = this.elements['warking:btn_revoke']
    },
    computed: {
        ...mapGetters([
            'elements'
        ])
    },
    methods: {
        selecFsutableFn(row) {
            if (!'status' in row || !row.status || [2].includes(row.status)) {
                return true
            } else {
                return false
            }
        },
        selectableFn(row) {
            if (!'resetSts' in row || !row.resetSts || row.resetSts == '0') {
                return true
            } else {
                return false
            }
        },
        // 批量选择要更新的Fsu
        selectFsuUpdateFn(val) {
            this.fsuCheckedList = val
        },
        // 批量更新状态
        updateStsBatchFsuAndDevice(type, title) {
            if (!this.fsuCheckedList.length) return this.$message.error('请选择数据')
            this.$confirm(title, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {

                if (this.detail.orderType == '7') {
                    let params = {
                        status: type,
                        suId: JSON.stringify(this.fsuCheckedList.map(v => v.suId))
                    }
                    this.$api.updateFsuStsBatch(params).then(res => {
                        if (res.code == 200) {
                            this.handleClick(this.currentRow)
                            this.$message.success('操作成功')
                        } else if (res.code == 500) {
                            this.$message.error(res.msg)
                        }
                    })
                }
                if (this.detail.orderType == '8') {
                    let arr = this.fsuCheckedList.map(v => {
                        let obj = {
                            suId: v.suId,
                            devId: v.devId
                        }
                        return obj
                    })
                    let params = {
                        status: type,
                        devList: JSON.stringify(arr)
                    }
                    this.$api.updateDeviceStsBatch(params).then(res => {
                        if (res.code == 200) {
                            this.handleClick(this.currentRow)
                            this.$message.success('操作成功')
                        } else if (res.code == 500) {
                            this.$message.error(res.msg)
                        }
                    })
                }

            }).catch(() => {

            });

        },

        // 批量重启
        fsuResetBatch() {
            if (!this.selectFsuList.length) return this.$message.error('请选择FSU数据')
            if (this.selectFsuList.length > 5) return this.$message.error('批量重启最多不超过5个FSU')
            let params = {
                orderNum: this.currentRow.orderNum,
                suId: JSON.stringify([...new Set(this.selectFsuList.map(v => v.suId))])
            }
            this.$api.fsuResetBatch(params).then(res => {
                if (res.code == 200) {
                    this.handleClick(this.currentRow)
                    this.$message.success('操作成功')
                } else if (res.code == 500) {
                    this.$message.error(res.msg)
                }
            })
        },
        resetStsformatter(row) {
            if (row.resetSts == '1') {
                return '已重启'
            } else {
                return '未重启'
            }
        },
        selectFsuFn(val) {
            this.selectFsuList = val
        },

        cpuformatter(row) {
            if (row.cpuUsage) {
                return row.cpuUsage + '%'
            }
        },
        memformatter(row) {
            if (row.memUsage) {
                return row.memUsage + '%'
            }
        },
        registTimeformatter(row) {
            if (row.registTime) {
                return row.registTime?.replace('T', ' ')
            }
            if (row.firstCheckTime) {
                return row.firstCheckTime?.replace('T', ' ')
            }
            if (row.checkTime) {
                return row.checkTime?.replace('T', ' ')
            }
            if (row.statTime) {
                return row.statTime?.replace('T', ' ')
            }
        },
        resetTimeformatter(row) {
            if (row.resetTime) {
                return row.resetTime?.replace('T', ' ')
            }
        },
        updateFsuAndDeviceSts(row, type, title) {
            this.$confirm(title, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                if (this.detail.orderType == '7') {
                    let params = {
                        status: type,
                        suId: row.suId
                    }
                    this.$api.updateFsuSts(params).then(res => {
                        if (res.code == 200) {
                            this.handleClick(this.currentRow)
                            this.$message.success('操作成功')
                        } else {
                            this.$message.error(res.msg)
                        }
                    })
                }
                if (this.detail.orderType == '8') {
                    let params = {
                        status: type,
                        suId: row.suId,
                        devId: row.devId
                    }
                    this.$api.updateDeviceSts(params).then(res => {
                        if (res.code == 200) {
                            this.handleClick(this.currentRow)
                            this.$message.success('操作成功')
                        } else {
                            this.$message.error(res.msg)
                        }
                    })
                }

            }).catch(() => {

            });
        },
        restartDialog(row, title) {
            this.$confirm(title, '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning'
            }).then(() => {
                this.restartFsu(row)
            }).catch(() => {

            });

        },
        async restartFsu(row) {
            this.fsuCurrentRow = row
            let params = {
                orderNum: this.currentRow.orderNum,
                suId: this.fsuCurrentRow.suId,
            }
            let res = await this.$api.fsuReset(params)

            if (res.code == 200) {
                this.handleClick(this.currentRow)
                this.$message.success('操作成功')
            } else if (res.code == 500) {
                this.$message.error(res.msg)
            }
        },
        // 操作日志
        async operateHistory(row) {
            this.fsuCurrentRow = row
            let params = {
                suId: this.fsuCurrentRow.suId,
                current: this.oprcurrentPage,
                size: this.oprpageSize
            }
            let res = await this.$api.getFsuResetHostory(params)
            if (res.code == 200) {
                this.dialogTitle = `操作日志`
                this.oprTableData = res.data.records.map(v => {
                    v.operateTime = v.operateTime.replace('T', ' ')
                    return v
                })
                this.dialogTableVisible = true
                this.oprtotal = res.data.total

                console.log(this.oprTableData, this.dialogTableVisible)
            } else {
                this.$message.error(res.msg)
            }
        },
        oprhandleSizeChange(val) {
            this.oprpageSize = val
            this.operateHistory(this.fsuCurrentRow)
        },
        oprhandleCurrentChange(val) {
            this.oprcurrentPage = val
            this.operateHistory(this.fsuCurrentRow)
        },

        // 查询流程
        async queryFlowData() {
            let params = {
                pageNum: 1,
                pageSize: 999
            }
            let res = await this.$api.queryFlowList(params)
            if (res.code == 200) {
                this.flowtableData = res.data.records
            } else {
                this.$message.error(res.msg)
            }
        },
        flowChange(flowId) {
            // let flowId = this.flowtableData.find(v => v.name == val)?.flowId
            // if (!flowId) return
            this.queryFlow(flowId)
        },
        // 根据流程id查环节 
        async queryFlow(flowId) {
            let res = await this.$api.queryFlowById({ flowId: flowId })
            if (res.code == 200) {
                this.processOption = res.data.nodeList
            } else {
                this.$message.error(res.msg)
            }
        },
        async exportData() {
            this.exportloading = true
            let data = [
                ['工单编号', '工单申请时间', '流程名称', '环节名称', '环节处理人', '申请人']
            ]
            let params = {
                workStatus: 0,
                pageNum: 1,
                pageSize: this.total
            }
            let res = await this.$api.queryOrderInfo(params)
            if (res.code == 200) {
                res.data.records.forEach(v => {
                    let item = [
                        v.orderNum,
                        v.startDate,
                        v.flowName,
                        v.nodeName,
                        v.handleUser,
                        v.applyUser
                    ]
                    data.push(item)
                })
                exportTableToExcel(data, '待办工单数据')
                this.exportloading = false
            } else {
                this.$message.error(res.msg)
                this.exportloading = false
            }
        },
        async revoke() {
            let params = {
                orderNum: this.currentRow.orderNum,
                handleUser: this.currentRow.handleUser
            }
            let res = await this.$api.orderRevoke(params)
            if (res.code == 200) {
                this.$message.success('已废弃')
                this.goback()
            } else {
                this.$message.error(res.msg)
            }
        },
        async pass() {
            let params = {
                orderNum: this.detail.orderNum,
            };

            // if (['2', '4'].includes(this.detail.afterJson.type)) {
            //     params = {
            //         orderNum: this.detail.orderNum,

            //     }

            // }
            // 设备阈值
            // if (['6'].includes(this.detail.afterJson.type)) {
            //  params = {
            //     orderNum: this.detail.orderNum,
            // handleUser: this.currentRow.handleUser,
            // id: this.detail.afterJson.id,
            // signalCode: this.detail.afterJson.signalCode,
            // HLimit: this.detail.afterJson.HLimit,
            // SHLimit: this.detail.afterJson.SHLimit,
            // LLimit: this.detail.afterJson.LLimit,
            // SLLimit: this.detail.afterJson.SLLimit,
            // SetValue: this.detail.afterJson.SetValue
            //   }

            // }
            // 开关机
            //  if (['5'].includes(this.detail.afterJson.type)) {
            // params = {
            //     orderNum: this.detail.orderNum,
            // handleUser: this.currentRow.handleUser,
            // id: this.detail.afterJson.id,
            // signalCode: this.detail.afterJson.signalCode,
            // HLimit: this.detail.afterJson.HLimit,
            // SHLimit: this.detail.afterJson.SHLimit,
            // LLimit: this.detail.afterJson.LLimit,
            // SLLimit: this.detail.afterJson.SLLimit,
            // SetValue: this.detail.afterJson.SetValue
            // }

            // }
            let res = await this.$api.orderSubmit(params)
            if (res.code == 200) {
                // sts 1 正常 2 流程结束  流程结束需要上报
                // if (res.data.sts == 2) {
                //     let _res;
                //     // 告警级别
                //     if (['2', '4'].includes(this.detail.afterJson.type)) {
                //         let params = {
                //             oderNum: this.detail.oderNum,
                //             id: this.detail.afterJson.id,
                //             signalCode: this.detail.afterJson.signalCode,
                //             alarmLevel: this.detail.afterJson.alarmLevel
                //         }
                //         _res = await this.$api.submitAramLevel(params)
                //     }
                //     // 设备阈值
                //     if (['6'].includes(this.detail.afterJson.type)) {
                //         let params = {
                //             oderNum: this.detail.oderNum,
                //             id: this.detail.afterJson.id,
                //             signalCode: this.detail.afterJson.signalCode,
                //             HLimit: this.detail.afterJson.HLimit,
                //             SHLimit: this.detail.afterJson.SHLimit,
                //             LLimit: this.detail.afterJson.LLimit,
                //             SLLimit: this.detail.afterJson.SLLimit,
                //             SetValue: this.detail.afterJson.SetValue
                //         }
                //         _res = await this.$api.submitAramParameter(params)
                //     }
                //     if (_res.code == 200) {
                //         this.$message.success('流程审批结束')
                //     } else {
                //         // TODO 配置修改失败需要调用挂起接口
                //         this.$message.success('配置修改失败！')
                //     }

                // } else {
                //    this.$message.success('提交成功')

                // }
                this.$message.success('提交成功')
                this.goback()
            } else {
                this.$message.error(res.msg)
            }
        },
        goback() {
            this.isShowDetail = false
            this.currentPage = 1
            this.pageSize = 10
            this.queryData()
        },
        reset() {
            this.workQuery = {
                orderNum: '',
                orderType: '',
                applyUser: '', // 申请人
                flowName: '', // 流程名称
                nodeName: '', // 环节名称
                date: []
            }
            this.currentPage = 1
            this.pageSize = 10
            this.queryData()
        },
        async queryData(pageNum, pageSize) {
            this.tabLoding = true
            if (pageNum) this.currentPage = pageNum
            if (pageSize) this.pageSize = pageSize
            let params = {
                ...this.workQuery,
                workStatus: 0,
                startTime: this.workQuery.date[0] || '',
                endTime: this.workQuery.date[1] || '',
                pageNum: pageNum || this.currentPage,
                pageSize: pageSize || this.pageSize
            }
            let res = await this.$api.queryOrderInfo(params)
            if (res.code == 200) {
                this.tableData = res.data.records
                this.total = res.data.total
            } else {
                this.$message.error(res.msg)
            }
            this.tabLoding = false

        },
        handleSizeChange(val) {
            this.pageSize = val
            this.queryData()
        },
        handleCurrentChange(val) {
            this.currentPage = val
            this.queryData()
        },
        handleClick1(row) {
            this.detail.afterJson.signalList = this.detail.afterJson.signalList.map(v => {
                v.resetSts = '1'
                return v
            })

            console.log(this.detail)
        },
        async handleClick(row) {
            this.refreshLoading = true
            this.currentRow = row
            let params = {
                orderNum: row.orderNum,
                handleUser: row.handleUser
            }

            let res = await this.$api.queryOrderDetail(params)
            if (res.code == 200) {
                res.data.afterJson = JSON.parse(res.data.afterJson)
                res.data.beforeJson = JSON.parse(res.data.beforeJson)
                this.detail = res.data

                if (['7', '8', '9'].includes(res.data.orderType)) {
                    let params = {
                        orderNum: row.orderNum,
                        pageSize: 999,
                        pageNum: 1
                    }
                    let checkRes = await this.$api.orderInfoResource(params)
                    if (checkRes.code == 200) {
                        this.deviceCheckedList = checkRes.data.records
                    } else {
                        this.$message.error(res.msg)
                    }
                }

                if (res.data.beforeJson) { //单站配置
                    if (this.detail.fsuInfo) { //单站阈值配置
                        this.detail.fsuInfo = this.detail.fsuInfo.map(v => {
                            let obj = {
                                registTime: v.registTime.replace('T', ' '),
                                cpuUsage: `${v.cpuUsage}%`,
                                memUsage: `${v.memUsage}%`,
                                resetTime: v?.resetTime?.replace('T', ' '),
                                suId: v.suId,
                                roomInfo: v.roomInfo,
                                resetSts: v.resetSts == 1 ? '已重启' : '未重启'
                            }
                            return obj
                        })
                    }


                } else { // 批量配置

                }


                this.isShowDetail = true
            } else {
                this.$message.error(res.msg)
            }
            this.refreshLoading = false

        }
    }
}
</script>

<style lang="scss" scoped>
.step-wrap {
    padding: 24px 48px;
    background-color: #fff;
    background: #FAFAFA;
    border-radius: 4px;
}

.myArrow {
    margin: 16px 0;
    padding: 2px 16px;
    align-self: center;
    border-radius: 4px;
    border: 1px solid #CECECE;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;

    img {
        width: 18px;
        transform: rotate(90deg);
        opacity: 0.5;

    }
}

.mycard {
    width: 100%;
    background: #FFFFFF;
    border-radius: 4px;
    border: 1px solid #E5E5E5;

    .cardTitle {
        padding-left: 16px;
        height: 44px;
        line-height: 44px;
        background: #FAFAFA;
        font-weight: 400;
        font-size: 14px;
        color: #333333;
    }
}

.title {
    font-size: 14px;
    font-weight: bold;
    margin: 24px 0 18px;
}

.detialWrap {
    padding: 24px 100px;
}

.btns {
    display: flex;
    flex-direction: row;
    margin-top: 12px;
}

.configTitle {
    text-align: center;
    font-size: 14px;
    margin-bottom: 8px;
}
</style>