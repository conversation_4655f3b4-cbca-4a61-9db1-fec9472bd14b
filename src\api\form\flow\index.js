import http from '@/api/http'
import {Message} from 'element-ui'

export function postAction(url, parameter) {
  return http({
    url: url,
    method: 'post',
    data: parameter
  })
}

export function httpAction(url, parameter, method) {
  return http({
    url: url,
    method: method,
    data: parameter
  })
}

export function putAction(url, parameter) {
  return http({
    url: url,
    method: 'put',
    data: parameter
  })
}

export function getAction(url, parameter) {
  return http({
    url: url,
    method: 'get',
    params: parameter
  })
}
