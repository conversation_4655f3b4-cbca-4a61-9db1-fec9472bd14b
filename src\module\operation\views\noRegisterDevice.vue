<template>
  <div class="app-container">

    <el-form :inline="true" :model="queryForm" size="small" label-width="100px" class="demo-form-inline">
      <el-form-item label="地市:">
        <el-select v-model="queryForm.cityId" :disabled="cityDisabled" @change="cityChange" filterable clearable
          placeholder="请选择">
          <el-option v-for="(item, index) in citylist" v-show="index !== 0" :key="item.cityId" :label="item.cityName"
            :value="item.cityId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="区域:">
        <el-select v-model="queryForm.areaId" :disabled="areaDisabled" @change="areaChange" filterable clearable
          placeholder="请选择">
          <el-option v-for="item in areaList" :key="item.areaId" :label="item.areaName"
            :value="item.areaId"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="局站:">
        <el-select v-model="queryForm.siteId" @change="siteChange" :remote-method="getSite" filterable clearable
          placeholder="请选择">
          <el-option v-for="item in siteList" filterable :key="`${item.area_id}-${item.site_id}`"
            :label="item.site_name" :value="item.site_id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="机房:">
        <el-select v-model="queryForm.roomId" @change="roomChange" :remote-method="getRoom" filterable clearable
          placeholder="请选择">
          <el-option v-for="item in roomlist" :key="`${item.room_id}-${item.site_id}`" :label="item.room_name"
            :value="item.room_id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备类型:">
        <el-select v-model="queryForm.deviceType" @change="devTypeChange" :remote-method="getDeviceType" filterable
          remote clearable placeholder="请输入">
          <el-option v-for="(item, index) in deviceTypeList" :key="`${index}-${item.id}`" :label="item.label"
            :value="item.id"></el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="设备名称:">
        <el-select v-model="queryForm.deviceName" filterable remote clearable placeholder="请输入"
          :remote-method="getDeviceName">
          <el-option v-for="(item, index) in devoptions" :key="`${index}-${item.id}-${item.label}`" :label="item.label"
            :value="item.label">
          </el-option>
        </el-select>
      </el-form-item>
      <el-form-item label="SU_ID:">
        <el-input v-model="queryForm.suId" placeholder="请输入" clearable></el-input>
      </el-form-item>
      <el-form-item label="SU_IP:">
        <el-input v-model="queryForm.suIp" placeholder="请输入" clearable></el-input>
      </el-form-item>
      <el-form-item label="">
        <el-button style="margin-left: 60px;" @click="queryFormReset">重置</el-button>
        <el-button type="primary" @click="queryData(1, 10)">查询</el-button>
        <el-button type="primary" @click="devExport" :loading="exportloading">导出</el-button>

      </el-form-item>
    </el-form>
    <el-table :data="deviceTableData" header-row-class-name="myHeaderClass" size="small" v-loading="tableLoading">
      <el-table-column prop="cityName" align="center" label="地市"></el-table-column>
      <el-table-column prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
      <el-table-column prop="siteName" align="center" label="局站" show-overflow-tooltip></el-table-column>
      <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
      <el-table-column prop="suIp" align="center" label="SU_IP" show-overflow-tooltip></el-table-column>
      <el-table-column prop="suId" align="center" label="FSU-MAC" show-overflow-tooltip></el-table-column>
      <el-table-column prop="suVendor" align="center" label="SU厂家" show-overflow-tooltip>
        <template slot-scope="scope">
          <div v-if="scope.row.suVendor == 'ZNV'">力维</div>
          <div v-if="scope.row.suVendor == 'SAIERCOM'">赛尔</div>
        </template>
      </el-table-column>
      <el-table-column prop="deviceType" align="center" label="设备类型" show-overflow-tooltip></el-table-column>
      <el-table-column prop="deviceName" align="center" label="设备名称" show-overflow-tooltip></el-table-column>
      <el-table-column prop="deviceVendor" align="center" label="设备厂家" show-overflow-tooltip>
        <template slot-scope="scope">
          <div v-if="scope.row.deviceVender == 'ZNV'">力维</div>
          <div v-else-if="scope.row.deviceVender == 'SAIERCOM'">赛尔</div>
          <div v-else>{{ scope.row.deviceVender }}</div>
        </template>
      </el-table-column>
      <el-table-column prop="status" align="center" label="状态" show-overflow-tooltip></el-table-column>
      <el-table-column prop="statTime" align="center" label="核查时间" show-overflow-tooltip></el-table-column>
      <el-table-column align="center" label="未注册天数" show-overflow-tooltip>
        <template slot-scope="scope">
          <div>{{ scope.row.numDay }}天</div>
        </template>
      </el-table-column>
      <!-- <el-table-column prop="" label="操作">
        <template slot-scope="scope">
          <div class="ad">
            <el-tooltip  effect="dark" content="确定已退网"
              placement="top">
              <el-button  type="text" size="small">确定已退网</el-button>
            </el-tooltip>
          </div>
        </template>
      </el-table-column> -->
    </el-table>
    <div class="pag">
      <el-pagination @size-change="handleSizeChange" background @current-change="handleCurrentChange"
        :current-page="currentPage" :page-sizes="[10, 20, 50]" :page-size="pageSize"
        layout="total, sizes, prev, pager, next, jumper" :total="total">
      </el-pagination>
    </div>



  </div>
</template>

<script>
import exportTableToExcel from './components/exportExcel.js'
export default {
  data() {
    return {
      // 查询条件下拉数据
      citylist: [],
      areaList: [],
      siteList: [],
      roomlist: [],
      deviceTypeList: [],
      cedianlist: [],
      cedianNameList: [],
      devoptions: [],
      tableLoading: false,
      queryForm: {
        areaId: '',
        cityId: '',
        deviceType: '',
        deviceName: '',
        roomId: '',
        siteId: '',
        suId: '',
        suIp: '',
      },
      deviceTableData: [],
      pageSize: 10,
      currentPage: 1,
      total: 0,
      content: '',
      currentRow: null,
      exportloading: false,
      role: '',
      cityDisabled: false,
      areaDisabled: false,
    }
  },
  created() {
    // this.role = sessionStorage.getItem('role')
    // this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
    // this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''

    // if (this.role == '1') {
    //   this.cityDisabled = false
    //   this.areaDisabled = false
    // }

    // if (['94', '95'].includes(this.role)) {
    //   this.cityDisabled = true
    //   this.areaDisabled = this.queryForm.areaId ? true : false
    // }
    this.queryCityList()
    this.getSite()
    this.getRoom()
    this.getDeviceType()
    this.getDeviceName()
    this.queryData()
  },
  methods: {
    async devExport() {
      this.exportloading = true
      let data = [
        ['地市', '区域', '局站', '机房', 'SU_IP', 'SU厂家', '设备类型', '设备名称', '设备厂家', '状态', '核查时间', '未注册天数']
      ]
      let params = {
        ...this.queryForm,
        current: 1,
        size: this.total
      }
      try {
        let res = await this.$api.getDeviceList(params)
        if (res.code == 200) {
          res.data.records.forEach(v => {
            let item = [
              v.cityName,
              v.areaName,
              v.stationName,
              v.roomName,
              v.suIp,
              v.suVendor,
              v.deviceType,
              v.deviceName,
              v.deviceVender,
              v.status,
              v.statTime,
            ]
            data.push(item)
          })
          exportTableToExcel(data, '设备统计')
          this.exportloading = false
        } else {
          this.$message.error(res.msg)
          this.exportloading = false
        }
      } catch (error) {
        this.$message.error('导出异常')
        this.exportloading = false
      }

    },

    // 初始化地市
    queryCityList() {
      let params = {
        cityName: "",
        pageNum: 1,
        pageSize: 999
      }
      this.$api.queryCityList(params).then(res => {
        if (res.code == 200) {
          this.citylist = res.data.records
          this.cfgcitylist = res.data.records
          // if (this.queryForm.cityId) {
          //   this.areaList = this.citylist.find(v => v.cityId == this.queryForm.cityId).areaList
          // }
        }
      })
    },
    cityChange(val) {
      this.queryForm.areaId = ''
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.queryForm.deviceName = ''
      this.areaList = []
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      this.devoptions = []
      this.areaList = this.citylist.find(v => v.cityId == val).areaList
      this.getSite()
      this.getRoom()
      this.getDeviceName()
      this.getDeviceType()
    },
    areaChange() {
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.queryForm.deviceName = ''
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      this.devoptions = []
      this.getSite()
      this.getRoom()
      this.getDeviceName()
      this.getDeviceType()
    },
    siteChange() {
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.queryForm.deviceName = ''
      this.roomlist = []
      this.deviceTypeList = []
      this.devoptions = []
      this.getRoom()
      this.getDeviceName()
      this.getDeviceType()
    },
    roomChange() {
      this.queryForm.deviceType = ''
      this.queryForm.deviceName = ''
      this.deviceTypeList = []
      this.devoptions = []
      this.getDeviceName()
      this.getDeviceType()
    },
    devTypeChange() {
      this.queryForm.deviceName = ''
      this.devoptions = []
      this.getDeviceName()
    },

    getSite(val) {
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      let params = {
        cityId: this.queryForm.cityId,
        areaId: this.queryForm.areaId,
        siteName: val,
        pageNum: 1,
        pageSize: 999
      }

      this.$api.querySiteInfo(params).then(res => {
        if (res.code == 200) {
          this.siteList = res.data.records
        }
      })
    },
    getRoom(val) {
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.roomlist = []
      this.deviceTypeList = []
      let params = {
        cityId: this.queryForm.cityId,
        areaId: this.queryForm.areaId,
        siteId: this.queryForm.siteId,
        roomName: val,
        pageNum: 1,
        pageSize: 999
      }
      this.$api.queryRoomInfo(params).then(res => {
        if (res.code == 200) {
          this.roomlist = res.data
        }
      })
    },
    async getDeviceName(queryString) {
      let res = await this.$api.deviceNameByExp({
        deviceName: queryString,
        cityId: this.queryForm.cityId ? [this.queryForm.cityId] : [],
        areaId: this.queryForm.areaId ? [this.queryForm.areaId] : [],
        siteId: this.queryForm.siteId ? [this.queryForm.siteId] : [],
        roomId: this.queryForm.roomId ? [this.queryForm.roomId] : [],
        deviceType: this.queryForm.deviceType ? [this.queryForm.deviceType] : [],
        current: 1,
        size: 999
      })
      if (res.code == 200) {
        this.devoptions = res.data.records
      } else {
        this.$message.error(res.msg)

      }
    },
    async getDeviceType(val) {
      let res = await this.$api.deviceTypeByExp({
        deviceType: val,
        cityId: this.queryForm.cityId ? [this.queryForm.cityId] : [],
        areaId: this.queryForm.areaId ? [this.queryForm.areaId] : [],
        siteId: this.queryForm.siteId ? [this.queryForm.siteId] : [],
        roomId: this.queryForm.roomId ? [this.queryForm.roomId] : [],
        current: 1,
        size: 999
      })
      if (res.code == 200) {
        this.deviceTypeList = res.data.records
      } else {
        this.$message.error(res.msg)
      }
    },
    queryFormReset() {
      this.areaList = []
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      this.devoptions = []

      this.queryForm = {
        areaId: '',
        cityId: '',
        deviceType: '',
        deviceName: '',
        roomId: '',
        siteId: '',
        suId: '',
        suIp: '',
      }
      this.queryData()
      this.role = sessionStorage.getItem('role')
      this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
      this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''

      if (this.role == '1') {
        this.cityDisabled = false
        this.areaDisabled = false
      }

      if (['94', '95'].includes(this.role)) {
        this.cityDisabled = true
      }
      this.queryCityList()
      this.getSite()
      this.getRoom()
      this.getDeviceType()
      this.getDeviceName()
      this.queryData()
      this.queryData(this.queryForm.current)

    },

    // 监控量
    queryData(pageNum, pageSize) {
      this.tableLoading = true
      if (pageNum) this.currentPage = pageNum
      if (pageSize) this.pageSize = pageSize
      let params = {
        ...this.queryForm,
        pageNum: pageNum || this.currentPage,
        pageSize: pageSize || this.pageSize
      }
      this.$api.getDeviceList(params).then(res => {
        if (res.code == 200) {
          this.deviceTableData = res.data.records
          this.tableLoading = false
          this.total = res.data.total
        } else {
          this.tableLoading = false
          this.$message.error(res.msg)
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.queryData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.queryData()
    }
  }
}
</script>

<style lang="scss" scoped>
.ad {
  display: flex;
  gap: 6px;

  i {
    cursor: pointer;
  }
}

.pag {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 12px;
}

.selectWrap {
  width: 600px;
  height: 60px;
  margin-left: 100px;
  overflow: scroll;
  margin-bottom: 16px;
  //box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
  border: 1px solid #d7dae2;
}
</style>