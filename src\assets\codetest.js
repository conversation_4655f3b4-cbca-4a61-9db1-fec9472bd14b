/**
 * @file 本文件为接口地址文件，页面使用时可直接引入对应接口
 */
import http from '@/api/http'


export const provinceSliceOrderStatsImpl = (params) => {
    return http.post("/ordermng/process/com.ai.ordermanager.services.impl.ProvinceSliceOrderStatsImpl", params);
}

// 是否展示短信验证码
export const querySmsVerifyStatus = () => {
    return http.post("/custmng/querySmsVerifyStatus");
}

// 发送短信验证码
export const sendVerifyCodeVaep = (params) => {
    return http.post("/custmng/sendVerifyCodeVaep", params);
}

// 短信验证码登录
export const enterVerifyCodeVaep = ( params) => {
    return http.post("/custmng/enterVerifyCodeVaep", params);
}

//  表单设计器 数据保存 
export const handelFormDesignSave = (api, params) => {
    return http.post(api, params);
}

//  表单设计器 数据回显
export const handelFormDesignQuery = (api, params) => {
    return http.post(api, params);
}

export const a = params => {
    return http.post('/api/self/logout/v1', params);
}

export const imgPrefix = "/api/self/file/download/v1/fileinfo"

export const queryComponentListByIds = async (params) => {
    let { IDS } = params
    // let res = await http.post('/api/self/queryComponentListByIds/v1', params)
    let res = await http.post('/ordermng/process/com.ai.ordermanager.services.impl.QueryComponentListImpl', params)
    if (res?.CODE === 0) {
        let data = IDS.map(v => res.RESULT.DATALIST.find(el => el.ID == v)).filter(item => item)
        return data
    } else {
        return null
    }
}

// 
export const commonHttp = (api, params) => {
    return http.post(api, params);
};

// 退出
export const logout = params => {
    return http.post('/api/self/logout/v1', params);
};

// 修改密码   promise  axios.get().then(res => {

//})
export const updatePasswordImpl = params => {
    return http.post('/api/self/updatePasswordImpl/v1', params);
};

// 导出月报
export const operatingDataDocExportImpl = params => {
    return http.post('/ordermng/process/com.ai.interfacemanager.gdnunicom.services.impl.OperatingDataDocExportImpl', params, {
        responseType: 'blob',
        timeout: 1000*60*50
    });
};
export const queryComponentList = params => {
    return http.post('/api/self/queryComponentList/v1', params);
};
// 查看模板数据
export const queryComponentTemplate = params => {
    return http.post('/api/self/queryComponentTemplate/v1', params);
};
// 新增组件
export const createComponentCase = params => {
    return http.post('/api/self/createComponentCase/v1', params);
};
//新增页面
export const pageCreate = params => {
    return http.post('/api/rest/pageCreate/v1', params);
};

//   查询字典值
export async function queryDict(params) {
    return http.post('/custmng/model/query/confinfo/CONF_DICT_VALUE', params)
}

//
export async function incomeDetailsByCategory(params) {
    return http.post('/api/chinaUnicom/income/data/incomeDetailsByCategory/v1', params)
}
// 账单下载
export async function incomeBillDownload(params) {
    return http.post('/api/chinaUnicom/5g/sxzw/incomeBillDownload/v1', params)
}
// 
export async function incomeDetails(params) {
    return http.post('/api/chinaUnicom/income/data/incomeDetails/v1', params)
}

// 切片订单收入分析 概览
export async function incomeAnalysisOverview(params) {
    return http.post('/api/chinaUnicom/5g/sxzw/incomeAnalysisOverview/v1', params)
}

// 各省切片收入统计 
export async function incomeStatistics(params) {
    return http.post('/api/chinaUnicom/income/data/incomeStatistics/v1', params)
}

// 全国/各省份收入趋势对比
export async function incomeComparison(params) {
    return http.post('/api/chinaUnicom/income/data/incomeComparison/v1', params)
}


// 报表展示
export async function incomeBilledList(params) {
    return http.post('/api/chinaUnicom/5g/sxzw/incomeBilledList/v1', params)
}


// 编辑组件 new
export const pageModify = params => {
    return http.post('/api/rest/pageModify/v1', params);
};

// 编辑组件 old
export const modifyComponentCase = params => {
    return http.post('/api/self/modifyComponentCase/v1', params);
};

// 上传base64图片  
export const uploadBase64ImageFile = params => {
    return http.post('/api/rest/UploadBase64ImageFile/v1', params);
};

// 复制组件 new
export const pageCopy = params => {
    return http.post('/api/rest/pageCopy/v1', params);
};
// 复制组件 old
export const copyComponentCase = params => {
    return http.post('/cfgmng/process/com.wframe.cfginfo.services.impl.CopyComponentImpl', params);
};

// 删除组件 new
export const pageDelete = params => {
    return http.post('/api/rest/pageDelete/v1', params);
};

// 删除组件 old
export const deleteComponentCase = params => {
    return http.post('/api/self/deleteComponentCase/v1', params);
};

// 上传
export const upload = params => {
    return http.post('/api/self/file/upload/v1', params);
};

// 下载
export const download = params => {
    return http.post('/api/self/file/download/v1/fileinfo', params);
};

// 获取菜单
export const queryMenuByUser = params => {
    return http.post('/api/self/queryMenuByUser/v1', params);
};
// 获取验证码
export function getQrcode() {
    let timestamp = (new Date()).getTime();
    return {
        imageSrc: `/api/self/genVerificationCode/v1?t=${timestamp}`
    }
}
// 发送短信验证码
export const getSmsCode = params => {
    return http.post('/api/self/genCode/v1', params);
}

const LOCALSTORAGE_KEY = 'cnpost'
// 登录
export async function doLogin(payload, isSecret) {
    // 校验二维码
    let ret = {}
    let loginParam = {
        userCode: payload.userCode,
        passWord: payload.passWord,
        vercode: payload.vercode
    }
    if (isSecret) {
        loginParam.KEY_TYPE = payload.KEY_TYPE
    }
    let loginRes = await http.post('/api/self/userLogin/v1', loginParam)
    ret = loginRes.CODE === 0 ? loginRes : { CODE: 1, responseMsg: loginRes.MESSAGE }
    if (ret.CODE === 0) {
        // 保存登录信息至 session storage
        let dataResult
        if (typeof ret.RESULT === 'string') {
            dataResult = JSON.parse(ret.RESULT)
        } else {
            dataResult = ret.RESULT
        }
        let user = {
            // 用户名字
            username: dataResult.NAME,
            // id查询MenuList用
            id: dataResult.ID,
            // 注销时候传给后端的id
            sessionId: ret.RESULT?.GLOBAL_SESSION_ID,
            // 响应报文返回信息存储
            responseMsg: ret.MESSAGE,
            // 组织名
            orgName: ret.ORG_NAME,
            // 组织id
            orgId: ret.ORG_ID,
            // 密码过期时间
            pwdRemainingtDays: ret.PWD_REMAINING_DAYS,
            avatar: ret.HEAD_URL,
            // 登录成功的一个布尔值
            success: true,
            // staffId用来获取个人中心里用户信息
            staffId: ret.STAFF_ID,
            // 判断密码是否要过期的一个布尔值
            needRemaining: true,
            code: dataResult.CODE,
            // 密码是否重置过
            isModifiedPass: dataResult.CHG_PASSWD_FLAG,
        }
        ret.user = user
        sessionStorage.setItem(LOCALSTORAGE_KEY, JSON.stringify(user))
    }
    return ret
}

export const queryParamByCode = async (code) => {
    let res = await http.post('/api/self/queryParamByCode/v1', code)
    if (res?.CODE === 0 && res.RESULT.DATALIST && res.RESULT.DATALIST.length > 0) {
        return res.RESULT.DATALIST
    } else {
        return null
    }
}

export const queryConfigByCode = async (code) => {
    let params = {};
    params['CODE'] = code;
    let res = await http.post('/api/self/queryLoginComId/v1', params)
    if (res?.CODE === 0 && res.RESULT.DATALIST && res.RESULT.DATALIST.length > 0) {
        return res.RESULT.DATALIST
    } else {
        return null
    }
}
///api/self/local/confParam/v1


export const queryConfigParamList = async () => {
    let params = {};
    let res = await http.post('/api/self/local/confParam/v1', params)
    if (res?.CODE === 0 && res.RESULT.DATALIST && res.RESULT.DATALIST.length > 0) {
        return res.RESULT.DATALIST
    } else {
        return null
    }
}

export const callComMethod = async (comp, callName, params) => {
    if (comp == null) {
        return null;
    }
    if (comp[callName] != null && typeof comp[callName] == "function") {
        console.debug("callComMethod call parent function:" + callName + "!");
        return comp[callName]("test");
    }
    return null;
}

export const callParentMethod = async (othis, callName, params) => {
    var parent = othis.$parent;
    if (parent == null) {
        return null;
    }
    return callComMethod(parent, callName, params);
}
///api/self/sysParam/v1
//这个接口必须登录
export const querySysParamByCode = async (code) => {
    console.log(code)
    if (code == null || code == "") {
        return null;
    }
    let cond = {};
    cond['CODE'] = code;
    let params = {};
    params['condition'] = JSON.stringify(cond);
    let res = await http.post('/api/self/sysParam/v1', params)
    if (res?.CODE === 0 && res.RESULT.DATALIST && res.RESULT.DATALIST.length > 0) {
        return res.RESULT.DATALIST
    } else {
        return null
    }
}


// 查询待办事项条数
export const queryTaskCount = () => {
    let params = { "condition": { "TODO": { "cond_type": "LIKE", "value": "" }, "STATUS": "1" } }
    return http.post("/basemng/model/count/baseinfo/SYS_TASK", params);
};


// 导出组件接口  /api/rest/download/component/v1
export const exportCompApi = (params) => {
    return http.post("/api/rest/download/component/v1", params);
};

// 查询js 接口数据
export const queryJSApi = (params) => {
    return http.post("/api/self/runs/v1", params);
};


export const queryMenuByTenantCode = (params) => {
    return http.post("/api/self/queryMenuByTenantCode/v1", params);
};

// 查询api能力列表
export const queryApiMain = (params) => {
    return http.post("/api/rest/apiMainQueryImpl/v1", params);
};

// 查询能力分组
export const queryApiModule = (params) => {
    return http.post("/api/rest/apiModuleQueryImpl/v1", params);
};

export async function queryChildTenanTByCode(params) {
    let param = {
        // condition:{CODE:params.TENANT_CODE},
        CODE: params.TENANT_CODE,
        result_method: "com.ai.tenantmng.services.result.QueryTenantMapOperatorImpl"
    }
    return http.post('/api/self/queryTenantChildImpl/v1', param)
}

export async function changeTenandLoginInfo(params) {
    return http.post('/api/rest/changeLoginInfo/v1', params)
}

//随行专网

//全国5G随行专网收入与客户规模（日环比）
export async function sxzw_busCustomerIncomDayList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/busCustomerIncomDayList/v1', params)
}

//全国5G随行专网收入与客户规模（周环比）
export async function sxzw_busCustomerIncomWeekList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/busCustomerIncomWeekList/v1', params)
}

//全国5G随行专网收入与客户规模（月环比）
export async function sxzw_busCustomerIncomMonthList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/busCustomerIncomMonthList/v1', params)
}

//全国各省随行专网收入统计	
export async function sxzw_incomeCountByProvinceList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/incomeCountByProvinceList/v1', params)
}
//全国各行业收入统计	
export async function sxzw_incomeCountByIndustryList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/incomeCountByIndustryList/v1', params)
}
//全国各省分行业收入统计	/api/chinaUnicom/sxzw/data/industryIncomeCountByProvinceList/v1
export async function sxzw_industryIncomeCountByProvinceList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/industryIncomeCountByProvinceList/v1', params)
}

//全国各省客户数统计	/api/chinaUnicom/sxzw/data/custCountByProvinceList/v1
export async function sxzw_custCountByProvinceList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/custCountByProvinceList/v1', params)
}
//全国各行业客户统计	/api/chinaUnicom/sxzw/data/custCountByIndustryList/v1
export async function sxzw_custCountByIndustryList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/custCountByIndustryList/v1', params)
}
//全国各省分行业客户统计	/api/chinaUnicom/sxzw/data/industryCustCountByProvinceList/v1
export async function sxzw_industryCustCountByProvinceList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/industryCustCountByProvinceList/v1', params)
}

//全国用户与号卡概括（日环比）	  
export async function sxzw_busUserTrafficDayList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/busUserTrafficDayList/v1', params)
}
//全国用户与号卡概括（周环比） 
export async function sxzw_busUserTrafficWeekList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/busUserTrafficWeekList/v1', params)
}
//全国用户与号卡概括（月环比） 
export async function sxzw_busUserTrafficMonthList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/busUserTrafficMonthList/v1', params)
}

//全国号卡业务详情	 
export async function sxzw_busCardIncomList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/busCardIncomList/v1', params)
}



//全国各省用户数统计	/api/chinaUnicom/sxzw/data/userCountByProvinceList/v1
export async function sxzw_userCountByProvinceList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/userCountByProvinceList/v1', params)
}

//全国各行业用户数统计	/api/chinaUnicom/sxzw/data/userCountByIndustryList/v1
export async function sxzw_userCountByIndustryList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/userCountByIndustryList/v1', params)
}

//全国各省分行业用户数统计	/api/chinaUnicom/sxzw/data/industryUserCountByProvinceList/v1
export async function sxzw_industryUserCountByProvinceList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/industryUserCountByProvinceList/v1', params)
}

//全国各省用户号卡收入统计	/api/chinaUnicom/sxzw/data/userCardIncomeCountByProvinceList/v1
export async function sxzw_userCardIncomeCountByProvinceList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/userCardIncomeCountByProvinceList/v1', params)
}


//全国各行业用户收入统计	/api/chinaUnicom/sxzw/data/userCardIncomeCountByIndustryList/v1
export async function sxzw_userCardIncomeCountByIndustryList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/userCardIncomeCountByIndustryList/v1', params)
}
//全国各省分行业用户收入统计	/api/chinaUnicom/sxzw/data/industryUserCardIncomeCountByProvinceList/v1
export async function sxzw_industryUserCardIncomeCountByProvinceList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/industryUserCardIncomeCountByProvinceList/v1', params)
}
//全国各省单用户专网流量统计	/api/chinaUnicom/sxzw/data/trafficCountByProvinceList/v1
export async function sxzw_trafficCountByProvinceList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/trafficCountByProvinceList/v1', params)
}
//全国各行业单用户专网流量统计	/api/chinaUnicom/sxzw/data/trafficCountByIndustryList/v1
export async function sxzw_trafficCountByIndustryList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/trafficCountByIndustryList/v1', params)
}
//全国各省分行业单用户专网流量统计	/api/chinaUnicom/sxzw/data/industryTrafficCountByProvinceList/v1
export async function sxzw_industryTrafficCountByProvinceList(params) {
    return http.post('/api/chinaUnicom/sxzw/data/industryTrafficCountByProvinceList/v1', params)
}


// 获取车联网统计信息
export async function getIoVOverview() {  
  return http.post('/api/process/ClwOverview/v1');
}
// 获取车联网订单列表
export async function getIoVOrderList(params) {
  return http.post('/api/process/ClwOrderList/v1', params);
}

// 订单一体化，订单列表
export async function getOrderInOneList(params) {  
  return http.post('/api/self/OrderIntegrationList/v1', params);
}

// 工单管理，获取用户信息
export async function getUserInfo(params) {
  return http.get('/bpm/api/v1/userInfo', { params });
}

// 工单管理，获取工单列表
export async function getWorkOrderList(params) {
  return http.get('/bpm/api/v1/workOrder/list', { params });
}

// 工单管理，获取工单详情
export async function getWorkOrderDetail(params) {
  return http.get('/bpm/api/v1/workOrder/detail', { params });
}

// 审核工单
export async function checkWorkOrder(params) {
  return http.post('/bpm/api/v1/check', params);
}

// 回答工单
export async function answerkWorkOrder(params) {
  return http.post('/bpm/api/v1/answer', params);
}
