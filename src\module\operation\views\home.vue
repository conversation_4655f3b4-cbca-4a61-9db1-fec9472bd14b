<template>
    <div class="homewrap" style="height: 100%;">
        <!-- <img :src="bg" width="500" alt="">
        <span>运维页面正在建设中...</span> -->
        <homeWarnDetail ></homeWarnDetail>
    </div>

</template>

<script>
import homeWarnDetail from "./components/homeWarnDetail.vue"
export default {
    components: {
        homeWarnDetail
    },
    data() {
        return {
            bg: require('./../views/img/homebg.jpg'),
            type: '',
            typeId: '',
            cityId: '',
            areaId: '',
            siteId: '',
            roomId: '',
        }
    },

    watch: {
        '$route.query': {
            immediate: true, // 如果需要在组件创建时立即触发，设置为true
            handler(newVal, oldVal) {
                // 当路由参数变化时，这里会被调用
                // newVal 是新的参数，oldVal 是旧的参数
                // console.log('Route parameter changed:', newVal);
                // 你可以在这里根据新的参数执行相应的操作
                this.type = newVal?.type || ''
                //this.typeId = newVal?.id || ''
                this.cityId = newVal?.cityId || ''
                this.areaId = newVal?.areaId || ''
                this.siteId = newVal?.cityId || ''
                this.roomId = newVal?.cityId || ''
            }
        }
    },

    created() {
    },
   
}
</script>

<style lang="scss" scoped>
    .homewrap {
        min-width: 990px;
        display: flex;
        flex-direction: column;
        align-items: center;
        span {
            margin-top: 24px;
            font-size: 24px;
        }
    }
</style>