import http from '@/api/http'

// api/report/dataTable/1 http://172.17.13.109/api/report/reportOffService/page/list
export function getSyOrderData(parms) {
  return http({
    url: '/api/flowable/reportCon/getSyOrderData',
    method: 'post',
    data: parms
  });
}
// 退服稽核报表
export function getTfjhOrderData(parms) {
  return http({
    url: '/api/flowable/reportCon/getTfjhOrderData',
    method: 'post',
    data: parms
  });
}

// 点击数字下钻查询工单明细
export function listToOrder(parms){
  return http({
    url: '/api/flowable/reportCon/listToOrder',
    method: 'post',
    data: parms
  });
}



