import http from '@/api/http'

//sid 查询
export function getSidInfo(query) {
  return http({
    url: '/api/resource/sid/getSidInfo',
    method: 'post',
    data: query
  });
}

//sid删除
export function delSidInfo(query) {//
  return http({
    url: '/api/resource/sid/delSidInfo',
    method: 'post',
    data: query
  });
}
//sid 新增
export function addSidInfo(query) {//
  return http({
    url: '/api/resource/sid/addSidInfo',
    method: 'post',
    data: query
  });
}
//修改
export function updSidInfo(query) {//
  return http({
    url: '/api/resource/sid/updSidInfo',
    method: 'post',
    data: query
  });
}
//根据provId查询sid情况
export function querySidInfoByProv(query) {//
  return http({
    url: '/api/resource/sid/querySidInfoByProv',
    method: 'post',
    data: query
  });
}
