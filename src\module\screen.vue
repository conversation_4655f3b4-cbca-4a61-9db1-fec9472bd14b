<template>
    <div class="container">
       <img :src="bg"  width="100%" @click="toPath" alt="">
    </div>
</template>

<script>
import { mapState, mapMutations } from "vuex";
export default {
    name: "Container",
    components: {},
    data() {
        return {
            bg: require('@/assets/images/screen-demo.png')
        };
    },
    computed: {
        
    },
    created() {
        
    },
    beforeDestroy(){
        
    },
    methods: {
     
     toPath() {
        this.$router.push('/operation')
     }
    },
};
</script>

<style lang="scss">
.container {
    width: 100%;
    height: 100%;
    overflow: hidden;
}
</style>
