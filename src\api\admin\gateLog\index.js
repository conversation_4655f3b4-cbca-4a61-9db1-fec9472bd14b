import http from '@/api/http'

export function page(query) {
    return http({
        url: '/api/admin/gateLog/page',
        method: 'get',
        params: query
    });
}

export function addObj(obj) {
    return http({
        url: '/api/admin/gateLog',
        method: 'post',
        data: obj
    });
}

export function getObj(id) {
    return http({
        url: '/api/admin/gateLog/' + id,
        method: 'get'
    })
}

export function delObj(id) {
    return http({
        url: '/api/admin/gateLog/' + id,
        method: 'delete'
    })
}

export function putObj(id, obj) {
    return http({
        url: '/api/admin/gateLog/' + id,
        method: 'put',
        data: obj
    })
}
