* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
  outline: none;
}

html,
body,
#app,
.retina {
  width: 100%;
  height: 100%;
}

body {
  min-width: 1000px;
  // min-height: 600px;
  color: rgba(0, 0, 0, 0.65);
  background: #f2f6f8;
  font-family: Avenir, Helvetica, Arial, sans-serif;
  font-size: 12px;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

ol,
ul {
  list-style: none;
}

button {
  outline: none;
  border: 0;
  cursor: pointer;
  background: transparent;
}
a {
  color: inherit;
  text-decoration: none;
}
a.download {
  color: inherit;
  text-decoration: none;
}
.el-menu.el-menu--horizontal {
  // background-color: $menu_bg_color;
  & > .el-menu-item {
    height: 50px;
    line-height: 50px;
    // color: $menu_font_color;
  }
  & > .el-submenu .el-submenu__title {
    height: 50px;
    line-height: 50px;
    // color: $menu_font_color;
  }
}
.el-menu.el-menu--popup.el-menu--popup-bottom-start,
.el-menu.el-menu--popup.el-menu--popup-right-start,
.el-menu.el-menu--popup.el-menu--popup-right-start .el-submenu,
.el-menu.el-menu--popup.el-menu--popup-right-start .el-menu-item {
  min-width: 136px;
}
.fe-border-gray {
  border: 1px solid #455a74;
}

.fe-border-red {
  border: 1px solid #f00;
}
.fe-layout {
  width: 100%;
  height: 100%;
}
.element-pager {
  position: relative;
  text-align: right;
  .el-pagination.is-background {
    padding: 0;
    .el-pagination__sizes {
      position: absolute;
      top: 0;
      left: 0;
      .el-select .el-input {
        margin: 0;
      }
    }
  }
  .el-pagination span:not([class*='suffix']) {
    font-size: 12px;
  }
  .el-pagination__sizes .el-input .el-input__inner {
    font-size: 12px;
  }
}
.el-menu.el-menu--horizontal {
  border-bottom: none !important;
}

.el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
  &:hover {
    border-color: #409eff;
  }
}

/* 水平样式 */
.el-menu--horizontal > div > .el-submenu {
  float: left;
}
/* 一级菜单的样式 */
.el-menu--horizontal > div > .el-menu-item {
  float: left;
  height: 56px;
  line-height: 56px;
  margin: 0;
  border-bottom: 2px solid transparent;
  color: #909399;
}
/* 解决下图1 下拉三角图标 */
.el-menu--horizontal > div > .el-submenu .el-submenu__icon-arrow {
  position: static;
  vertical-align: middle;
  margin-left: 8px;
  margin-top: -3px;
}
/* 解决下图2 无下拉菜单时 不对齐问题 */
.el-menu--horizontal > div > .el-submenu .el-submenu__title {
  height: 56px;
  line-height: 56px;
  border-bottom: 2px solid transparent;
  color: #909399;
}
.my_pop {
  padding: 0 !important;
}
.el-menu--popup-bottom-start {
  margin-top: 0 !important;
}
.my-input {
  .el-input__inner {
    padding-left: 50px !important;
  }
  .el-input__prefix {
    left: 8px !important;
    .iconfont {
      font-size: 24px;
    }
  }
  &::before {
    content: '';
    height: 16px;
    width: 1px;
    position: absolute;
    top: 50%;
    margin-top: -8px;
    left: 40px;
    z-index: 10;
    background-color: rgba(0, 0, 0, 0.09);
  }
}
.my_error {
  width: 320px;
  height: 40px;
  margin-bottom: 28px;
  padding: 0 16px;
  box-sizing: border-box;
  border-radius: 2px;
  background-color: rgba(255, 241, 240, 1);
  border: 1px solid rgba(255, 204, 199, 1);
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  .my_error_1 {
    display: flex;
    flex-direction: row;
    align-items: center;
    .my_error_1_1 {
      display: flex;
      flex-direction: row;
      align-items: center;
      justify-content: center;
      width: 16px;
      height: 16px;
      line-height: 16px;
      border-radius: 50%;
      color: #fff;
      font-size: 12px;
      background-color: rgba(240, 65, 52, 1);
      margin-right: 8px;
    }
    .my_error_1_2 {
      color: rgba(0, 0, 0, 0.65);
    }
  }
  .my_error_2 {
    cursor: pointer;
    padding: 4px;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
  }
}


.myBread {
  padding-left: 24px;
  background-color: #272845;
  height: 40px;
  display: flex;
  align-items: center;
 .itemBread {
   .el-breadcrumb__inner {
      color: #909399;
   }
  
 }
 .el-breadcrumb__item:last-child .el-breadcrumb__inner, .el-breadcrumb__item:last-child .el-breadcrumb__inner a, .el-breadcrumb__item:last-child .el-breadcrumb__inner a:hover, .el-breadcrumb__item:last-child .el-breadcrumb__inner:hover {
      color: #fff;
   }
}

.my-label {
  background: #69bc3f;
}
.my-content{
  color: #F56C6C;
}