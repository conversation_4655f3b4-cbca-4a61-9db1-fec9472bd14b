import Cookies from 'js-cookie';

const app = {
  state: {
    sidebar: {
      opened: !+Cookies.get('sidebarStatus')
    },
    theme: 'default',
    livenewsChannels: Cookies.get('livenewsChannels') || '[]',
    visitedViews: JSON.parse(sessionStorage.getItem('visitedlist')) ? JSON.parse(sessionStorage.getItem('visitedlist')) : [],//[{name:'首页',path:'/',keepAlive:true}],
    keepAliveflg: JSON.parse(sessionStorage.getItem('keepAliveflg')) ? JSON.parse(sessionStorage.getItem('keepAliveflg')) : {},
    btnactive: localStorage.getItem('btnactive') ? localStorage.getItem('btnactive') : '0',
    orderInfos: JSON.parse(sessionStorage.getItem("orderInfos")) ? JSON.parse(sessionStorage.getItem("orderInfos")) : [],
    orderNum: '',
    gohomemap: 'homeMap',
    gotodaijobid: '',
    healthInfo: sessionStorage.getItem("healthInfo") ? sessionStorage.getItem("healthInfo") : ''
  },
  mutations: {
    ORDER_INFO: (state, vl) => {
      state.orderInfos = vl
      sessionStorage.setItem('orderInfos', JSON.stringify(state.orderInfos));
    },//
    ORDER_NUM: (state, vl) => {
      state.orderNum = vl
      //sessionStorage.setItem('orderInfos',JSON.stringify(state.orderInfos));
    },
    GOTODAI_JOBID: (state, vl) => {

      state.gotodaijobid = vl

    },
    GOHOMEMAP: (state, vl) => {
      state.gohomemap = vl
      //sessionStorage.setItem('orderInfos',JSON.stringify(state.orderInfos));
    },
    KEEP_ALIVEFLG: (state, vl) => {
      state.keepAliveflg = vl
      sessionStorage.setItem('keepAliveflg', JSON.stringify(state.keepAliveflg));
    },
    TOGGLE_SIDEBAR: state => {
      if (state.sidebar.opened) {
        Cookies.set('sidebarStatus', 1);
      } else {
        Cookies.set('sidebarStatus', 0);
      }
      state.sidebar.opened = !state.sidebar.opened;
    },
    ADD_VISITED_VIEWS: (state, view) => {
      // console.log(view);
      if (state.visitedViews.some(v => v.path === view.path) && view.meta.keepAlive) {
        state.visitedViews.forEach((item, index) => {
          if (item.path === view.path) {
            item.isflg = true;
          } else {
            item.isflg = false;
          }
        });
        return;
      }
      // console.log(view)
      let obj = {
        name: view.name,
        path: view.path,
        keepAlive: view.meta.keepAlive,
        isflg: true,
        objectId: view.meta.objectId,
        rscId: view.meta.rscId,
      }
      // 一个页面中 新增 修改 查看  避免相同的重复出现tab页面
      let arrvis = state.visitedViews;
      // console.log(arrvis)
      let arrflg = false;
      //遍历 state.visitedViews 把iframe引的页面关掉 只展示最后一个iframe
      state.visitedViews.filter(function (item, index) {

        item.isflg = false;
        if (item.objectId == obj.objectId && item.rscId == obj.rscId && item.objectId && item.name == obj.name) {
          arrvis.splice(index, 1, obj);
          arrflg = true;
        }
      })

      if (!arrflg) {
        arrvis.push(obj)
      }
      // view.meta.keepAlive?'':obj.isflg = view.meta.isflg;
      // state.visitedViews.push(obj);
      state.visitedViews = arrvis;
      // sessionStorage.setItem('menuoflist',JSON.stringify(that.menuoflist));
      sessionStorage.setItem('visitedlist', JSON.stringify(state.visitedViews));
    },
    DEL_VISITED_VIEWS: (state, view) => {
      let index;
      for (const [i, v] of state.visitedViews.entries()) {
        if (v.path === view.path) {
          index = i
          break
        }
      }
      state.visitedViews.splice(index, 1);
      sessionStorage.setItem('visitedlist', JSON.stringify(state.visitedViews));
    },
    CLEAR_VISITED_VIEWS: (state, view) => {
      // state.visitedViews.splice(0, state.visitedViews.length);
      state.visitedViews = state.visitedViews.length ? state.visitedViews.slice(-1) : [];
      sessionStorage.setItem('visitedlist', JSON.stringify(state.visitedViews));
    },
    BTN_ACTIVENUM: (state, view) => {
      // console.log(view)
      state.btnactive = view;
      localStorage.setItem('btnactive', view);

    },
    HEALTH_INFO: (state, vl) => {
      state.healthInfo = vl
      sessionStorage.setItem('healthInfo', vl);
    }
  },
  actions: {
    ToggleSideBar: ({ commit }) => {
      commit('TOGGLE_SIDEBAR')
    },
    addVisitedViews: ({ commit }, view) => {
      commit('ADD_VISITED_VIEWS', view)
    },
    delVisitedViews: ({ commit }, view) => {
      commit('DEL_VISITED_VIEWS', view);
    },
    clearVisitedViews: ({ commit }, view) => {
      commit('CLEAR_VISITED_VIEWS', view);
    },
    setkeepAliveflg: ({ commit }, va) => {
      commit('KEEP_ALIVEFLG', va);
    },
    btnactivenum: ({ commit }, va) => {
      commit('BTN_ACTIVENUM', va);
    },
    setorderInfo: ({ commit }, val) => {
      commit("ORDER_INFO", val);
    },
    setordernumInfo: ({ commit }, val) => {
      commit("ORDER_NUM", val);
    },
    setgohomemapInfo: ({ commit }, val) => {
      commit("GOHOMEMAP", val);
    },
    setHealthInfo: ({ commit }, val) => {
      commit("HEALTH_INFO", val);
    },
    setgotodaijobid: ({ commit }, val) => {
      commit("GOTODAI_JOBID", val);
    },
  }
};

export default app;
