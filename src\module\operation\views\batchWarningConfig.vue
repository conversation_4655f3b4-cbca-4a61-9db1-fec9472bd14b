<template>
  <div class="app-container">


    <!-- 监控量 -->
    <div>
      <!-- 一级列表 -->
      <div v-if="currentModule == 'list'">
        <el-form :inline="true" :model="queryForm" size="small" label-width="100px" class="demo-form-inline">
          <el-form-item label="地市:">
            <el-select v-model="queryForm.cityId" :disabled="cityDisabled" @change="cityChange" filterable clearable
              placeholder="请选择">
              <el-option v-for="(item, index) in citylist" v-show="index !== 0" :key="item.cityId"
                :label="item.cityName" :value="item.cityId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="区域:">
            <el-select v-model="queryForm.areaId" :disabled="areaDisabled" @change="areaChange" filterable clearable
              placeholder="请选择">
              <el-option v-for="item in areaList" :key="item.areaId" :label="item.areaName"
                :value="item.areaId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="局站:">
            <el-select v-model="queryForm.siteId" @change="siteChange" :remote-method="getSite" filterable clearable
              placeholder="请选择">
              <el-option v-for="item in siteList" filterable :key="`${item.area_id}-${item.site_id}`"
                :label="item.site_name" :value="item.site_id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="机房:">
            <el-select v-model="queryForm.roomId" @change="roomChange" :remote-method="getRoom" filterable clearable
              placeholder="请选择">
              <el-option v-for="item in roomlist" :key="`${item.room_id}-${item.site_id}`" :label="item.room_name"
                :value="item.room_id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备类型:">
            <el-select v-model="queryForm.deviceType" :remote-method="getDeviceType" filterable remote clearable
              placeholder="请输入">
              <el-option v-for="(item, index) in deviceTypeList" :key="`${index}-${item.id}`" :label="item.label"
                :value="item.id"></el-option>
            </el-select>
          </el-form-item>

          <el-form-item label="测点类型:">
            <el-select v-model="queryForm.signalType" clearable placeholder="请选择">
              <el-option label="遥测-告警" :value="2"></el-option>
              <el-option label="遥信-告警" :value="4"></el-option>
              <!-- <el-option label="遥测" :value="1"></el-option>
              <el-option label="遥信" :value="3"></el-option> -->
            </el-select>
            <!-- <el-input v-model="queryForm.signalType" placeholder="请输入" clearable></el-input> -->
          </el-form-item>
          <el-form-item label="告警标题:">
            <el-input v-model="queryForm.signalName" placeholder="请输入" clearable></el-input>
          </el-form-item>

          <el-form-item label="">
            <el-button style="margin-left: 80px;" @click="queryFormReset">重置</el-button>
            <el-button type="primary" @click="queryData(1, 10)">查询</el-button>
            <el-button type="primary" @click="showAdd">新增</el-button>
          </el-form-item>
        </el-form>
        <el-table :data="deviceConfigTableData" header-row-class-name="myHeaderClass" size="small"
          v-loading="tableLoading">
          <el-table-column prop="cityName" align="center" label="地市" show-overflow-tooltip></el-table-column>
          <el-table-column prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
          <el-table-column prop="siteName" align="center" label="局站" show-overflow-tooltip></el-table-column>
          <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
          <el-table-column prop="deviceTypeName" align="center" label="设备类型" show-overflow-tooltip></el-table-column>
          <!-- <el-table-column prop="deviceName" align="center" label="设备名称" show-overflow-tooltip></el-table-column> -->
          <el-table-column prop="signalName" align="center" label="告警标题" show-overflow-tooltip></el-table-column>
          <el-table-column prop="alarmLevel" align="center" label="告警级别" show-overflow-tooltip>

          </el-table-column>
          <el-table-column prop="" label="操作" width="200">
            <template slot-scope="scope">
              <div class="ad">
                <!-- 阈值设置 6-->
                <!-- <el-tooltip v-if="btndeviceThreshold && ['6'].includes(scope.row.type)" effect="dark" content="阈值设置"
                  placement="top">
                  <i @click="deviceThreshold(scope.row)" class="el-icon-coordinate " style="font-size: 18px;"></i>
                </el-tooltip> -->
                <el-button type="text" size="small" @click="showConfig(scope.row)">配置日志</el-button>
                <el-button type="text" size="small" @click="showDetial(scope.row)">配置详情</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>
        <div class="pag" v-if="deviceConfigTableData.length">
          <el-pagination @size-change="handleSizeChange" background @current-change="handleCurrentChange"
            :current-page="currentPage" :page-sizes="[10, 20, 50]" :page-size="pageSize"
            layout="total, sizes, prev, pager, next, jumper" :total="total">
          </el-pagination>
        </div>
      </div>

      <el-page-header v-if="currentModule !== 'list'" @back="goback">
        <div slot="content" style="font-size: 14px;">
          {{ content }}
        </div>
      </el-page-header>

      <!-- 设备阈值配置 -->
      <div style="margin-top: 16px;">

        <!-- 新增 -->
        <el-form v-if="currentModule == 'add'" :inline="true" :model="deviceThresholdData" label-width="100px"
          size="small">
          <el-form-item label="地市:">
            <el-select @change="getAreaOptions" :disabled="cityDisabled" filterable
              v-model="deviceThresholdData.selectCityIds" placeholder="请选择">
              <el-option v-for="(item, index) in cfgcitylist" v-show="index !== 0" :key="item.cityId"
                :label="item.cityName" :value="item.cityId"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="区域:">
            <el-select @remove-tag="areaRemove" :disabled="areaDisabled" filterable @change="batchAreaChange"
              v-model="deviceThresholdData.selectAreaIds" multiple placeholder="请选择">
              <el-option v-for="item in cfgareaList" :key="item.id" :label="item.label" :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="局站:">
            <el-select @remove-tag="siteRemove" @change="batchSiteChange" remote :remote-method="getSiteOptions"
              filterable v-model="deviceThresholdData.selectSiteIds" multiple placeholder="请输入进行检索">
              <el-option v-for="item in cfgsiteList" :key="item.id + item.lastId" :label="item.label"
                :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="机房:">
            <el-select :remote-method="getRoomOptions" remote v-model="deviceThresholdData.selectRoomIds" filterable
              multiple placeholder="请输入进行检索">
              <el-option v-for="item in cfgroomlist" :key="item.id + item.lastId" :label="item.label"
                :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备类型:">
            <el-select @remove-tag="devTypeRemove" v-loadmore="getDevTypeOptions" remote multiple
              :remote-method="getDevTypeOptions" v-model="deviceThresholdData.selectDevTypeIds" filterable
              placeholder="请输入进行检索">
              <el-option v-for="item in cfgdeviceTypeList" :key="item.id + item.lastId" :label="item.label"
                :value="item.id"></el-option>
            </el-select>
          </el-form-item>
          <el-form-item label="设备名称:">
            <el-input v-model="deviceThresholdData.deviceName" placeholder="请输入" clearable></el-input>
          </el-form-item>
          <el-form-item label="SU_ID:">
            <el-input v-model="suId" placeholder="请输入" clearable></el-input>
          </el-form-item>
          <el-form-item label="SU_IP:">
            <el-input v-model="suIp" placeholder="请输入" clearable></el-input>
          </el-form-item>
          <el-form-item label=" ">
            <el-button type="primary" @click="queryDeviceData(1, 10)">查询</el-button>
          </el-form-item>
          <div style=" margin: 14px 12px;">
            <div style="margin: 12px;font-size: 14px;font-weight: 500;font-size: 14px;color: #333333;  ">待选设备列表：</div>
            <el-table :data="deviceTableData" ref="deviceTableData" max-height="460" row-key="devSuId"
              @selection-change="selectDeviceFn" border header-row-class-name="myHeaderClass" size="small"
              v-loading="deviceTableLoading">
              <el-table-column type="selection" :reserve-selection="true" width="55"></el-table-column>
              <el-table-column prop="cityName" align="center" label="地市" show-overflow-tooltip></el-table-column>
              <el-table-column prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
              <el-table-column prop="siteName" align="center" label="局站" show-overflow-tooltip></el-table-column>
              <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
              <el-table-column prop="suIp" align="center" label="SU_IP" show-overflow-tooltip></el-table-column>
              <el-table-column prop="suVendor" align="center" label="SU厂家" show-overflow-tooltip>
                <template slot-scope="scope">
                  <div v-if="scope.row.suVendor == 'ZNV'">力维</div>
                  <div v-if="scope.row.suVendor == 'SAIERCOM'">赛尔</div>
                </template>
              </el-table-column>
              <el-table-column prop="deviceTypeName" align="center" label="设备类型"
                show-overflow-tooltip></el-table-column>
              <el-table-column prop="deviceName" align="center" label="设备名称" show-overflow-tooltip></el-table-column>
              <el-table-column prop="deviceVendor" align="center" label="设备厂家" show-overflow-tooltip></el-table-column>
            </el-table>
            <div class="pag" v-if="deviceTabletotal > 0">
              <el-pagination @size-change="handleDeviceSizeChange" background
                @current-change="handleDeviceCurrentChange" :current-page="deviceTablecurrentPage"
                :page-sizes="[10, 20, 50]" :page-size="deviceTablepageSize" layout="total,  prev, pager, next, jumper"
                :total="deviceTabletotal">
              </el-pagination>
            </div>
            <div style="margin: 18px 0 0px;font-size: 18px;display: flex;flex-direction: row;justify-content: center;">
              <div class="iconWrap" style="margin-right: 40px;cursor: pointer;" @click="addDevice">
                <i class="el-icon-d-arrow-left" style="transform: rotateZ(-90deg);color: #333333;"></i>
              </div>
              <div class="iconWrap" style="cursor: pointer;" @click="removeDevice">
                <i class="el-icon-d-arrow-left" style="transform: rotateZ(90deg);color: #333333;"></i>
              </div>


            </div>
            <div style="margin: 0 0 12px; font-size: 14px; font-weight: 500;font-size: 14px;color: #333333; ">已选设备列表：
            </div>
            <el-table :data="selectedDeviceData" @selection-change="selectRemoveDeviceFn" max-height="460" border
              header-row-class-name="myHeaderClass" size="small">
              <el-table-column type="selection" width="55"></el-table-column>
              <el-table-column prop="cityName" align="center" label="地市" show-overflow-tooltip></el-table-column>
              <el-table-column prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
              <el-table-column prop="siteName" align="center" label="局站" show-overflow-tooltip></el-table-column>
              <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
              <el-table-column prop="suIp" align="center" label="SU_IP" show-overflow-tooltip></el-table-column>
              <el-table-column prop="suVendor" align="center" label="SU厂家" show-overflow-tooltip>
                <template slot-scope="scope">
                  <div v-if="scope.row.suVendor == 'ZNV'">力维</div>
                  <div v-if="scope.row.suVendor == 'SAIERCOM'">赛尔</div>
                </template>
              </el-table-column>
              <el-table-column prop="deviceTypeName" align="center" label="设备类型"
                show-overflow-tooltip></el-table-column>
              <el-table-column prop="deviceName" align="center" label="设备名称" show-overflow-tooltip></el-table-column>
              <el-table-column prop="deviceVendor" align="center" label="设备厂家" show-overflow-tooltip></el-table-column>
            </el-table>

            <div style="margin: 18px 0 12px;font-size: 14px; font-weight: 500;font-size: 14px;color: #333333; ">待选告警标题列表
            </div>
            <div style="display: flex;margin-bottom: 14px;">
              <div style="flex:1">
                <div style="display: flex;">
                  <el-form-item label="测点类型:">
                    <el-select style="width: 200px;" multiple v-model="deviceThresholdData.signalTypeIds"
                      placeholder="请选择">
                      <el-option label="遥测-告警" value="2"></el-option>
                      <el-option label="遥信-告警" value="4"></el-option>

                      <!-- <el-option label="遥测" :value="1"></el-option>
                <el-option label="遥信" :value="3"></el-option> -->
                    </el-select>
                  </el-form-item>
                  <el-form-item label="待选告警标题:">
                    <el-input v-model="querySignalName" placeholder="请输入" clearable>
                      <el-button slot="append" icon="el-icon-search" @click="querySignalData(1, 10)"></el-button>
                    </el-input>
                  </el-form-item>
                </div>

                <el-table :data="cfgSignalNameList" row-key="id" max-height="460" v-loading="signalTableLoading"
                  ref="signalTableData" @selection-change="selectSignalFn" border header-row-class-name="myHeaderClass"
                  size="small">
                  <el-table-column type="selection" width="55" :reserve-selection="true"></el-table-column>
                  <el-table-column prop="id" align="center" label="测点ID" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="label" align="center" label="测点名称" show-overflow-tooltip></el-table-column>
                </el-table>
                <div class="pag" v-if="signTotal > 0">
                  <el-pagination @size-change="handleSignalSizeChange" background
                    @current-change="handleSignalCurrentChange" :current-page="signalcurrentPage"
                    :page-sizes="[10, 20, 50]" :page-size="signalPageSize" layout="total,  prev, pager, next"
                    :total="signTotal">
                  </el-pagination>
                </div>
              </div>
              <div
                style="margin: 0 16px;font-size: 18px;display: flex;flex-direction: column;justify-content: center;align-items: center;">
                <div class="iconWrap" style="padding: 12px 4px;margin-bottom: 16px;cursor: pointer;" @click="addSignal">
                  <i class="el-icon-d-arrow-left" style="transform: rotateZ(180deg);color: #333333;"></i>
                </div>
                <div class="iconWrap" style="padding: 12px 4px;cursor: pointer;" @click="removeSignal">
                  <i class="el-icon-d-arrow-left" style="color: #333333;"></i>
                </div>

              </div>
              <div style="flex:1;margin-right: 16px;">
                <el-form-item label="已选告警标题:"> </el-form-item>
                <div></div>
                <el-table :data="selectedSignalList" max-height="460" @selection-change="selectRemoveSignalFn" border
                  header-row-class-name="myHeaderClass" size="small">
                  <el-table-column type="selection" width="55"></el-table-column>
                  <el-table-column prop="id" align="center" label="测点ID" show-overflow-tooltip></el-table-column>
                  <el-table-column prop="label" align="center" label="测点名称" show-overflow-tooltip></el-table-column>
                </el-table>
              </div>
            </div>
            <div style="display: flex; justify-content: end">
              <el-form-item label="告警级别：" style="margin-right: 0;margin-bottom: 0;">
                <!-- <el-input v-model="deviceThresholdData.HLimit"></el-input> -->
                <el-select v-model="deviceThresholdData.alarmLevel" placeholder="请选择">
                  <el-option v-for="item in warnLevelOptions" :key="item.level" :label="item.name" :value="item.level">
                  </el-option>
                </el-select>
              </el-form-item>
            </div>
          </div>

          <div style="display: flex;justify-content: end;border-top: 1px solid #EBEEF5;">
            <el-button style=" margin-top: 12px;" size="small" @click="goback">取消</el-button>
            <el-button style=" margin-top: 12px;" size="small" @click="sdeviceThresholdConfig"
              :loading="submitWarnLevelLoading" type="primary">确定</el-button>
          </div>




        </el-form>

        <!-- 配置日志 -->
        <div class="detialWrap" v-if="currentModule == 'config'">
          <div class="title">审批详情</div>
          <div class="step-wrap">
            <el-steps :space="200" finish-status="success">
              <el-step v-for="(item, index) in detail.transferInfos"
                :status="[0, 2].includes(item.handleStatus) ? 'success' : item.handleStatus == 3 ? 'finish' : 'wait'"
                :key="index" :title="item.handlerUser || '-'">
                <template slot="description">
                  <div> {{ item.handleStatus == 1 ? '' : item.handleTime }}</div>
                  <div v-if="item.handleStatus == 1">待审批</div>
                  <div> {{ item.transferNodeName || '-' }}</div>
                </template>
              </el-step>
            </el-steps>
          </div>

          <div class="title">流程详情</div>
          <el-descriptions style="width: 100%;" :column="7" direction="vertical" border>
            <el-descriptions-item label="工单编号">{{ detail.orderNum }}</el-descriptions-item>
            <el-descriptions-item label="申请人">{{ detail.applyUser }}</el-descriptions-item>
            <el-descriptions-item label="申请时间">{{ detail.startDate }}</el-descriptions-item>
            <el-descriptions-item label="流程名称">{{ detail.flowName }}</el-descriptions-item>
            <el-descriptions-item label="当前环节名称">{{ detail.nodeName }}</el-descriptions-item>
            <el-descriptions-item label="下一环节名称">{{ detail.nextNodeName }}</el-descriptions-item>
            <el-descriptions-item label="下一环节审批人">{{ detail.nextHandlerUsers }}</el-descriptions-item>
          </el-descriptions>

          <div class="title">
            <span>配置详情</span>
          </div>
          <div style="display: flex;">
            <!-- 批量配置 -->
            <el-table :data="currentRow" header-row-class-name="myHeaderClass" size="small" border>
              <el-table-column prop="cityName" align="center" label="地市" show-overflow-tooltip></el-table-column>
              <el-table-column prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
              <el-table-column prop="siteName" align="center" label="局站" show-overflow-tooltip></el-table-column>
              <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
              <el-table-column prop="devTypeName" align="center" label="设备类型" show-overflow-tooltip></el-table-column>
              <el-table-column prop="devName" align="center" label="设备名称" show-overflow-tooltip></el-table-column>
              <el-table-column prop="label" align="center" label="告警标题" show-overflow-tooltip></el-table-column>
              <el-table-column prop="alarmLevel" align="center" label="告警级别" show-overflow-tooltip></el-table-column>

            </el-table>
          </div>
        </div>

        <!-- 配置详情 -->
        <div v-if="currentModule == 'detail'">
          <el-table :data="currentRow" header-row-class-name="myHeaderClass" size="small" border>
            <el-table-column prop="cityName" align="center" label="地市" show-overflow-tooltip></el-table-column>
            <el-table-column prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
            <el-table-column prop="siteName" align="center" label="局站" show-overflow-tooltip></el-table-column>
            <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
            <el-table-column prop="devTypeName" align="center" label="设备类型" show-overflow-tooltip></el-table-column>
            <el-table-column prop="devName" align="center" label="设备名称" show-overflow-tooltip></el-table-column>
            <el-table-column prop="label" align="center" label="告警标题" show-overflow-tooltip></el-table-column>
            <el-table-column prop="alarmLevel" align="center" label="告警级别" show-overflow-tooltip></el-table-column>
          </el-table>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  data() {
    return {
      warnLevelOptions: [
        { level: 1, name: '一级告警' },
        { level: 2, name: '二级告警' },
        { level: 3, name: '三级告警' },
        { level: 4, name: '四级告警' },
        { level: 5, name: '五级告警' },
        { level: 6, name: '六级告警' },
      ],
      submitWarnLevelLoading: false,
      querySignalName: '',
      suId: '',
      suIp: '',
      role: '',
      cityId: '',
      areeaId: '',
      cityDisabled: true,
      areaDisabled: true,
      currentModule: 'list', // list 列表， add 新增 ，detail 详情，config 配置日志
      detail: null, // 配置日志
      // 查询条件下拉数据
      citylist: [],
      areaList: [],
      siteList: [],
      roomlist: [],
      deviceTypeList: [],
      cedianlist: [],
      cedianNameList: [],
      devoptions: [],

      //阈值设置下拉数据 
      cfgcitylist: [],
      cfgareaList: [],
      cfgsiteList: [],
      cfgroomlist: [],
      cfgdeviceTypeList: [], // 设备类型
      cfgdeviceNameList: [], // 设备名称
      cfgcedianlist: [],// 测点类型
      cfgSignalNameList: [], // 测点名称

      signalPageSize: 10,
      signalcurrentPage: 1,
      signTotal: 0,
      selectedSignalList: [],
      temporarySelectedSignalList: [],
      selectRemoveSignal: [],
      signalTableLoading: false,

      tableLoading: false,
      warningLoading: false,
      hyDataLoading: false,
      arrow: require('./img/arrow.svg'),
      opricon: require('./../views/img/齿轮组.svg'),
      tabActiveName: 'first',
      queryForm: {
        areaId: '',
        cityId: '',
        deviceType: '',
        roomId: '',
        signalName: '',
        signalType: 2,
        siteId: '',
      },

      deviceTableData: [],
      temporarySelectedDeviceData: [],
      selectedDeviceData: [],
      selectRemoveDevicd: [],
      deviceTablepageSize: 10,
      deviceTablecurrentPage: 1,
      deviceTabletotal: 0,
      deviceTableLoading: false,

      deviceConfigTableData: [],
      pageSize: 10,
      currentPage: 1,
      total: 0,
      content: '',
      currentRow: null,

      // 设备阈值配置
      deviceThresholdFlag: false,
      deviceThresholdActive: '当前配置',
      sitePage: 1,
      siteSearch: '',
      roomPage: 1,
      roomSearch: '',
      devTypePage: 1,
      devTypeSearch: '',
      devNamePage: 1,
      devNameSearch: '',
      signalNamePage: 1,
      signalNameSearch: '',
      deviceThresholdData: {
        selectCityIds: "",
        selectAreaIds: [],
        selectSiteIds: [],
        selectRoomIds: [],
        selectDevTypeIds: [],
        selectDevNameIds: [],
        signalTypeIds: ['2', '4'],
        signalNameIds: [],
        alarmLevel: ''
      },
      olddeviceThresholdData: {},
      deviceThresholdHistoryData: [],
      deviceThresholdPageSize: 10,
      deviceThresholdCurrentPage: 1,
      deviceThresholdTotal: 0,

      // 告警级别设置
      warnLevelSetFlag: false,
      warnLevelSetActive: '当前配置',
      warnLevelSetData: {},
      oldwarnLevelSetData: {},
      warnLevelSetHistoryData: [],
      warnLevelPageSize: 10,
      warnLevelCurrentPage: 1,
      warnLevelTotal: 0,

      // 权限按钮
      btnshowHistory: false,
      btnoperateHistory: false,
      btndeviceThreshold: false,
      btnwarnLevelSet: false,
      btnopenDevice: false,
      btncloseDevice: false,
    }
  },
  directives: {  // 在组件中接受一个 directives 的选项
    loadmore: {
      inserted(el, binding) {
        const dom = el.querySelector('.el-select-dropdown .el-select-dropdown__wrap');  // 获取下拉框元素
        dom.addEventListener('scroll', function () {       // 监听元素触底
          const condition = this.scrollHeight - this.scrollTop <= this.clientHeight;
          if (condition) {
            binding.value();
          }
        });
      },
    },
  },
  created() {
    this.role = sessionStorage.getItem('role')
    this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
    this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''
    this.deviceThresholdData.selectCityIds = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
    this.deviceThresholdData.selectAreaIds = sessionStorage.getItem('areaId') ? [sessionStorage.getItem('areaId')] : []
    if (this.role == '1') {
      this.cityDisabled = false
      this.areaDisabled = false
    }

    if (['94', '95'].includes(this.role)) {
      this.cityDisabled = true
      this.areaDisabled = this.deviceThresholdData.selectAreaIds.length ? true : false
    }
    this.queryCityList()
    this.getSite()
    this.getRoom()
    this.getDeviceType()
    this.queryData()
  },
  methods: {
    addDevice() {
      this.selectedDeviceData = [... this.temporarySelectedDeviceData]
      this.querySignalData(1, 10)
    },
    addSignal() {
      this.selectedSignalList = [... this.temporarySelectedSignalList]
    },
    selectRemoveDeviceFn(val) {
      this.selectRemoveDevicd = val
    },
    selectRemoveSignalFn(val) {
      this.selectRemoveSignal = val
    },
    removeDevice() {
      if (!this.selectRemoveDevicd.length) return
      let removeIds = this.selectRemoveDevicd.map(v => v.devSuId)
      this.selectedDeviceData = this.selectedDeviceData.filter(v => !removeIds.includes(v.devSuId))
      // this.temporarySelectedDeviceData = [...this.selectRemoveDevicd]
      this.$refs.deviceTableData.clearSelection();
      this.selectedDeviceData.forEach(row => {
        this.$refs.deviceTableData.toggleRowSelection(row, true);
      });
      this.querySignalData(1, 10)
    },
    removeSignal() {
      if (!this.selectRemoveSignal.length) return
      let removeIds = this.selectRemoveSignal.map(v => v.id)
      this.selectedSignalList = this.selectedSignalList.filter(v => !removeIds.includes(v.id))
      // this.temporarySelectedDeviceData = [...this.selectRemoveDevicd]
      this.$refs.signalTableData.clearSelection();
      this.selectedSignalList.forEach(row => {
        this.$refs.signalTableData.toggleRowSelection(row, true);
      });
    },
    selectDeviceFn(val) {
      this.temporarySelectedDeviceData = val
    },
    selectSignalFn(val) {

      this.temporarySelectedSignalList = val

      // console.log( this.temporarySelectedSignalList)

    },

    // 获取设备列表
    queryDeviceData(pageNum, pageSize) {
      // if (!this.deviceThresholdData.selectCityIds) return this.$message.error('请先选择地市')
      // if (!this.deviceThresholdData.selectDevTypeIds) return this.$message.error('请选择设备类型进行查询')
      this.deviceTableLoading = true
      if (pageNum) this.deviceTablecurrentPage = pageNum
      if (pageSize) this.deviceTablepageSize = pageSize
      let params = {
        cityId: this.deviceThresholdData.selectCityIds ? [this.deviceThresholdData.selectCityIds] : [],
        areaId: this.deviceThresholdData.selectAreaIds,
        siteId: this.deviceThresholdData.selectSiteIds,
        roomId: this.deviceThresholdData.selectRoomIds,
        deviceType: this.deviceThresholdData.selectDevTypeIds,
        deviceName: this.deviceThresholdData.deviceName,
        suId: this.suId,
        suIp: this.suIp,
        current: pageNum || this.deviceTablecurrentPage,
        size: pageSize || this.deviceTablepageSize
      }
      this.$api.getBatchDeviceList(params).then(res => {
        if (res.code == 200) {
          this.deviceTableData = res.data.records.map(v => {
            v.devSuId = v.deviceId + v.suId
            return v
          })
          this.deviceTableLoading = false
          this.deviceTabletotal = res.data.total
        } else {
          this.deviceTableLoading = false
          this.$message.error(res.msg)
        }
      })
    },
    handleDeviceSizeChange(val) {
      this.deviceTablepageSize = val
      this.queryDeviceData()
    },
    handleDeviceCurrentChange(val) {
      this.deviceTablecurrentPage = val
      this.queryDeviceData()
    },

    // 获取测点名称
    querySignalData(pageNum, pageSize) {
      if (!this.selectedDeviceData.length) return this.$message.error('请先选择设备')
      this.signalTableLoading = true
      if (pageNum) this.signalcurrentPage = pageNum
      if (pageSize) this.signalPageSize = pageSize
      let params = {
        suId: [...new Set(this.selectedDeviceData.map(v => v.suId))],
        devId: [...new Set(this.selectedDeviceData.map(v => v.deviceId))],
        type: this.deviceThresholdData.selectDevTypeIds,
        signalType: this.deviceThresholdData.signalTypeIds.length ? this.deviceThresholdData.signalTypeIds : ['2', '4'],
        name: this.querySignalName,
        current: pageNum || this.signalcurrentPage,
        size: pageSize || this.signalPageSize
      }
      this.$api.getBatchSignalList(params).then(res => {
        if (res.code == 200) {
          console.log(res.data.records)
          this.cfgSignalNameList = res.data.records
          this.signalTableLoading = false
          this.signTotal = res.data.total
        } else {
          this.signalTableLoading = false
          this.$message.error(res.msg)
        }
      })
    },
    handleSignalSizeChange(val) {
      this.signalPageSize = val
      this.querySignalData()
    },
    handleSignalCurrentChange(val) {
      this.signalcurrentPage = val
      this.querySignalData()
    },
    async showConfig(row) {
      let params = {
        orderNum: row.orderNum,
        handleUser: row.applyUser
      }

      let res = await this.$api.queryOrderDetail(params)
      if (res.code == 200) {
        //res.data.afterJson = JSON.parse(res.data.afterJson)
        this.detail = res.data

        let currentRow = JSON.parse(row.afterJson).signalList
        currentRow = currentRow.map(v => {
          let obj = {
            ...v,
            alarmLevel: row.alarmLevel
          }
          return obj
        })
        // console.log(row, currentRow)
        this.currentRow = currentRow
        this.content = '配置日志'
        this.currentModule = 'config'
      } else {
        this.$message.error(res.msg)
      }
    },
    showDetial(row) {
      let currentRow = JSON.parse(row.afterJson).signalList
      currentRow = currentRow.map(v => {
        let obj = {
          ...v,
          alarmLevel: row.alarmLevel
        }
        return obj
      })
      // console.log(row, currentRow)
      this.currentRow = currentRow
      this.currentModule = 'detail'
      this.content = '配置详情'
    },
    showAdd() {
      this.deviceThresholdData = {
        selectCityIds: '',
        selectAreaIds: [],
        selectSiteIds: [],
        selectRoomIds: [],
        selectDevTypeIds: [],
        selectDevNameIds: [],
        signalTypeIds: ['2', '4'],
        signalNameIds: [],
        alarmLevel: '',
      }
      this.deviceThresholdData.selectCityIds = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
      this.deviceThresholdData.selectAreaIds = sessionStorage.getItem('areaId') ? [sessionStorage.getItem('areaId')] : []
      if (this.deviceThresholdData.selectCityIds) {
        let params = {
          pid: [this.deviceThresholdData.selectCityIds],
          current: 1,
          size: 999
        }
        this.$api.getBatchArea(params).then(res => {
          if (res.code == 200) {
            this.cfgareaList = res.data.records
          }
        })
      }
      this.getSiteOptions()
      this.getRoomOptions()
      this.getDevTypeOptions()

      this.currentModule = 'add'
      this.content = '设备告警新增'
    },
    batchAreaChange(val) {
      this.getSiteOptions()
    },
    areaRemove() {
      this.deviceThresholdData.selectSiteIds = this.cfgsiteList
        .filter(v => this.deviceThresholdData.selectAreaIds.includes(v.lastId))
        .filter(v => this.deviceThresholdData.selectSiteIds.includes(v.id))
        .map(v => v.id)
      this.cfgsiteList = this.cfgsiteList.filter(v => this.deviceThresholdData.selectAreaIds.includes(v.lastId))
      this.siteRemove()
    },
    batchSiteChange(val) {
      this.getRoomOptions()
    },
    siteRemove() {
      this.deviceThresholdData.selectRoomIds = this.cfgroomlist
        .filter(v => this.deviceThresholdData.selectSiteIds.includes(v.lastId))
        .filter(v => this.deviceThresholdData.selectRoomIds.includes(v.id))
        .map(v => v.id)
      this.cfgroomlist = this.cfgroomlist
        .filter(v => this.deviceThresholdData.selectSiteIds.includes(v.lastId))
      this.roomRemove()
    },
    batchRoomChange(val) {
      this.getDevTypeOptions()
    },
    roomRemove() {
      // this.deviceThresholdData.selectDevTypeIds = this.cfgdeviceTypeList
      //   .filter(v => this.deviceThresholdData.selectRoomIds.includes(v.lastId))
      //   .filter(v => this.deviceThresholdData.selectDevTypeIds == v.id)
      //   .map(v => v.id).join()
      // this.cfgdeviceTypeList = this.cfgdeviceTypeList
      //   .filter(v => this.deviceThresholdData.selectRoomIds.includes(v.lastId))
      // this.devTypeRemove()
    },

    devTypeRemove() {
      // this.deviceThresholdData.selectDevNameIds = this.cfgdeviceNameList
      //   .filter(v => this.deviceThresholdData.selectDevTypeIds.includes(v.devTypeId))
      //   .filter(v => this.deviceThresholdData.selectDevNameIds.includes(v.id))
      //   .map(v => v.id)
      // this.cfgdeviceNameList = this.cfgdeviceNameList
      //   .filter(v => this.deviceThresholdData.selectDevTypeIds.includes(v.devTypeId))
      // this.devIdRemove()
    },

    devIdRemove() {
      this.deviceThresholdData.signalNameIds = this.cfgSignalNameList
        .filter(v => this.deviceThresholdData.selectDevNameIds.includes(v.lastId))
        .filter(v => this.deviceThresholdData.signalNameIds.includes(v.id))
        .map(v => v.id)
      this.cfgSignalNameList = this.cfgSignalNameList
        .filter(v => this.deviceThresholdData.selectDevNameIds.includes(v.lastId))
    },

    // 获取多选区域下拉列表
    getAreaOptions() {
      if (!this.deviceThresholdData.selectCityIds) {
        this.cfgareaList = []
        return
      }
      this.selectedDeviceData = []
      this.cfgSignalNameList = []
      this.selectedSignalList = []
      let params = {
        pid: [this.deviceThresholdData.selectCityIds],
        current: 1,
        size: 999
      }
      this.$api.getBatchArea(params).then(res => {
        if (res.code == 200) {
          this.cfgareaList = res.data.records
          // 删除区县
          this.deviceThresholdData.selectAreaIds = this.cfgareaList
            .filter(v => this.deviceThresholdData.selectAreaIds.includes(v.id))
            .map(v => v.id)
          // 删除局站 
          this.deviceThresholdData.selectSiteIds = this.cfgsiteList
            .filter(v => this.deviceThresholdData.selectAreaIds.includes(v.lastId))
            .filter(v => this.deviceThresholdData.selectSiteIds.includes(v.id))
            .map(v => v.id)
          // 删除机房
          this.deviceThresholdData.selectRoomIds = this.cfgroomlist
            .filter(v => this.deviceThresholdData.selectSiteIds.includes(v.lastId))
            .filter(v => this.deviceThresholdData.selectRoomIds.includes(v.id))
            .map(v => v.id)
          // 删除设备类型与设备名称
          // this.deviceThresholdData.selectDevTypeIds = this.cfgdeviceTypeList
          //   .filter(v => this.deviceThresholdData.selectRoomIds.includes(v.lastId))
          //   .filter(v => [this.deviceThresholdData.selectDevTypeIds].includes(v.id))
          //   .map(v => v.id).join()

          //   console.log(this.deviceThresholdData.selectDevTypeIds)
          // this.deviceThresholdData.selectDevNameIds = this.cfgdeviceNameList
          //   .filter(v => this.deviceThresholdData.selectRoomIds.includes(v.lastId))
          //   .filter(v => this.deviceThresholdData.selectDevNameIds.includes(v.id))
          //   .map(v => v.id)
          // // 删除测点名称
          // this.deviceThresholdData.signalNameIds = this.cfgSignalNameList
          //   .filter(v => this.deviceThresholdData.selectDevNameIds.includes(v.lastId))
          //   .filter(v => this.deviceThresholdData.signalNameIds.includes(v.id))
          //   .map(v => v.id)
        }
      })
    },

    // 获取局站多选
    async getSiteOptions(queryString) {
      let res = await this.$api.getBatchSite({
        name: queryString,
        pid: this.deviceThresholdData.selectAreaIds,
        current: this.sitePage,
        size: 999
      })
      if (res.code == 200) {
        this.cfgsiteList = res.data.records
      } else {
        this.$message.error(res.msg)
      }
    },
    //获取机房多选
    async getRoomOptions(queryString) {
      let res = await this.$api.getBatchRoom({
        name: queryString,
        pid: this.deviceThresholdData.selectSiteIds,
        current: this.roomPage,
        size: 999
      })
      if (res.code == 200) {
        this.cfgroomlist = res.data.records
      } else {
        this.$message.error(res.msg)
      }
    },
    // 设备类型下拉数据选择
    async getDevTypeOptions(queryString) {
      let res = await this.$api.getBatchType({
        name: queryString,
        pid: this.deviceThresholdData.selectRoomIds,
        current: this.devTypePage,
        size: 999
      })
      if (res.code == 200) {
        this.cfgdeviceTypeList = res.data.records
      } else {
        this.$message.error(res.msg)
      }
    },
    async deviceNameQuery(queryString) {
      if (!queryString) return
      let res = await this.$api.deviceNameQuery({ deviceName: queryString, current: 1, size: 999 })
      if (res.code == 200) {
        this.devoptions = res.data.records
      } else {
        this.$message.error(res.msg)
      }
    },
    // 初始化地市
    queryCityList() {
      let params = {
        cityName: "",
        pageNum: 1,
        pageSize: 999
      }
      this.$api.queryCityList(params).then(res => {
        if (res.code == 200) {
          this.citylist = res.data.records
          this.cfgcitylist = res.data.records
          if (this.queryForm.cityId) {
            this.areaList = this.citylist.find(v => v.cityId == this.queryForm.cityId).areaList
          }
        }
      })
    },
    cityChange(val) {
      this.queryForm.areaId = ''
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.areaList = []
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      this.areaList = this.citylist.find(v => v.cityId == val).areaList
      this.getSite()
      this.getRoom()
      this.getDeviceType()
    },
    areaChange() {
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''

      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []

      this.getSite()
      this.getRoom()
      this.getDeviceType()
    },
    siteChange() {
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.roomlist = []
      this.deviceTypeList = []
      this.getRoom()
      this.getDeviceType()
    },
    roomChange() {
      this.queryForm.deviceType = ''
      this.deviceTypeList = []
      this.getDeviceType()
    },


    getSite(val) {
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      let params = {
        cityId: this.queryForm.cityId,
        areaId: this.queryForm.areaId,
        siteName: val,
        pageNum: 1,
        pageSize: 999
      }

      this.$api.querySiteInfo(params).then(res => {
        if (res.code == 200) {
          this.siteList = res.data.records
        }
      })
    },
    getRoom(val) {
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.roomlist = []
      this.deviceTypeList = []
      let params = {
        cityId: this.queryForm.cityId,
        areaId: this.queryForm.areaId,
        siteId: this.queryForm.siteId,
        roomName: val,
        pageNum: 1,
        pageSize: 999
      }
      this.$api.queryRoomInfo(params).then(res => {
        if (res.code == 200) {
          this.roomlist = res.data.records
        }
      })
    },

    async getDeviceType(val) {
      let res = await this.$api.getBatchType({
        name: val,
        // cityId: this.queryForm.cityId ? [this.queryForm.cityId] : [],
        // areaId: this.queryForm.areaId ? [this.queryForm.areaId] : [],
        // siteId: this.queryForm.siteId ? [this.queryForm.siteId] : [],
        // roomId: this.queryForm.roomId ? [this.queryForm.roomId] : [],
        current: 1,
        size: 999
      })
      if (res.code == 200) {
        this.deviceTypeList = res.data.records
      } else {
        this.$message.error(res.msg)
      }
    },


    queryFormReset() {
      this.areaList = []
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []

      this.queryForm = {
        areaId: '',
        cityId: '',
        deviceType: '',
        roomId: '',
        signalName: '',
        signalType: 2,
        siteId: '',
      }
      this.queryData()
      this.role = sessionStorage.getItem('role')
      this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
      this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''

      if (this.role == '1') {
        this.cityDisabled = false
        this.areaDisabled = false
      }

      if (['94', '95'].includes(this.role)) {
        this.cityDisabled = true
      }
      this.queryCityList()
      this.getSite()
      this.getRoom()
      this.getDeviceType()
      this.queryData()
      this.queryData(this.queryForm.current)

    },

    // 监控量
    queryData(pageNum, pageSize) {
      this.tableLoading = true
      if (pageNum) this.currentPage = pageNum
      if (pageSize) this.pageSize = pageSize
      let params = {
        ...this.queryForm,
        current: pageNum || this.currentPage,
        size: pageSize || this.pageSize
      }
      this.$api.getalarmlist(params).then(res => {
        if (res.code == 200) {
          this.deviceConfigTableData = res.data.records.map(v => {
            v.alarmLevel = this.warnLevelOptions.find(s => s.level == v.alarmLevel)?.name
            return v
          })
          this.tableLoading = false
          this.total = res.data.total
        } else {
          this.tableLoading = false
          this.$message.error(res.msg)
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.queryData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.queryData()
    },

    // 返回
    goback() {
      this.currentModule = 'list'
      this.currentRow = null
      this.queryData(1, 10)

    },

    async sdeviceThresholdConfig() {
      if (!this.selectedSignalList.length) return this.$message.error('请选择告警标题')
      if (!this.deviceThresholdData.alarmLevel) {
        return this.$message.error('请选择告警级别')
      }
      let title = `请确认是否修改【告警级别】参数，确认后将发起审批流程`
      this.$confirm(title, '', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
        hideIcon: true
      }).then(async () => {
        this.submitWarnLevelLoading = true
        let afterJson = {
          signalList: this.selectedSignalList
            .map(v => {
              let obj = {
                ...v,
                alarmLevel: this.deviceThresholdData.alarmLevel,
              }
              return obj
            }),

        }
        let params = {
          orderType: 4,
          businessInfoBatchReq: {
            cityId: [...new Set(this.selectedSignalList.map(v => v.cityId))].join(','),
            cityName: [...new Set(this.selectedSignalList.map(v => v.cityName))].join(','),
            areaId: [...new Set(this.selectedSignalList.map(v => v.areaId))].join(','),
            areaName: [...new Set(this.selectedSignalList.map(v => v.areaName))].join(','),
            siteId: [...new Set(this.selectedSignalList.map(v => v.siteId))].join(','),
            siteName: [...new Set(this.selectedSignalList.map(v => v.siteName))].join(','),
            roomId: [...new Set(this.selectedSignalList.map(v => v.roomId))].join(','),
            roomName: [...new Set(this.selectedSignalList.map(v => v.roomName))].join(','),
            devId: [...new Set(this.selectedSignalList.map(v => v.devId))].join(','),
            devName: [...new Set(this.selectedSignalList.map(v => v.devName))].join(','),
            suId: [...new Set(this.selectedSignalList.map(v => v.suId))].join(','),
            devType: [...new Set(this.selectedSignalList.map(v => v.devTypeCode))].join(','),
            devTypeName: [...new Set(this.selectedSignalList.map(v => v.devTypeName))].join(','),
            signalId: [...new Set(this.selectedSignalList.map(v => v.id))].join(','),
            signalName: [...new Set(this.selectedSignalList.map(v => v.label))].join(','),
            signalType: this.deviceThresholdData.signalTypeIds.join(','),
            alarmLevel: this.deviceThresholdData.alarmLevel,
            afterJson: JSON.stringify(afterJson)
          }
        }
        //console.log(params)
        // return
        try {
          let res = await this.$api.orderCreate(params)
          if (res.code == 200) {
            this.goback()
            this.$message.success('保存成功')
          } else {
            this.$message.error(res.msg)
          }
          this.submitWarnLevelLoading = false
        } catch (error) {
          this.submitWarnLevelLoading = false

        }
      }).catch(() => {

      });

    }
  }
}
</script>

<style lang="scss" scoped>
.step-wrap {
  padding: 24px 48px;
  background-color: #fff;
  background: #FAFAFA;
  border-radius: 4px;
}

.iconWrap {
  padding: 4px 20px;
  border-radius: 4px;
  border: 1px solid #CECECE;
}

.ad {
  display: flex;
  gap: 6px;

  i {
    cursor: pointer;
  }
}

.pag {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 12px;
}

.selectWrap {
  width: 600px;
  height: 60px;
  margin-left: 100px;
  overflow: scroll;
  margin-bottom: 16px;
  //box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
  border: 1px solid #d7dae2;
}

.detialWrap {
  padding: 0 24px;

  .title {
    font-size: 14px;
    font-weight: bold;
    margin: 18px 0 12px;
  }
}
</style>