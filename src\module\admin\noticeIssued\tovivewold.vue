<!-- 定制任务页面 -->
<template>
<div class="app-container calendar-list-container disposefiles handFilter">
    <div class="filter-container">
    <div class = "search-box">
    </div>
    <!-- 表格 -->
     <div style="margin-top:10px;">
      
    </div>
    <div class = "listTables martop10" v-loading="lodin">
       <el-table
        :data="tableData"
        border
        @row-click="handletovivew"
        :height="tableheight"
        ref="multipleTable"
        style="width: 100%" :fit = true  >
        
         <el-table-column
          v-for="(row, index) in columnDatas"
          :key="index"
          :prop = "row.field"
          :label = "row.title"
          :min-width="row.columnWidth || 120"
          show-overflow-tooltip
          >
          </el-table-column>
        <!-- <el-table-column align="center" label="操作" width="100">
           <template slot-scope="scope" >
          <el-button    type="text"  size="small"  @click.stop="handletovivew(scope.row)">查看</el-button>
    
        </template>
         </el-table-column> -->

      </el-table>
      <div class = "pagination cleafix tablPaginat" style="margin-top: 15px;margin-bottom:0px; ">
        <el-pagination
            background
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
            :page-sizes="[10,15,20, 50]"
            :current-page.sync = "pageIndex"
            :page-size.sync ="pageSize"
            layout="total, sizes, prev, pager, next, jumper"
            :total="totalCount">
          </el-pagination>
      </div>
    </div>
    <!-- 编辑dilog -->
    <el-dialog :title="dialogtitle" width="1100px" :visible.sync="dialogEditVisible" top="5vh" class="" :append-to-body="false" :close-on-click-modal="true"  >
      <!-- <addEditor :closeDiadl="closeDiadl" ref="addEditor"></addEditor> -->
      <!-- 通告---strat-- -->
      <div class="notcont" v-loading="btnloading">
        <div class="nottop">
          <div class="nottoplef"></div>
          <img  class="logimg" :src="imlog" />
        </div>
        <div class="notcent">
          <p class="notcenth6" v-text="noticeobj.noticeTitle"></p>
          <div class="ql-editor editercont" v-html="noticeobj.noticeInfo">
          </div>
        </div>
        <div class="notbom">
          <span>该文档最后由:</span>
          <span class="notbomsp2">{{noticeobj.createBy}} </span>
          <span>更新于：{{noticeobj.createTime}}</span>
        </div>
      </div>
      <!-- 通告---end---- -->
    </el-dialog>

  </div>
</div>
</template>

<script>

import qs from "qs";
import {
  selNoticePage,//查询获取数据
  selNoticeById,//查看
  postnoticelist,
} from 'api/admin/user/index';
import imlog from "@/assets/login/logotop.png";
export default {
   components: {
    "addEditor": () => import("./addquillEditor"),
  },
  data () {
    return {
      formDatalist: {
        noticeInfo:'',
        titleName:'',
        DATE:[],
      },
      pageIndex: 1,
      pageSize: 10,
      totalCount: 0,
      tablelist:{
         pageIndex: 1,
         pageSize: 10,
         totalCount: 0,
      },
      tableData: [],
      columnDatas: [
          {'field': 'notice_title', title: '公告标题','columnWidth':'150'},
          // {'field': 'notice_title', title: '公告内容','columnWidth':'150'},
          {'field': 'name', title: '发布人','columnWidth':'100'},
          {'field': 'create_time', title: '发布时间','columnWidth':'180'},
      ],
      btnloading:false,
      tableheight:390,
      lodin:false,
      dialogEditVisible:false,
      dialogtitle:'公告',
      noticeobj:{
        'createBy':null,
        'createTime':null,
        'noticeInfo':null,//公告内容
        'noticeTitle':'',//公告标题
      },
      'imlog':imlog,
      listarr:[],
    }
  },
  created(){
    let bodyheight = document.documentElement.clientHeight;
    this.tableheight =  parseInt(bodyheight)>800?757:577;
    this.tablelist.pageSize = parseInt(bodyheight)>800?20:15;
    this.pageSize = this.tablelist.pageSize;

  },
  mounted () {
   // this.queryDateInit()
    this.queryData();
    this.postnoticelistdata();
  },
  computed: {
    
  },
  methods: {
    getdatelist(lastMonthToday){
       let that = this;
    
      let lastMonthYear = lastMonthToday.getFullYear();
      let lastMonth = lastMonthToday.getMonth() + 1 < 10 ? "0" + (lastMonthToday.getMonth() + 1) : lastMonthToday.getMonth() + 1;
      let lastMonthDay =lastMonthToday.getDate < 10 ? "0" + lastMonthToday.getDate : lastMonthToday.getDate();
      let lastMonthKsrq = lastMonthYear + "-" + lastMonth + "-" + lastMonthDay;
       return lastMonthKsrq
    },
    handletovivew(rows){//查看
     let that = this;
      const selectData = this.$refs.multipleTable.selection;
      that.dialogtitle = '公告'
      that.dialogEditVisible = true;
      that.noticeobj.createBy = null;
      that.noticeobj.createTime = null;
      that.noticeobj.noticeInfo = null;//公告内容
      that.noticeobj.noticeTitle = '';//公告标题
      //获取数据
      that.toviewlist(rows.id);

    },
   
    // 分页pagesize
    handleSizeChange (val) {
      this.pageIndex = 1;
      this.pageSize = val;
      this.queryData()
    },
    // 分页pageindex
    handleCurrentChange (val) {
      this.pageIndex = val;
      this.queryData()
    },
    // 查询数据
    queryData () {
      let that = this;
      let lastMonthToday = new Date(
        new Date().getTime() - 30 * 24 * 60 * 60 * 1000
      );
      let tadat = new Date(
        new Date().getTime()
      );
     // console.log(that.formDatalist.DATE);
      let datelist = that.formDatalist.DATE;
      let params =  {
        'page': this.pageIndex,
        'limit': this.pageSize,
        'startTime':that.getdatelist(tadat),//查询开始时间，
        'endTime':that.getdatelist(lastMonthToday),//查询截止时间
       // user_id:that.formDatalist.userName,
      }
     // return;
      that.lodin = true;
     selNoticePage(params).then((res) => {
         // console.log(res)
         if(res.status==200){
           let dat = res.data
            this.totalCount = dat.total;
          this.tableData = dat.rows;

         }
       
         that.lodin = false;
      }).catch((err) => {
       
        that.lodin = false;
      })
    },
     toviewlist(id){
      let that = this;
      let pasmt = {
           'id':id
      }
      that.btnloading = true;
      selNoticeById(pasmt).then((data)=>{
       // console.log(data)
          // that.listQuery.titleName = data.noticeTitle;
          // that.content = data.noticeInfo;
      that.noticeobj.createBy = data.createBy;
      that.noticeobj.createTime = data.createTime;
      that.noticeobj.noticeInfo = data.noticeInfo;//公告内容
      that.noticeobj.noticeTitle = data.noticeTitle;//公告标题
      that.btnloading = false;
     }).catch((error)=>{
       that.btnloading = false;

     })

    },
    postnoticelistdata(){
      let that = this;
      postnoticelist().then( res =>{
        let list = res;
        let liarr = [];
        //list = {'a':1,'b':'2'};
        let obj = {}
        for(var ob in list){
           obj = {
             tit:ob,
             lisarr:list[ob],
           };
           liarr.push(obj)
        }
        that.listarr =liarr;
       // console.log(that.listarr)

      }).catch(err =>{

      })

    },
   
   
  }
}
</script>

<style lang='scss' scoped>
.notcont{
  min-height:200px;
  max-height:570px;
  overflow: auto;
  .nottop{
    height:30px;
    position: relative;
    .nottoplef{
     position: absolute;
      border: dashed 1px #ccc;
      top: 25px;
      right:90px;
      left:0px;
    }
    .logimg{
      float:right;
      width:auto;
      height: 30px;
    }
  }
  .notcent{
    min-height:150px;
    margin-top: 5px;
  }
  .notcenth6{
    font-size:20px;
    font-weight: 550;
    text-align: center;
    padding:5px 0px;
    line-height: 1.4;
    margin:0px;
  }
  .notbom{
    font-size:13px;
    font-weight: 550;
    text-align: right;
    line-height: 30px;
    
    .notbomsp2{
      display: inline-block;
      padding:0px 8px;
    }
  }
}
.editercont{
  padding:0px;
}
</style>
<style lang="scss" >
@import "src/styles/common.scss";
</style>