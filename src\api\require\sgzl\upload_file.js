import http from '@/api/http'

export function selectTbFile(obj) {
  return http({
    url: '/api/watchme/requireUploadCon/selectTbFile',
    method: 'post',
    data: obj
  });
}

export function uploadFile(obj) {
  return http({
    url: '/api/admin/noticeCon/file/upload',
    method: 'post',
    data: obj
  });
}
export function uploadFileDesign(obj) {
  return http({
    url: '/api/watchme/fileCon/uploadFileDesign',
    method: 'post',
    data: obj
  });
}

export function saveFile(obj) {
  return http({
    url: '/api/watchme/fileCon/saveFile',
    method: 'post',
    data: obj
  });
}
export function saveFileDesign(obj) {
  return http({
    url: '/api/watchme/fileCon/saveFileDesign',
    method: 'post',
    data: obj
  });
}

export function saveOrderAddFile(obj) {
  return http({
    url: '/api/watchme/fileCon/saveOrderAddFile',
    method: 'post',
    data: obj
  });
}

export function delFile(obj) {
  return http({
    url: '/api/watchme/fileCon/deleteFile',
    method: 'post',
    data: obj
  });
}

export function deleteFile(obj) {
  return http({
    url: '/api/admin/noticeCon/file/delete',
    method: 'post',
    data: obj
  });
}

export function downFile(obj) {
  return http({
    url: '/api/watchme/fileCon/downloadFile',
    method: 'post',
    data: obj,
    responseType: 'blob'
  });
}

export function getFilesList(obj) {
  return http({
    url: '/api/watchme/fileCon/getFilesList',
    method: 'post',
    data: obj
  });
}

export function commentLists(obj) {
  return http({
    url: '/api/flowable/flowable/processInstance/comments',
    method: 'post',
    data: obj
  });
}

//预览图片 /api/watchme/fileCon/queryImage
export function queryImage(obj) {
  return http({
    url: '/api/watchme/fileCon/queryImage',
    method: 'post',
    data: obj,
  })
}
