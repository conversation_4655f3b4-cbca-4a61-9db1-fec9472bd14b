import http from '@/api/http'

export function fetchTree(query) {
  return http({
    url: '/api/admin/menu/tree',
    method: 'get',
    params: query
  });
}

export function fetchAll() {
  return http({
    url: '/api/admin/menu/all',
    method: 'get'
  });
}
export function addObj(obj) {
  return http({
    url: '/api/admin/menu',
    method: 'post',
    data: obj
  });
}

export function getObj(id) {
  return http({
    url: '/api/admin/menu/' + id,
    method: 'get'
  })
}

export function delObj(id) {
  return http({
    url: '/api/admin/menu/' + id,
    method: 'delete'
  })
}

export function putObj(id, obj) {
  return http({
    url: '/api/admin/menu/' + id,
    method: 'put',
    data: obj
  })
}
export function getStationByUser(query) {
  return http({
    url: '/api/admin/user/getStationByUser',
    method: 'get',
    params: query
  })
}
//删除基站 
export function delStationByUser(obj) {
  return http({
    url: '/api/admin/user/delStationByUser',
    method: 'post',
    data: obj
  })
}
//基站批量 导入
// export function getImportsExcel(query,url1){
//   return http({
//     url: url1,
//     method: 'post',
//     header: {
//       "Content-Type": "application/x-www-form-urlencoded;charset=utf-8"
//     },
//     data: query
//   });
// }
