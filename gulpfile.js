// gulpfile.js
const path = require('path')
const gulp = require('gulp')
const cleanCSS = require('gulp-clean-css')
const cssWrap = require('gulp-css-wrap')
gulp.task('css-wrap', function () {
  //  待打包的element-ui主题文件，临时文件夹，不需修改，不需打包上传
  return gulp.src(path.resolve('./theme/index.css'))
    .pipe(cssWrap({
      // 需要在外面包裹的class类名
      // 按照element-variables.scss变量文件中$--color-primary颜色命名，需要修改
      selector: '.custom-169bfa'
    }))
    .pipe(cleanCSS())
    .pipe(gulp.dest('src/assets/css/theme/169bfa'))
  // 包裹后的css文件夹输出目录，按照element-variables.scss变量文件中
  // $--color-primary颜色命名最后一级目录名，需要修改
})
