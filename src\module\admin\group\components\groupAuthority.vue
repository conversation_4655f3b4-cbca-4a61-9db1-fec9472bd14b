<template>
  <div class=" calendar-list-container disposefiles addentry">
    <div class="filter-container">
      <el-row :gutter="10">
        <el-col :span="24">
          <el-button type="primary" size='small' v-if="groupManager_menu" @click="update">保存</el-button>
        </el-col>
        <el-col :span="8" style='margin-top:10px;'>
          <el-input placeholder="输入关键字进行过滤" v-model="filterText"> </el-input>
          <div class="departree" style="height:450px;">
            <!-- check-strictly  -->
            <el-tree class="filter-tree" check-strictly :data="treeData" show-checkbox highlight-current
              :props="defaultProps" node-key="id" :filter-node-method="filterNode" ref="menuTree"
              @node-click="getNodeData" default-expand-all>
            </el-tree>
          </div>
        </el-col>
        <el-col :span="16" style='margin-top:10px;'>
          <div class="listTables">
            <el-table ref="elementTable" size="small" :data="list" border fit highlight-current-row @select="handleSelectionChange"
              height="485px" style="width: 100%">
              <el-table-column v-if="groupManager_element" type="selection" width="40"> </el-table-column>
              <el-table-column width="200px" align="center" label="资源编码"><template slot-scope="scope">
                  <span>
                    {{ scope.row.code }}</span>
                </template>

              </el-table-column>
              <el-table-column width="200px" align="center" label="资源类型"><template slot-scope="scope">
                  <span>
                    {{ scope.row.type }}</span>
                </template>

              </el-table-column>
              <el-table-column width="200px" align="center" label="资源名称"><template slot-scope="scope">
                  <span>
                    {{ scope.row.name }}</span>
                </template>

              </el-table-column>
              <el-table-column width="200px" align="center" label="资源地址"><template slot-scope="scope">
                  <span>
                    {{ scope.row.uri }}</span>
                </template>
              </el-table-column>
              <el-table-column width="200px" align="center" label="资源请求类型"><template slot-scope="scope">
                  <span>
                    {{ scope.row.method }}</span>
                </template>
              </el-table-column>
            </el-table>
          </div>
        </el-col>

      </el-row>
    </div>
  </div>
</template>
<script>
import {
  fetchTree
} from '@/api/admin/menu/index';
import {
  page
} from '@/api/admin/element/index';
import {
  removeElementAuthority,
  addElementAuthority,
  getElementAuthority,
  modifyMenuAuthority,
  getMenuAuthority
} from '@/api/admin/group/index';
import { mapGetters } from 'vuex';
export default {
  name: 'menulist',
  props: {
    groupId: {
      default: '1'
    }
  },
  data() {
    return {
      filterText: '',
      list: null,
      total: null,
      listQuery: {
        name: undefined
      },
      treeData: [],
      defaultProps: {
        children: 'children',
        label: 'title'
      },
      groupManager_menu: false,
      groupManager_element: false,
      currentId: -1
    }
  },
  watch: {
    filterText(val) {
      this.$refs.menuTree.filter(val);
    }
  },
  created() {
    this.getList();
    this.groupManager_menu = this.elements['groupManager:menu'];
    this.groupManager_element = this.elements['groupManager:element'];
  },
  computed: {
    ...mapGetters([
      'elements'
    ])
  },
  methods: {
    getList() {
      fetchTree(this.listQuery).then(data => {
        this.treeData = data;
        this.initAuthoritys();
      });
    },
    filterNode(value, data) {
      if (!value) return true;
      return data.label.indexOf(value) !== -1;
    },
    getNodeData(data) {
      this.listQuery.menuId = data.id;
      page(this.listQuery).then(response => {
        this.list = response.data.rows;
        getElementAuthority(this.groupId).then(data => {
          const obj = {};
          for (let i = 0; i < this.list.length; i++) {
            obj[this.list[i].id] = this.list[i];
          }
          const toggle = {};
          for (let i = 0; i < data.data.length; i++) {
            const id = data.data[i]
            if (obj[id] !== undefined && toggle[id] === undefined) {
              this.$refs.elementTable.toggleRowSelection(obj[data.data[i]]);
              toggle[id] = true;
            }
          }
        });
      });
      this.currentId = data.id;
      this.showElement = true;
    },
    getTreeNodeKey(node) {
      return node.id;
    },
    handleSelectionChange(val, row) {
      let flag = true;
      for (let i = 0; i < val.length; i++) {
        if (val[i].id === row.id) {
          addElementAuthority(this.groupId, {
            menuId: this.currentId,
            elementId: row.id
          });
          flag = false;
          break;
        }
      }
      if (flag) {
        removeElementAuthority(this.groupId, {
          menuId: this.currentId,
          elementId: row.id
        });
      }
    },
    update() {
      let that = this;
      const nodes = this.$refs.menuTree.getCheckedNodes();
      const ids = [];
      for (let i = 0; i < nodes.length; i++) {
        ids.push(nodes[i].id);
      }

      modifyMenuAuthority(this.groupId, {
        menuTrees: ids.join()
      }).then((data) => {
        if (data.status == '200') {
          that.$message({
            showClose: true,
            message: "保存成功",
            type: "success",
            duration: 1500
          });
          that.$emit('closeAuthorityDialog');
        } else {
          that.$message({
            showClose: true,
            message: "保存失败",
            type: "warning",
            duration: 1500
          });
        }

      });
    },
    initAuthoritys() {
      getMenuAuthority(this.groupId).then(data => {
        const result = [];
        for (let i = 0; i < data.data.length; i++) {
          result.push(data.data[i].id);
        }
        this.$refs.menuTree.setCheckedKeys(result, true);
      });
    }
  }
}
</script>
<style scoped lang="scss">
.departree {
  padding: 10px 5px;
  background-color: #fff;
  overflow: auto;
  border: solid 1px #EBEEF5;
  border-top: none;
}
</style>
<style lang="scss">
// @import "src/styles/common.scss";</style>