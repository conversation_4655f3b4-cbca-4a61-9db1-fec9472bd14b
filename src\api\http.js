import router from '@/router'
import axios from 'axios'
import { Message, MessageBox} from 'element-ui'
import apiConfig from '@/api/axios.config.js'
import { getToken,removelocalStorage} from '@/utils/auth';
import qs from 'qs'
import store from '../store';
const service = axios.create({
  baseURL: apiConfig.commonConfig.baseURL,
  timeout: 1000*60*5
})

// request拦截器
service.interceptors.request.use(config => {
  // Do something before request is sent
  if (store.getters.token) {
    config.headers['Authorization'] = getToken(); // 让每个请求携带token--['Authorization']为自定义key 请根据实际情况自行修改
  }
  return config;
}, error => {
  // Do something with request error
  console.log(error); // for debug
  Promise.reject(error);
})

// respone拦截器
service.interceptors.response.use(
  response => {
    /**
     * 下面的注释为通过response自定义code来标示请求状态，当code返回如下情况为权限有问题，登出并返回到登录页
     * 如通过xmlhttprequest 状态码标识 逻辑可写在下面error中
     */
    const res = response.data;
    if (response.status === 401 || res.status === 40101) {
      Message({
        message: res.msg || error.message,
        type: 'error',
        duration: 2 * 1000,
        showClose: true,
      });
      removelocalStorage();
      router.push('/login')
      store.dispatch('FedLogOut')
      return Promise.reject('error');
    }
   
    if (res.status === 40301 ) { //
      Message({
        message: res.message,
        type: 'error',
        duration: 2 * 1000
      });
      removelocalStorage();
      router.push('/login')
      store.dispatch('FedLogOut')
      return Promise.reject('error');
    }
    if (res.status === 40001) {
      Message({
       // message: '账户或密码错误！',
        message: res.message,
        type: 'warning'
      });
      
      return Promise.reject('error');
    }
    if (response.status !== 200 && res.status !== 200) {
      // Message({
      //   showClose: true,
      //   message: res.message,
      //   type: 'error',
      //   duration: 5 * 1000
      // });
      return response.data;
    } else {
      return response.data;
    }
  },
  error => {
    // console.log(error); // for debug
    Message({
      showClose: true,
      message: error.message || error.error,
      type: 'error',
      duration: 5 * 1000
    });
    
    return Promise.reject(error);
  }
);

export default service
