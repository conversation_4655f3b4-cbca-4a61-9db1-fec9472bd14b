<template>
  <div class="app-container">
    <div v-if="!trendChartFlag">
      <el-form :inline="true" :model="queryForm" size="small" label-width="100px" class="demo-form-inline">
        <el-form-item label="地市:">
          <el-select v-model="queryForm.cityId" :disabled="cityDisabled" @change="cityChange" filterable clearable
            placeholder="请选择">
            <el-option v-for="(item, index) in citylist" v-show="index !== 0" :key="item.cityId" :label="item.cityName"
              :value="item.cityId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="区域:">
          <el-select v-model="queryForm.areaId" :disabled="areaDisabled" @change="areaChange" filterable clearable
            placeholder="请选择">
            <el-option v-for="item in areaList" :key="item.areaId" :label="item.areaName"
              :value="item.areaName"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="局站:">
          <el-select v-model="queryForm.siteId" @change="siteChange" :remote-method="getSite" filterable clearable
            placeholder="请选择">
            <el-option v-for="(item, index) in siteList" filterable :key="`${index}-${item.siteId}`"
              :label="item.siteName" :value="item.siteId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="机房:">
          <el-select v-model="queryForm.roomId" @change="roomChange" :remote-method="getRoom" filterable clearable
            placeholder="请选择">
            <el-option v-for="(item, index) in roomlist" :key="`${index}-${item.roomId}`" :label="item.roomName"
              :value="item.roomId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备类型:">
          <el-select v-model="queryForm.deviceType" @change="devTypeChange" :remote-method="getDeviceType" filterable
            remote clearable placeholder="请输入">
            <el-option v-for="(item, index) in deviceTypeList" :key="`${index}-${item.id}`" :label="item.label"
              :value="item.id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="设备名称:">
          <el-select v-model="queryForm.deviceName" filterable remote clearable placeholder="请输入"
            :remote-method="getDeviceName">
            <el-option v-for="(item, index) in devoptions" :key="`${index}-${item.id}-${item.label}`"
              :label="item.label" :value="item.label">
            </el-option>
          </el-select>
        </el-form-item>

        <el-form-item label="测点类型:">
          <el-select v-model="queryForm.signalType" clearable placeholder="请选择">
            <el-option label="遥测" :value="1"></el-option>
            <el-option label="遥信" :value="3"></el-option>
          </el-select>
          <!-- <el-input v-model="queryForm.signalType" placeholder="请输入" clearable></el-input> -->
        </el-form-item>
        <el-form-item label="测点名称:">
          <!-- <el-input v-model="queryForm.signalName" placeholder="请输入" clearable></el-input> -->
          <el-select v-model="queryForm.signalName" filterable remote clearable placeholder="请输入"
            :remote-method="getSignalIdList">
            <el-option v-for="(item, index) in signalNameList" :key="`${index}-${item}`" :label="item" :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="测点ID:">
          <!-- <el-input v-model="queryForm.signalId" placeholder="请输入" clearable></el-input> -->
          <el-select v-model="queryForm.signalId" filterable remote clearable placeholder="请输入"
            :remote-method="getSignalIdList">
            <el-option v-for="(item, index) in signalIdList" :key="`${index}-${item}`" :label="item" :value="item">
            </el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="时间：">
          <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="date" type="datetimerange" range-separator="至"
            start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>
        <el-form-item label="第一个值:" class="currentValue">
          <el-select 
            v-model="operatorFirstVal" 
            clearable 
            placeholder="请选择" 
            @change="setParams"
          >
            <el-option 
              v-for="(item, index) in permissionList" 
              :key="`${index}`" 
              :label="item.name" 
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-input 
            v-model="firstVal" 
            placeholder="请输入" 
            clearable 
            @input="changeParams" 
            size="small"
          />
        </el-form-item>
        
        <el-form-item label="最后一个值:" class="currentValue">
          <el-select 
            v-model="operatorLastVal" 
            clearable 
            placeholder="请选择" 
            @change="setParams"
          >
            <el-option 
      v-for="(item, index) in permissionList" 
      :key="`${index}`" 
      :label="item.name" 
      :value="item.value"
    >
            </el-option>
          </el-select>
          <el-input 
            v-model="lastVal" 
            placeholder="请输入" 
            clearable 
            @input="changeParams" 
            size="small"
          />
        </el-form-item>
        
        <el-form-item label="平均值:" class="currentValue">
          <el-select 
            v-model="operatorAverageVal" 
            clearable 
            placeholder="请选择" 
            @change="setParams"
          >
            <el-option 
              v-for="(item, index) in permissionList" 
              :key="`${index}`" 
              :label="item.name" 
              :value="item.value"
            >
            </el-option>
          </el-select>
          <el-input 
            v-model="averageVal" 
            placeholder="请输入" 
            clearable 
            @input="changeParams" 
            size="small"
          />
        </el-form-item>
        <el-form-item>
          <el-button style="margin-left: 80px;" @click="queryFormReset">重置</el-button>
          <el-button type="primary" @click="queryData(1, 10)">查询</el-button>
          <el-button type="primary" @click="exportExcel">导出</el-button>
          <!-- <el-button type="primary" @click="performanceExport" :loading="exportDetailloading">导出</el-button> -->
        </el-form-item>
      </el-form>
      <el-table :data="deviceTableData" header-row-class-name="myHeaderClass" size="small" v-loading="tableLoading">
        <el-table-column prop="cityName" align="center" label="地市" show-overflow-tooltip></el-table-column>
        <el-table-column prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
        <el-table-column prop="siteName" align="center" label="局站" show-overflow-tooltip></el-table-column>
        <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
        <!-- <el-table-column prop="deviceTypeName" align="center" label="设备类型" show-overflow-tooltip></el-table-column> -->
        <el-table-column prop="deviceName" align="center" label="设备名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="signalType" align="center" label="测点类型" show-overflow-tooltip>
          <template slot-scope="scope">
            {{ scope.row.signalType == '1' ? '遥测' : scope.row.signalType == '3' ? '遥信' : scope.row.signalType }}
          </template>
        </el-table-column>
        <el-table-column prop="signalId" align="center" label="测点ID" show-overflow-tooltip></el-table-column>
        <el-table-column prop="signalName" align="center" label="测点名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="defineValue" align="center" label="测点值" show-overflow-tooltip></el-table-column>
        <el-table-column prop="firstVal" align="center" label="第一个值" show-overflow-tooltip></el-table-column>
        <el-table-column prop="lastVal" align="center" label="最后一个值" show-overflow-tooltip></el-table-column>
        <el-table-column prop="averageVal" align="center" label="平均值" show-overflow-tooltip></el-table-column>
        <el-table-column prop="reportTime" align="center" label="上报时间" show-overflow-tooltip></el-table-column>
        <el-table-column prop="" label="操作">
          <template slot-scope="scope">
            <div class="ad">
              <el-button type="text" size="small" @click="showTrendPage(scope.row)">趋势分析</el-button>
            </div>
          </template>
        </el-table-column>
      </el-table>
      <div class="pag">
        <el-pagination @size-change="handleSizeChange" background @current-change="handleCurrentChange"
          :current-page="currentPage" :page-sizes="[10, 20, 50]" :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="total">
        </el-pagination>
      </div>
    </div>

    <div v-show="trendChartFlag" style="width: 100%;">
      <el-form :inline="true" :model="htquery" size="small" ref="htquery" class="demo-form-inline">
        <el-form-item label="时间：">
          <el-date-picker value-format="yyyy-MM-dd HH:mm:ss" v-model="htquery.date" type="datetimerange"
            range-separator="至" start-placeholder="开始日期" end-placeholder="结束日期">
          </el-date-picker>
        </el-form-item>

        <el-form-item>
          <el-button @click="resetHistory">重置</el-button>
          <el-button type="primary" @click="showTrend">查询</el-button>
          <el-button v-if="trendChartFlag" type="primary" @click="trendChartFlag = false">返回列表</el-button>
        </el-form-item>
      </el-form>
      <div id="trend" v-loading="trendLoading" style="width: 100%; height: 400px;"></div>

    </div>
  </div>
</template>

<script>
import * as echarts from "echarts";
import dayjs from 'dayjs'
import { mapGetters } from 'vuex';
import exportTableToExcel from './components/exportExcel.js'

export default {
  data() {
    return {
      trendLoading: false,
      exportDetailloading: false,

      // 查询条件下拉数据
      citylist: [],
      areaList: [],
      siteList: [],
      roomlist: [],
      deviceTypeList: [],
      cedianlist: [],
      cedianNameList: [],
      devoptions: [],
      signalIdList: [],
      signalNameList: [],

      date: [dayjs().subtract('1', 'day').format("YYYY-MM-DD HH:mm:ss"), dayjs().format("YYYY-MM-DD HH:mm:ss")],
      tableLoading: false,
      queryForm: {
        areaId: '',
        cityId: '',
        deviceName: '',
        roomId: '',
        signalNamesignalId: '',
        signalName: '',
        signalType: '',
        siteId: '',
        deviceType: '',
        // defineValueMore:0,
        // defineValueLess:0,
        // defineValue:0,
      },
      deviceTableData: [],
      pageSize: 10,
      currentPage: 1,
      total: 0,
      content: '',
      currentRow: null,
      trendChartFlag: false,
      htquery: {
        date: [dayjs().subtract('1', 'day').format("YYYY-MM-DD HH:mm:ss"), dayjs().format("YYYY-MM-DD HH:mm:ss")]
      },
      role: '',
      cityDisabled: true,
      areaDisabled: false,
      currentValue: '',
      permission:'',
      operatorLastVal:'',
      lastVal:'',
      operatorFirstVal:'',
      firstVal:'',
      operatorAverageVal:'',
      averageVal:'',
      permissionList: [
        {
          name: '<',
          value: '1'
        },
        {
          name: '<=',
          value: '2'
        },
        {
          name: '>',
          value: '3'
        },
        {
          name: '>=',
          value: '4'
        },
        {
          name: '=',
          value: '5'
        },
      ],
      currentValueStatus: '',
    }
  },
  computed: {
    ...mapGetters([
      'elements'
    ])
  },
  async created() {


    this.role = sessionStorage.getItem('role')
    this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
    this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''
    // console.log( this.queryForm.areaId)
    if (this.role == '1') {
      this.cityDisabled = false
      this.areaDisabled = false
    }

    if (['94', '95'].includes(this.role)) {
      this.cityDisabled = true
      this.areaDisabled = this.queryForm.areaId ? true : false
    }
    await this.queryData()

    setTimeout(() => {
      this.queryCityList()
      this.getArea()
      this.getSite()
      this.getRoom()
      this.getDeviceType()
      this.getDeviceName()
      this.getSignalIdList()
      this.getSignalNameList()
    }, 1000)


  },
  mounted() {

  },
  methods: {
    clearOtherDefineValues(targetKey) {
      const keysToDelete = ['defineValueMore', 'defineValueLess', 'defineValue'];
      keysToDelete.forEach(key => {
        if (key !== targetKey && this.queryForm[key]) {
          delete this.queryForm[key];
        }
      });
      this.queryForm[targetKey] = this.currentValue;
    },
    changeParams(){
      this.currentValue = this.currentValue.replace(/[^\d]/g, '');
      switch (this.currentValueStatus) {
        case 'more':
          this.clearOtherDefineValues('defineValueMore');
          break;
        case 'less':
          this.clearOtherDefineValues('defineValueLess');
          break;
        case 'equal':
          this.clearOtherDefineValues('defineValue');
          break;
        default:
          break;
      }
    },
    setParams(val){
      switch (val) {
        case 'less':
          this.currentValueStatus = 'less'
          this.changeParams()
          break;
        case 'lessEqual':
          this.currentValueStatus = 'lessEqual'
          this.changeParams()
          break;
        case 'more':
          this.currentValueStatus = 'more'
          this.changeParams()
          break;
        case 'moreEqual':
          this.currentValueStatus = 'moreEqual'
          this.changeParams()
          break;
        case 'equal':
          this.currentValueStatus = 'equal'
          this.changeParams()
          break;
        default:
          break;
      }
    },
    async performanceExport() {
      this.exportDetailloading = true
      let data = [
        ['地市', '区域', '局站', '机房', '设备类型', '设备名称', '测点类型', '测点ID', '测点名称', '测点值', '上报时间']
      ]
      let params = {
        ...this.queryForm,
        startReportTime: this.date[0],
        endReportTime: this.date[1],
        current: 1,
        pageSize: 9999
      }
      try {
        let res = await this.$api.devicePerformance(params)
        if (res.code == 200) {
          res.data.records.forEach(v => {
            let item = [
              v.cityName,
              v.areaName,
              v.stationName,
              v.roomName,
              v.deviceTypeName,
              v.devName,
              v.alarmLevel,
              v.alarmTime,
              v.alarmClearTime,
              v.alarmDuration,
              v.alarmDesc,
              v.alarmDesc,
              v.triggerVal,
            ]
            data.push(item)
          })
          exportTableToExcel(data, '性能统计')
          this.exportDetailloading = false
        } else {
          this.$message.error(res.msg)
          this.exportDetailloading = false
        }
      } catch (error) {
        this.$message.error('导出异常')
        this.exportDetailloading = false
      }

    },
    resetHistory() {
      this.htquery = {
        date: [dayjs().subtract('1', 'day').format("YYYY-MM-DD HH:mm:ss"), dayjs().format("YYYY-MM-DD HH:mm:ss")]
      }
      this.showTrend()
    },

    // 初始化地市
    queryCityList() {
      let params = {
        cityName: "",
        pageNum: 1,
        pageSize: 999
      }
      this.$api.getSignalValuesCitylist(params).then(res => {
        if (res.code == 200) {
          this.citylist = res.data.records
        }
      })
    },
    cityChange(val) {
      // this.queryForm.areaId = ''
      // this.queryForm.siteId = ''
      // this.queryForm.roomId = ''
      // this.queryForm.deviceType = ''
      // this.queryForm.deviceName = ''
      // this.areaList = []
      // this.siteList = []
      // this.roomlist = []
      // this.deviceTypeList = []
      // this.devoptions = []
      this.areaList = this.citylist.find(v => v.cityId == val).areaList
      this.getArea()
      this.getSite()
      this.getRoom()
      this.getDeviceName()
      this.getDeviceType()
    },
    areaChange() {
      // this.queryForm.siteId = ''
      // this.queryForm.roomId = ''
      // this.queryForm.deviceType = ''
      // this.queryForm.deviceName = ''
      // this.siteList = []
      // this.roomlist = []
      // this.deviceTypeList = []
      // this.devoptions = []
      this.getSite()
      this.getRoom()
      this.getDeviceName()
      this.getDeviceType()
    },
    siteChange() {
      // this.queryForm.roomId = ''
      // this.queryForm.deviceType = ''
      // this.queryForm.deviceName = ''
      // this.roomlist = []
      // this.deviceTypeList = []
      // this.devoptions = []
      this.getRoom()
      this.getDeviceName()
      this.getDeviceType()
    },
    roomChange() {
      // this.queryForm.deviceType = ''
      // this.queryForm.deviceName = ''
      // this.deviceTypeList = []
      // this.devoptions = []
      this.getDeviceName()
      this.getDeviceType()
    },
    devTypeChange() {
      // this.queryForm.deviceName = ''
      // this.devoptions = []
      this.getDeviceName()
    },

    getSite(val) {
      // this.queryForm.siteId = ''
      // this.queryForm.roomId = ''
      // this.queryForm.deviceType = ''
      // this.siteList = []
      // this.roomlist = []
      // this.deviceTypeList = []
      // let temp;
      // try {
      //   temp = this.areaList.filter(i => {
      //     return i.areaName == this.queryForm.areaName
      //   })
      //   // this.queryForm.areaId = temp[0].areaId
      // } catch (error) {

      // }      
      let params = {
        cityId: this.queryForm.cityId,
        areaId: this.queryForm.areaId,
        siteName: val,
        pageNum: 1,
        pageSize: 999
      }

      this.$api.getSignalValuesSitelist(params).then(res => {
        if (res.code == 200) {
          this.siteList = res.data.records
        }
      })
    },
    getArea(val) {
      if (!sessionStorage.getItem('areaId')) {
        this.queryForm.areaId = ''
      }
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.areaList = []
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      this.type = ''
      let params = {
        cityId: this.queryForm.cityId,
        areaName: val,
        pageNum: 1,
        pageSize: 999
      }
      this.$api.getAlarmArealist(params).then(res => {
        if (res.code == 200) {
          this.areaList = res.data.records
        }
      })
    },
    getRoom(val) {
      // this.queryForm.roomId = ''
      // this.queryForm.deviceType = ''
      // this.roomlist = []
      // this.deviceTypeList = []
      // let temp;
      // try {
      //   temp = this.areaList.filter(i => {
      //     return i.areaName == this.queryForm.areaName
      //   })
      //   // this.queryForm.areaId = temp[0].areaId
      // } catch (error) {

      // }
      let params = {
        cityId: this.queryForm.cityId,
        areaId: this.queryForm.areaId,
        siteId: this.queryForm.siteId,
        roomName: val,
        pageNum: 1,
        pageSize: 999
      }
      this.$api.getSignalValuesRoomlist(params).then(res => {
        if (res.code == 200) {
          this.roomlist = res.data.records
        }
      })
    },
    async getDeviceName(queryString) {
      // let temp;
      // try {
      //   temp = this.areaList.filter(i => {
      //     return i.areaName == this.queryForm.areaName
      //   })
      //   // this.queryForm.areaId = temp[0].areaId
      // } catch (error) {

      // }
      let res = await this.$api.deviceNameQuery({
        deviceName: queryString,
        cityId: this.queryForm.cityId ? [this.queryForm.cityId] : [],
        areaId: this.queryForm.areaId ? [this.queryForm.areaId] : [],
        siteId: this.queryForm.siteId ? [this.queryForm.siteId] : [],
        roomId: this.queryForm.roomId ? [this.queryForm.roomId] : [],
        deviceType: this.queryForm.deviceType ? [this.queryForm.deviceType] : [],
        current: 1,
        size: 999
      })
      if (res.code == 200) {
        this.devoptions = res.data.records
      } else {
        this.$message.error(res.msg)

      }
    },

    getSignalNameList(val) {
      let params = {
        columnName: 'signal_name',
        columnValue: val,
        pageNum: 1,
        pageSize: 999
      }
      this.$api.getSignalValuesColumnNamelist(params).then(res => {
        if (res.code == 200) {
          this.signalNameList = res.data.records
        }
      })
    },
    getSignalIdList(val) {
      let params = {
        columnName: 'signal_id',
        columnValue: val,
        pageNum: 1,
        pageSize: 999
      }
      this.$api.getSignalValuesColumnNamelist(params).then(res => {
        if (res.code == 200) {
          this.signalIdList = res.data.records
        }
      })
    },
    async getDeviceType(val) {
      // let temp;
      // try {
      //   temp = this.areaList.filter(i => {
      //     return i.areaName == this.queryForm.areaName
      //   })
      //   // this.queryForm.areaId = temp[0].areaId
      // } catch (error) {

      // }
      let res = await this.$api.queryDeviceType({
        deviceType: val,
        cityId: this.queryForm.cityId ? [this.queryForm.cityId] : [],
        areaId: this.queryForm.areaId ? [this.queryForm.areaId] : [],
        siteId: this.queryForm.siteId ? [this.queryForm.siteId] : [],
        roomId: this.queryForm.roomId ? [this.queryForm.roomId] : [],
        current: 1,
        size: 999
      })
      if (res.code == 200) {
        this.deviceTypeList = res.data.records
      } else {
        this.$message.error(res.msg)
      }
    },
    queryFormReset() {
      location.reload()
      // this.areaList = []
      // this.siteList = []
      // this.roomlist = []
      // this.deviceTypeList = []
      // this.signalIdList = []
      // this.devoptions = []

      // this.queryForm = {
      //   areaId: '',
      //   cityId: '',
      //   deviceName: '',
      //   roomId: '',
      //   signalId: '',
      //   signalName: '',
      //   signalType: '',
      //   siteId: '',
      // }
      // this.date = [dayjs().subtract('1', 'day').format("YYYY-MM-DD HH:mm:ss"), dayjs().format("YYYY-MM-DD HH:mm:ss")]
      // this.queryData()
      // this.role = sessionStorage.getItem('role')
      // this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
      // this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''

      // if (this.role == '1') {
      //   this.cityDisabled = false
      //   this.areaDisabled = false
      // }

      // if (['94', '95'].includes(this.role)) {
      //   this.cityDisabled = true
      // }
      // setTimeout(() => {
      //   this.queryCityList()
      //   this.getArea()
      //   this.getSite()
      //   this.getRoom()
      //   this.getDeviceType()
      //   this.getDeviceName()
      //   this.getSignalIdList()
      //   this.getSignalNameList()
      // }, 1000)
      // this.queryData(this.queryForm.current)

    },
    showTrendPage(row) {
      this.currentRow = row
      this.trendChartFlag = true
      this.showTrend()
    },
    // 趋势图
    async showTrend() {
      this.trendLoading = true

      // myChart.showLoading()
      let xData = []
      let Data = []
      let params = {
        suId: this.currentRow.suId,
        devId: this.currentRow.deviceId,
        isPage: 'false',
        signalId: this.currentRow.signalId,
        createTimeStart: this.htquery.date[0] || '',
        createTimeEnd: this.htquery.date[1] || '',
        current: 1,
        size: 99999
      }
      let res = await this.$api.devicePmHistory(params)
      if (res.code == 200) {
        res.data.forEach(v => {
          xData.unshift(dayjs(v.report_time).format("YYYY-MM-DD HH:mm:ss"))
          Data.unshift(v.define_value)
        })
      } else {
        this.$message.error(res.msg)
      }
      this.trendLoading = false

      this.$nextTick().then(() => {
        // 初始化echarts实例
        const myChart = echarts.init(document.getElementById('trend'));

        // 图表配置项
        const options = {
          title: {
            text: this.currentRow.signalName,

            left: "center",
            textStyle: {
              color: "#7b93a7",
              fontSize: 16,
              align: "center",
            },
          },
          tooltip: {
            show: true,
          },
          xAxis: {
            type: 'category',
            boundaryGap: false,
            data: xData,
            axisLabel: {
              width: 50
            }
          },
          dataZoom: [
            {
              type: 'inside',
              start: 90,
              end: 100
            },
            {
              start: 90,
              end: 100
            }
          ],
          yAxis: {
            type: 'value',

          },
          series: [
            {
              type: 'line',
              data: Data
            },
          ],
        };
        //  myChart.hideLoading()

        // 使用配置项显示图表
        myChart.setOption(options);
      })
    },
    // 监控量
    queryData(pageNum, pageSize) {
  // 验证查询条件
  if (!this.validateQueryConditions()) {
    return
  }
  
  this.tableLoading = true
  if (pageNum) this.currentPage = pageNum
  if (pageSize) this.pageSize = pageSize
  
  let params = {
    ...this.queryForm,
    startReportTime: this.date[0],
    endReportTime: this.date[1],
    operatorLastVal: this.operatorLastVal,
    lastVal: this.lastVal,
    operatorFirstVal: this.operatorFirstVal,
    firstVal: this.firstVal,
    operatorAverageVal: this.operatorAverageVal,
    averageVal: this.averageVal,
    pageNum: pageNum || this.currentPage,
    pageSize: pageSize || this.pageSize
  }
  
  this.$api.devicePerformance(params).then(res => {
    if (res.code == 200) {
      this.deviceTableData = res.data.records
      this.tableLoading = false
      this.total = res.data.total
    } else {
      this.tableLoading = false
      this.$message.error(res.msg)
    }
  })
},

// 新增验证方法
validateQueryConditions() {
  // 检查每个条件组是否完整（要么都为空，要么都有值）
  const lastValGroup = [this.operatorLastVal, this.lastVal]
  const firstValGroup = [this.operatorFirstVal, this.firstVal]
  const averageValGroup = [this.operatorAverageVal, this.averageVal]
  
  // 检查每组条件是否完整
  const isLastValValid = this.checkConditionGroup(lastValGroup)
  const isFirstValValid = this.checkConditionGroup(firstValGroup)
  const isAverageValValid = this.checkConditionGroup(averageValGroup)
  
  if (!isLastValValid || !isFirstValValid || !isAverageValValid) {
    this.$message.warning('请确保最大值/最小值/平均值的符号和数据完整！')
    return false
  }
  
  return true
},

// 辅助方法：检查一组条件是否有效
checkConditionGroup([operator, value]) {
  // 如果两个都为空，则有效
  if (!operator && !value) return true
  
  // 如果两个都有值，则有效
  if (operator && value) return true
  
  // 否则无效
  return false
},
    handleSizeChange(val) {
      this.pageSize = val
      this.queryData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.queryData()
    },
    exportExcel() {
      // 导出所有
      this.$confirm("是否确认导出当前所有报表?", "警告", {
        confirmButtonText: "确定",
        cancelButtonText: "取消",
        type: "warning",
      }).then(async () => {

        const data = []
        let temp;
        try {
          temp = this.areaList.filter(i => {
            return i.areaName == this.queryForm.areaName
          })
          // this.queryForm.areaId = temp[0].areaId
        } catch (error) {

        }
        let params = {
          ...this.queryForm,
          ...temp[0],
          startReportTime: this.date[0],
          endReportTime: this.date[1],
          operatorLastVal: this.operatorLastVal,
          lastVal: this.lastVal,
          operatorFirstVal: this.operatorFirstVal,
          firstVal: this.firstVal,
          operatorAverageVal: this.operatorAverageVal,
          averageVal: this.averageVal,
          pageNum: this.currentPage,
          pageSize: this.total
        }
        await this.$api.devicePerformance(params).then(res => {
          data.push(...res.data.records)
        })
        if (data.length) {
          const fileName = '性能统计';
          const excelData = [
            ['地区id', '地区', '地市id', '地市', '时间', "define_value", "设备id", '设备', '设备类型', 'remark', '报表时间', 'roomID', "room", "signalId", 'signal', 'signal类型', '站点id', "站点", "suID"]//导出表头
          ]; // 表格表头
          data.forEach((item, index) => {
            let rowData = [];
            //导出内容的字段
            rowData = [
              item.areaId,
              item.areaName,
              item.cityId,
              item.cityName,
              item.createTime,
              item.defineValue,
              item.deviceId,
              item.deviceName,
              item.deviceType,
              item.remark,
              item.reportTime,
              item.roomId,
              item.roomName,
              item.signalId,
              item.signalName,
              item.signalType,
              item.siteId,
              item.siteName,
              item.suId
            ];
            excelData.push(rowData);
          });
          console.log(excelData);
          exportTableToExcel(excelData, fileName)
        } else {
          this.$message({
            type: 'error',
            message: '请确保导出的报表数量不为0'
          });
        }
      });
    },
  }
}
</script>

<style lang="scss" scoped>
.ad {
  display: flex;
  gap: 6px;

  i {
    cursor: pointer;
  }
}

.pag {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 12px;
}

.selectWrap {
  width: 600px;
  height: 60px;
  margin-left: 100px;
  overflow: scroll;
  margin-bottom: 16px;
  //box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
  border: 1px solid #d7dae2;
}
.app-container{
  .el-form{
    .currentValue{
      display: flex;
      width: 30%;
      float: left;
      ::v-deep(.el-form-item__content){
        display: flex;
        .el-select{
          width: 30%;
          .el-input{
          width: 100%;
          .el-input__inner{

          }
        }
        }
        .el-input{
          width: 70%;
          .el-input__inner{

          }
        }
      }
    }
  }
}
</style>