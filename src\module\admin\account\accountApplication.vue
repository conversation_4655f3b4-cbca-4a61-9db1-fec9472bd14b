<template>
  <!--工单查询-->
  <div class="app-container disposefiles handFilter ">
    <div class="filter-container">
    <el-form   :model="listQuery"  ref="sizeForm" label-position="right" label-width="110px" label-suffix = ":"  class="demo-form-inline applyform">
       <el-row :gutter="10">
       
        <el-col :span="20">
        <el-form-item label="申请附件" >
         <el-upload class="upload-demo"
          style="margin-right: 10px;height:60px;"
            action=""
            ref="upload"
            :on-change="handleChangeFile"
            :http-request="myUploadFile"
            :multiple="false"
            :accept="this.acceptFile('excel')"
            :limit="1"
            :file-list="fileListFile">

        <el-button size="small" type="primary">申请附件</el-button>
         <!-- <div slot="tip" class="el-upload__tip ">只能上传.xlsx或.xls文件</div> -->
        <div slot="tip" class="el-upload__tip uploawar">只能上传.xlsx或.xls文件,不超过30M</div>
        </el-upload>
        <!-- <div class="errdv" >
          <div v-show="erromasg">
          <p>导入失败信息：</p>
          <p class="errdvp1">{{erromasg}}</p>
          </div>
          </div> -->
        </el-form-item>
       </el-col>
        <el-col :span="24" >
         <el-form-item class="formbtn text-center">
          
         <el-button split-button type="primary" @click="submitUpload" :loading="formloding" >提交 </el-button>
          <el-button split-button type="primary" @click="Uploadfilet" >下载模板</el-button>
        </el-form-item>
        </el-col>
        <el-col :span="24" class="errdv" style="padding-left:100px;">
          <div v-show="erromasg">
          <p>提交失败信息：</p>
          <p class="errdvp1">{{erromasg}}</p>
          </div> 
        </el-col>
       
      </el-row>
    </el-form>
    <div class="zlistTablecom listTables martop10">
      <el-table
        stripe
        border
        :fit="true"
        highlight-current-row
        max-height="570px"
        :data="tableData"
        style="width: 100%">
        <el-table-column type="index" align="left" width="50" label="序号"></el-table-column>
        <el-table-column align="left" prop="file_name" label="文件名称" min-width="200%">
        </el-table-column>
        <el-table-column align="left" label="上传人">
          <template slot-scope="scope">
            <span>{{ scope.row.name }}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="上传时间">
          <template slot-scope="scope">
            <span>{{ scope.row.create_time }}</span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="文件类型">
          <template >
            <span></span>
          </template>
        </el-table-column>
        <el-table-column align="left" label="操作">
          <template slot-scope="scope">
            <a @click="handleDelFile(scope.row)"
               style="text-decoration: underline; color: red; font-weight: bold; font-style: italic;">删&nbsp;除</a>
          </template>
        </el-table-column>
      </el-table>
     </div>
  
  </div>
  </div>
</template>

<script>

  import {mapGetters} from 'vuex'
  import {downFile} from '@/api/require/sgzl/upload_file';
  import {getFileOrderNum,doStartAccNumApply} from '@/api/admin/department/index';
  import {uploadFile, saveOrderAddFile,deleteFile} from '@/api/require/sgzl/upload_file';
  import {getFilesTabObj} from '@/api/form/form/index';
  export default {
    name: '',
    components: {},
    component: {

    },
    data() {
      return {
        listQuery:{
          createUser:this.$store.state.user.name,
          createUserId:this.$store.state.user.staffId,
          stratTime:null,
        },
        loadProgress:0,
        progressFlag:false,
        fileListFile: [],
        fileOrderNum:null,//获取上传的key
        erromasg:'',
        fileId:null,
        tableData:[],
        formloding:false,
        
      }
    },
    created(){
        this.getFileOrderNumlist();
    },
    mounted() {
     
    

    },
    methods: {
    acceptFile(e) {
      const allowHook = {
        video: '.mp4, .ogv, .ogg, .webm',
        audio: '.wav, .mp3, .ogg, .acc, .webm, .amr',
        file: 'doc,.docx,.xlsx,.xls,.pdf',
        excel: '.xlsx,.xls',
        img: '.jpg, .jpeg, .png, .gif'
        }
      if (e) {
        return allowHook[e];
      }
      let srt = null
      for (const k in allowHook) {
        srt += allowHook[k]
      }
      return srt
    },
  
    submitUpload() {//提交
       if(!this.fileOrderNum) {
        this.$message.warning('请选择上传文件');
        return;
       }
       this.subimtmi();
       // this.$refs.upload.submit();
    //   this.getFileOrderNumlist();
    },
    handleChangeFile(file, fileList) {
     // console.log(file)
      if (file.status === "ready") {
       
        if (fileList.length > 1) {
            fileList.splice(0, 1);
        }
        this.fileListFile = fileList;
      }
    },
    
   subimtmi() {
       let that = this;
       let pams ={
            'creat_by':that.listQuery.createUserId,
            'fileOrderNum':that.fileOrderNum,
       }
       that.formloding = true;
       that.erromasg= '';
        doStartAccNumApply(pams).then(response => {
          //  that.fileOrderNum = null;
          that.formloding = false;
           if(response.flag == '0'){
              
              this.$message.success(response.msg);
             // this.orderNum = response.message;
           }else if(response.flag == '1'){
             //  this.$message.warning(response.msg);
               that.erromasg = response.msg;
           }
        }).catch(err =>{
         // that.fileOrderNum = null;
        //  that.erromasg = err;
         that.formloding = false;
        })
    },
    getFileOrderNumlist(file){//获取上传的key
      let that = this;
      that.fileOrderNum = null;
      getFileOrderNum().then(data =>{
        
        that.fileOrderNum = data //data.fileOrderNum;
        if(that.fileOrderNum){
          // that.myUploadFile(file)
        }else{
          this.$message.warning('生成序列号失败。请重新上传附件！！！！');
        }
       
       
      }).catch(err =>{
         this.$refs.upload.clearFiles();
      })
        
    },
    myUploadFile(file) {
    //  console.log(file)
      let that = this;
      let filSiz = file.file;
          filSiz = filSiz.size;
       let filnum = parseFloat(filSiz/1024/1024);
        if(filnum>30){
          this.$message({
              message: '请上传小于30M的文件！！！',
              showClose: true,
              type: 'error',
              duration: 2000
            });
            this.$refs.upload.clearFiles();
            return;
        }
      let fd = new FormData();
      fd.append('file', file.file);//传文件
      fd.append('userId', this.listQuery.createUserId);//传其他参数
    //  fd.append('requireId', this.orderNum)
      //1.上传文件
      uploadFile(fd).then(response => {
          if(JSON.parse(JSON.stringify(response)).result === '2'){
            this.$message({
              title: '失败',
              message: '保存附件失败，失败原因：'+JSON.parse(JSON.stringify(response)).msg,
              showClose: true,
              type: 'error',
              duration: 2000
            });
          
            this.$refs.upload.clearFiles();
            return;
          }
          //2.获取文件fileId
          let fileId = JSON.parse(JSON.stringify(response)).fileId;

          //3.保存附件到业务表(tm_order_add)
          let saveObj = {
            relaId: that.fileOrderNum,
           // orderNum: this.orderNum.orderNum,
            attrId:'dssq',
            attrValue: fileId,
            remark: this.listQuery.createUserId
          };
          saveOrderAddFile(saveObj).then(response => {
              if (response.flag === "1") {
                this.$message({
                  title: '成功',
                  message: '保存附件成功',
                  showClose: true,
                  type: 'success',
                  duration: 2000
                });
                this.$refs.upload.clearFiles();
              
               that.getFilesTabeData();
              } else if (response.flag === "0") {
                this.$message({
                  title: '错误',
                  message: '保存附件失败...',
                  showClose: true,
                  type: 'error',
                  duration: 2000
                });
             
                this.$refs.upload.clearFiles();
              }
            });
        });
    },
    Uploadfilet(){//下载模板
    let that = this;
    let data = {fileId:'2016'}
     downFile(data).then(response => {
        
          var blob = new Blob([response], {type: "application/vnd.ms-excel;charset=utf-8"});
         
          if (window.navigator.msSaveOrOpenBlob) {//msSaveOrOpenBlob方法返回bool值
            navigator.msSaveBlob(blob, '账号申请'+ '.xlsx');//本地保存
          } else {
            var link = document.createElement('a');//a标签下载
            link.href = window.URL.createObjectURL(blob);
            link.download = '账号申请'+ '.xlsx';
            link.click();
            window.URL.revokeObjectURL(link.href);
          }
        }).catch((error) => {
       // console.log(error)
      })

    },
     getFilesTabeData() {//查询附件
      //let orde = this.orderNum.split(',') || [];
     // console.log(12345)
      let that = this;
      let obj = {orderNum:that.fileOrderNum}
      getFilesTabObj(obj).then(response => {
          let json = JSON.parse(JSON.stringify(response));
          this.tableData = json.selectFilesTableData;
        });
    },
    handleDelFile(obj) {
      let that = this;
      let data = {
        requireId:that.fileOrderNum,
        fileId: obj.file_id,
        userId: this.listQuery.createUserId
      };
       deleteFile(data).then(response => {
          if (response.flag === "1") {
            this.$message({
              title: '成功',
              message: '删除附件成功',
              showClose: true,
              type: 'success',
              duration: 2000
            });
            this.getFilesTabeData();
          } else if (response.flag === "0") {
            this.$message({
              title: '错误',
              message: '删除附件失败...',
              showClose: true,
              type: 'error',
              duration: 2000
            });
          }
        });
    },

    }
  }
</script>
<style scoped lang="scss">
.applyform{
    width:80%;
    margin:0px auto;
    background-color:#fff;
    padding:20px 10px;
}
.errdv{
  min-height:50px;
  color:#f56c6c;
  font-size:14px;
  text-align:left;
  line-height:1.4;
  p{
    margin:0px;
  }
  .errdvp1{
    padding-left:10px;
  }
}
</style>
<style lang="scss" >
// @import "src/styles/common.scss";



</style>
