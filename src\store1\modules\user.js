import {
  loginByEmail,
  logout,
  getInfo,
  getMenus,
  getfirstTopMenus,
  getUserRouter,//拉取路由表
} from '@/api/login';

import {
  getToken,
  setToken,
  removeToken,
  removelocalStorage,
  setSysUserId,
  getSysUserId,
  setSysProvCode,
  setSysCityCode,
  getSysCityCode,
  getSysProvCode,
  setSyspermiss,
  getSyspermiss,
  setUserName,
  getUserName,
  getBtnactive,
  setUserId,
  setUserIds,
  setuserpass

} from '@/utils/auth';
import Btns from "@/utils/events";
import Vue from 'vue';
const user = {
  state: {
    user: '',
    status: '',
    code: '',
    token: getToken(),
    name: getUserName() ? getUserName() : '',
    avatar: '',
    introduction: '',
    roles: [],
    menus: undefined,//JSON.parse(sessionStorage.getItem('menuslist')) ? JSON.parse(sessionStorage.getItem('menuslist')) :undefined,
    elements:undefined,//JSON.parse(sessionStorage.getItem('elements')) ? JSON.parse(sessionStorage.getItem('elements')) :undefined,
    permissionMenus: undefined,//JSON.parse(sessionStorage.getItem('permissionMenus')) ? JSON.parse(sessionStorage.getItem('permissionMenus')) :undefined,
    firstMenlist:JSON.parse(localStorage.getItem('firstTopMenlist')) ? JSON.parse(localStorage.getItem('firstTopMenlist')) :[],
    planId: undefined,
    setting: {
      articlePlatform: []
    },
    provCode: getSysProvCode() ? getSysProvCode() :'',
    cityCode:getSysCityCode() ? getSysCityCode() : '',
    permisslevel: getSyspermiss() ? getSyspermiss() : '',
    staffId: getSysUserId() ? getSysUserId() : '',
    allRouterlist:'',//路由
  },

  mutations: {
    SET_CODE: (state, code) => {
      state.code = code;
    },
    SET_TOKEN: (state, token) => {
      state.token = token;
    },
    SET_INTRODUCTION: (state, introduction) => {
      state.introduction = introduction;
    },
    SET_SETTING: (state, setting) => {
      state.setting = setting;
    },
    SET_STATUS: (state, status) => {
      state.status = status;
    },
    SET_NAME: (state, name) => {
      state.name = name;
    },
    STAFF_ID: (state, staffId) => {
      state.staffId = staffId;
    },
    SET_AVATAR: (state, avatar) => {
      state.avatar = avatar;
    },
    SET_ROLES: (state, roles) => {
      state.roles = roles;
    },
    SET_MENUS: (state, menus) => {
      state.menus = menus;
      //存菜单
      sessionStorage.setItem('menuslist',JSON.stringify(state.menus))
    },
    SET_ELEMENTS: (state, elements) => {
      state.elements = elements;
      //存菜单
      sessionStorage.setItem('elements',JSON.stringify(state.elements));
    },
    LOGIN_SUCCESS: () => {
      console.log('login success')
    },
    LOGOUT_USER: state => {
      state.user = '';
    },
    SET_PERMISSION_MENUS: (state, permissionMenus) => {
    // console.log(permissionMenus)

      let permMenus = permissionMenus;
      let preIndex = getBtnactive();
      if(preIndex && permMenus){
       // (preIndex == '1' || preIndex == '2' )?preIndex=preIndex:preIndex=0;
      // (preIndex !='7' )?preIndex=preIndex:preIndex=0;
     //  console.log(permMenus[preIndex])
       let menarr = [];
          for(let itm in permMenus){
          
          let item = permMenus[itm];
         // console.log(item)
           if(item.id == preIndex){
            menarr = item.children;
           
            break;
           }
          if(item.children.length){
               let arr1 = item.children;
               let arr2 =  arr1.filter((ite)=>{
                 return ite.id == preIndex;
               });
               menarr = arr2.length>0?arr2[0].children:[];
            }
           if(menarr.length>0){
            break;
           }
           
          }
       state.permissionMenus = menarr //preIndex=='6'? permMenus[0].children : permMenus[1].children;
      }else{
       state.permissionMenus ='';
      }
    //  console.log(permissionMenus)
    //  sessionStorage.setItem('permissionMenus',JSON.stringify(state.permissionMenus))
    sessionStorage.setItem('permissionMenus',JSON.stringify(permissionMenus));

    },
    SET_FIRSTTOP_MENUS:(state, firstTopMenus) => {
      let permMenus = firstTopMenus;
      let firstMenlist = [];
      let obj;
      permMenus.forEach((item,index)=>{
        let child = item.children;
        let arr1 = [];
        child.forEach((ite,inde)=>{
          let obj1 = {};
          obj1 = {
            id:ite.id,
            icoClass:ite.icon,
            icontyp:ite.attr3,
            btnName:ite.title,
            maintyp:ite.attr2,
            attr4:ite.attr4,
            path:ite.path,
            parentId:item.id
          };
          arr1.push(obj1)
        })
          obj = {
                    id:item.id,
                    icoClass:item.icon,
                    icontyp:item.attr3,
                    btnName:item.title,
                    maintyp:item.attr2,
                    attr4:item.attr4,
                    path:item.path,
                    children:arr1,
                };
          firstMenlist.push(obj)
      });
    //  console.log(firstMenlist)
    localStorage.setItem('firstTopMenlist',JSON.stringify(firstMenlist));
    state.firstMenlist = firstMenlist; 
    // console.log(JSON.parse(localStorage.getItem('firstTopMenlist')))

  },
    GET_PERMISSION_MENUS:(state, preIndex)=>{
      let perMenus =  sessionStorage.getItem('permissionMenus');
          perMenus = JSON.parse(perMenus);
         // console.log(perMenus)
         // console.log(preIndex)
          let menarr = [];
          for(let itm in perMenus){
          
          let item = perMenus[itm];
         // console.log(item)
           if(item.id == preIndex){
            menarr = item.children;
           
            break;
           }
          if(item.children.length){
               let arr1 = item.children;
               let arr2 =  arr1.filter((ite)=>{
                 return ite.id == preIndex;
               });
               menarr = arr2.length>0?arr2[0].children:[];
            }
           if(menarr.length>0){
            break;
           }
           
          }
       // console.log(menarr)
      perMenus = menarr // perMenus[preIndex].children;
      state.permissionMenus = perMenus;
    },
    setplanId: (state, planId) => {
      state.planId = planId;
    },
    SET_PROVCODE:(state,provCode) =>{
      state.provCode = provCode;
    },
    SET_CITYCODE:(state,cityCode) =>{
      state.cityCode = cityCode;
    },
    SET_PERMISSION_LEVEl:(state,level) =>{
      state.permisslevel = level;
    },
    
  },

  actions: {
    // 邮箱登录
    LoginByEmail({
      commit
    }, userInfo) {
      const username = userInfo.username.trim();
      const userpass = userInfo.password;
      commit('SET_TOKEN', '');
      commit('SET_ROLES', []);
      commit('SET_MENUS', undefined);
      commit('SET_ELEMENTS', undefined);
      removeToken();
      //清除所有的本地存储数据
      removelocalStorage();
      return new Promise((resolve, reject) => {
        loginByEmail(username, userInfo.password).then(response => {
        
          setToken(response.data);
          setuserpass(userInfo.password);
          setUserIds(response.id);
          //登录 存userID
          setSysUserId(response.userId);
          setUserId(username);
         // setSysProvCode(response.province);
          // let citycodes = response.departCity?response.departCity:'';
          //     username == 'admin'?citycodes=750:'';
          //  setSysCityCode(citycodes);

         // setSyspermiss(response.role);
          setUserName(response.name);
        //  commit('SET_PROVCODE',response.province);
         // commit('SET_CITYCODE',response.city);
        // commit('SET_CITYCODE',citycodes);
          commit('SET_TOKEN', response.data);
          commit('STAFF_ID',response.userId);
         // commit('SET_PERMISSION_LEVEl',response.role);
          commit('SET_NAME', response.name);
        
          resolve(response);
        }).catch(error => {
          reject(error);
        });
      });
    },

    // 获取用户信息
    GetInfo({
      commit,
      state
    }) {
      return new Promise((resolve, reject) => {
        getInfo(state.token).then(response => {
          const data = response;
          commit('SET_ROLES', '');
         // commit('SET_NAME', data.name);
         //    http://git.oschina.net/uploads/42/547642_geek_qi.png?1499487420 https://ss3.bdstatic.com/70cFv8Sh_Q1YnxGkpoWK1HF6hhy/it/u=2534506313,1688529724&fm=26&gp=0.jpg
          commit('SET_AVATAR', './static/img/admin.jpg');
          commit('SET_INTRODUCTION', data.description);
          const menus = {};
          for (let i = 0; i < data.menus.length; i++) {
            menus[data.menus[i].code] = true;
          }
          commit('SET_MENUS', menus);
          const elements = {};
          for (let i = 0; i < data.elements.length; i++) {
            elements[data.elements[i].code] = true;
          }
          commit('SET_ELEMENTS', elements);
           //获取一级菜单
        getfirstTopMenus(state.token).then(response => {
          //  console.log(response);
            commit('SET_FIRSTTOP_MENUS', response);
            let obj ={'goto':'initop'};
            setTimeout(()=>{
                Btns.$emit('goinittopbtn',obj);
            },100);
           // resolve(response);
          });
           resolve(response);
        }).catch(error => {
          reject(error);
        });
        getMenus(state.token).then(response => {
          //console.log(response)
          //response[0].children[4].children[0].code = 'backdevice?backNext=1234&userId=12345&path=backdevice'
          commit('SET_PERMISSION_MENUS', response);
          //console.log(12345678)
          // let obj ={'goto':'initop'};
          // setTimeout(()=>{
          //     Btns.$emit('goinittopbtn',obj);
          // },100);
         
        });
       
      });
    },
    //拉取路由表 待验证
    loginGetUserRouter({
      commit,
      state
    }, code){
      return new Promise((resolve, reject) => {
        getUserRouter(state.token).then(response => {
          //console.log(response);
          resolve(response);
        }).catch(error => {
          reject(error);
        });
      });
    },
    // 第三方验证登录
    LoginByThirdparty({
      commit,
      state
    }, code) {
      return new Promise((resolve, reject) => {
        commit('SET_CODE', code);
        loginByThirdparty(state.status, state.email, state.code).then(response => {
          commit('SET_TOKEN', response.data.token);
          setToken(response.data.token);
          resolve();
        }).catch(error => {
          reject(error);
        });
      });
    },

    // 登出
    LogOut({
      commit,
      state
    }) {
      return new Promise((resolve, reject) => {
        logout(state.token).then(() => {
          commit('SET_TOKEN', '');
          commit('SET_ROLES', []);
          commit('SET_MENUS', undefined);
          commit('SET_ELEMENTS', undefined);
          commit('SET_PERMISSION_MENUS', undefined);
          removeToken();
          //清除所有的本地存储数据
          removelocalStorage();
          resolve();
        }).catch(error => {
          reject(error);
        });
      });
    },

    // 前端 登出
    FedLogOut({
      commit
    }) {
      return new Promise(resolve => {
        commit('SET_TOKEN', '');
        commit('SET_MENUS', undefined);
        commit('SET_ELEMENTS', undefined);
        commit('SET_PERMISSION_MENUS', undefined);
        removeToken();
        //清除所有的本地存储数据
        removelocalStorage();
        resolve();
      });
    },

    // 动态修改权限
    ChangeRole({
      commit
    }, role) {
      return new Promise(resolve => {
        commit('SET_ROLES', [role]);
        commit('SET_TOKEN', role);
        setToken(role);
        resolve();
      })
    },
    getPermissionMenus({ commit }, preIndex){
     // console.log(preIndex);
     if(preIndex){
    //  (preIndex == '1' || preIndex == '2' )?preIndex=preIndex:preIndex=0;
    //(preIndex != '7' )?preIndex=preIndex:preIndex=0;
     // console.log(preIndex)

      commit('GET_PERMISSION_MENUS', preIndex);
    }

    }
  }
};

export default user