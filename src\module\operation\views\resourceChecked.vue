<template>
  <div class="app-container">

    <template v-if="showContent == 'list'">
      <el-form :inline="true" :model="queryForm" size="small" label-width="100px" class="demo-form-inline">
        <el-form-item label="地市:">
          <el-select v-model="queryForm.cityId" :disabled="cityDisabled" @change="cityChange" filterable clearable
            placeholder="请选择">
            <el-option v-for="(item, index) in citylist" v-show="index !== 0" :key="item.cityId" :label="item.cityName"
              :value="item.cityId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="区域:">
          <el-select v-model="queryForm.areaId" :disabled="areaDisabled" @change="areaChange" filterable clearable
            placeholder="请选择">
            <el-option v-for="item in areaList" :key="item.areaId" :label="item.areaName"
              :value="item.areaId"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="局站:">
          <el-select v-model="queryForm.siteId" @change="siteChange" :remote-method="getSite" filterable clearable
            placeholder="请选择">
            <el-option v-for="item in siteList" filterable :key="`${item.area_id}-${item.site_id}`"
              :label="item.site_name" :value="item.site_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="机房:">
          <el-select v-model="queryForm.roomId" :remote-method="getRoom" filterable clearable placeholder="请选择">
            <el-option v-for="item in roomlist" :key="`${item.room_id}-${item.site_id}`" :label="item.room_name"
              :value="item.room_id"></el-option>
          </el-select>
        </el-form-item>
        <el-form-item label="">
          <el-button style="margin-left: 60px;" @click="queryFormReset">重置</el-button>
          <el-button type="primary" @click="queryData(1, 10)">查询</el-button>
          <el-button type="primary" @click="devExport" :loading="exportloading">导出</el-button>

        </el-form-item>
      </el-form>
      <el-table :data="deviceTableData" header-row-class-name="myHeaderClass" size="small" v-loading="tableLoading">
        <el-table-column prop="cityName" align="center" label="地市"></el-table-column>
        <el-table-column prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
        <el-table-column prop="siteName" align="center" label="局站" show-overflow-tooltip></el-table-column>
        <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>

        <el-table-column prop="ossNum" align="center" label="中资设备数" show-overflow-tooltip>
          <template slot-scope="scope">
            <div style="cursor: pointer;text-decoration-line: underline;color: #409eff;"
              @click="queryZZData(scope.row)">{{ scope.row.ossNum }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="dhNum" align="center" label="中台设备数" show-overflow-tooltip>
          <template slot-scope="scope">
            <div style="cursor: pointer;text-decoration-line: underline;color: #409eff;"
              @click="queryZTData(scope.row)">{{ scope.row.dhNum }}</div>
          </template>
        </el-table-column>
        <el-table-column prop="statTime" align="center" label="核查时间" show-overflow-tooltip></el-table-column>
      </el-table>
      <div class="pag">
        <el-pagination @size-change="handleSizeChange" background @current-change="handleCurrentChange"
          :current-page="currentPage" :page-sizes="[10, 20, 50]" :page-size="pageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="total">
        </el-pagination>
      </div>
    </template>

    <el-page-header v-if="['ZTDevice', 'ZZDevice'].includes(showContent)" @back="goback">
      <div slot="content" style="font-size: 14px;">
        {{ content }}
      </div>
    </el-page-header>

    <!-- 中台设备 dh-->
    <template v-if="showContent == 'ZTDevice'">
      <div style="display: flex;justify-content: end;margin: 12px 0;">
        <el-button type="primary" size="small" @click="detailExport('DH')" :loading="exportloading">导出</el-button>
      </div>

      <el-table :data="ZTDeviceList" header-row-class-name="myHeaderClass" size="small" v-loading="tableLoading">
        <el-table-column prop="cityName" align="center" label="地市"></el-table-column>
        <el-table-column prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
        <el-table-column prop="siteName" align="center" label="局站" show-overflow-tooltip></el-table-column>
        <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
        <el-table-column prop="suId" align="center" label="SU_ID" show-overflow-tooltip></el-table-column>
        <el-table-column prop="suIp" align="center" label="SU_IP" show-overflow-tooltip></el-table-column>
        <el-table-column prop="deviceId" align="center" label="设备ID" show-overflow-tooltip></el-table-column>
        <el-table-column prop="deviceName" align="center" label="设备名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="registTime" align="center" label="注册时间" show-overflow-tooltip></el-table-column>
      </el-table>
      <div class="pag">
        <el-pagination @size-change="ZThandleSizeChange" background @current-change="ZThandleCurrentChange"
          :current-page="ZTcurrentPage" :page-sizes="[10, 20, 50]" :page-size="ZTpageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="ZTtotal">
        </el-pagination>
      </div>
    </template>

    <!-- 中资设备 oss-->
    <template v-if="showContent == 'ZZDevice'">
      <div style="display: flex;justify-content: end;margin: 12px 0;">
        <el-button type="primary" size="small" @click="detailExport('OSS')" :loading="exportloading">导出</el-button>
      </div>
      <el-table :data="ZZDeviceList" header-row-class-name="myHeaderClass" size="small" v-loading="tableLoading">
        <el-table-column prop="cityName" align="center" label="地市"></el-table-column>
        <el-table-column prop="areaName" align="center" label="区域" show-overflow-tooltip></el-table-column>
        <el-table-column prop="siteName" align="center" label="局站" show-overflow-tooltip></el-table-column>
        <el-table-column prop="roomName" align="center" label="机房" show-overflow-tooltip></el-table-column>
        <el-table-column prop="resManNo" align="center" label="资管编码" show-overflow-tooltip></el-table-column>
        <el-table-column prop="deviceId" align="center" label="设备编码" show-overflow-tooltip></el-table-column>
        <el-table-column prop="deviceName" align="center" label="设备名称" show-overflow-tooltip></el-table-column>
        <el-table-column prop="registTime" align="center" label="投入使用时间" show-overflow-tooltip></el-table-column>
      </el-table>
      <div class="pag">
        <el-pagination @size-change="ZZhandleSizeChange" background @current-change="ZZhandleCurrentChange"
          :current-page="ZZcurrentPage" :page-sizes="[10, 20, 50]" :page-size="ZZpageSize"
          layout="total, sizes, prev, pager, next, jumper" :total="ZZtotal">
        </el-pagination>
      </div>
    </template>
  </div>
</template>

<script>
import exportTableToExcel from './components/exportExcel.js'
export default {
  data() {
    return {
      // 查询条件下拉数据
      citylist: [],
      areaList: [],
      siteList: [],
      roomlist: [],
      deviceTypeList: [],
      cedianlist: [],
      cedianNameList: [],
      devoptions: [],
      tableLoading: false,
      queryForm: {
        areaId: '',
        cityId: '',

        roomId: '',
        siteId: '',

      },
      deviceTableData: [],
      pageSize: 10,
      currentPage: 1,
      total: 0,
      content: '',
      currentRow: null,
      exportloading: false,
      role: '',
      cityDisabled: false,
      areaDisabled: false,

      showContent: 'list', // list 一级列表 ZTDevice 中台设备 ZZDevice 中资设备
      // 中资设备
      ZZDeviceList: [],
      ZZpageSize: 10,
      ZZcurrentPage: 1,
      ZZtotal: 0,

      // 中资设备
      ZTDeviceList: [],
      ZTpageSize: 10,
      ZTcurrentPage: 1,
      ZTtotal: 0,
    }
  },
  created() {
    // this.role = sessionStorage.getItem('role')
    // this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
    // this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''

    // if (this.role == '1') {
    //   this.cityDisabled = false
    //   this.areaDisabled = false
    // }

    // if (['94', '95'].includes(this.role)) {
    //   this.cityDisabled = true
    //   this.areaDisabled = this.queryForm.areaId ? true : false
    // }
    this.queryCityList()
    this.getSite()
    this.getRoom()

    this.queryData()
  },
  methods: {
    goback() {
      this.showContent = 'list'
      this.ZTcurrentPage = 1
      this.ZZcurrentPage = 1
      this.ZTtotal = 0
      this.ZZtotal = 0

    },
    // 一级列表导出
    async devExport() {
      this.exportloading = true
      let data = [
        ['地市', '区域', '局站', '机房', '中资设备数', '中台设备数', '核查时间']
      ]
      let params = {
        ...this.queryForm,
        current: 1,
        size: this.total
      }
      try {
        let res = await this.$api.checkSum(params)
        if (res.code == 200) {
          res.data.records.forEach(v => {
            let item = [
              v.cityName,
              v.areaName,
              v.stationName,
              v.roomName,
              v.ossNum,
              v.dhNum,
              v.statTime
            ]
            data.push(item)
          })
          exportTableToExcel(data, '中资资源核查表')
          this.exportloading = false
        } else {
          this.$message.error(res.msg)
          this.exportloading = false
        }
      } catch (error) {
        this.$message.error('导出异常')
        this.exportloading = false
      }

    },
    // 详情列表导出
    async detailExport(type) {
      this.exportloading = true

      let oss = [ // 中资
        ['地市', '区域', '局站', '机房', '资管编码', '设备编码', '设备名称', '投入使用时间']
      ]
      let ossKey = ['cityName', 'areaName', 'siteName', 'roomName', 'resManNo', 'deviceId', 'deviceName', 'registTime']
      let dh = [ // 中台
        ['地市', '区域', '局站', '机房', 'SU_ID', 'SU_IP', '设备ID', '设备名称', '注册时间']
      ]
      let dhKey = ['cityName', 'areaName', 'siteName', 'roomName', 'suId', 'suIp', 'deviceId', 'deviceName', 'registTime']
      let data = type == 'OSS' ? oss : dh
      let dataKey = type == 'OSS' ? ossKey : dhKey
      let fileName = type == 'OSS' ? '中资设备明细' : '中台设备明细'
      let params = {
        ...this.currentRow,
        type: type,
        current: 1,
        size: this.total
      }
      try {
        let res = await this.$api.checkDetail(params)
        if (res.code == 200) {
          res.data.records.forEach(v => {
            let item = []
            dataKey.forEach(key => {
              item.push(v[key])
            })
            data.push(item)
          })
          console.log(data);

          exportTableToExcel(data, fileName)
          this.exportloading = false
        } else {
          this.$message.error(res.msg)
          this.exportloading = false
        }
      } catch (error) {
        this.$message.error('导出异常')
        this.exportloading = false
      }

    },
    // 初始化地市
    queryCityList() {
      let params = {
        cityName: "",
        pageNum: 1,
        pageSize: 999
      }
      this.$api.queryCityList(params).then(res => {
        if (res.code == 200) {
          this.citylist = res.data.records
          this.cfgcitylist = res.data.records
          // if (this.queryForm.cityId) {
          //   this.areaList = this.citylist.find(v => v.cityId == this.queryForm.cityId).areaList
          // }
        }
      })
    },
    cityChange(val) {
      this.queryForm.areaId = ''
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.queryForm.deviceName = ''
      this.areaList = []
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      this.devoptions = []
      this.areaList = this.citylist.find(v => v.cityId == val).areaList
      this.getSite()
      this.getRoom()

    },
    areaChange() {
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.queryForm.deviceName = ''
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      this.devoptions = []
      this.getSite()
      this.getRoom()

    },
    siteChange() {
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.queryForm.deviceName = ''
      this.roomlist = []
      this.deviceTypeList = []
      this.devoptions = []
      this.getRoom()

    },


    getSite(val) {
      this.queryForm.siteId = ''
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.siteList = []
      this.roomlist = []
      this.deviceTypeList = []
      let params = {
        cityId: this.queryForm.cityId,
        areaId: this.queryForm.areaId,
        siteName: val,
        pageNum: 1,
        pageSize: 999
      }

      this.$api.querySiteInfo(params).then(res => {
        if (res.code == 200) {
          this.siteList = res.data.records
        }
      })
    },
    getRoom(val) {
      this.queryForm.roomId = ''
      this.queryForm.deviceType = ''
      this.roomlist = []
      this.deviceTypeList = []
      let params = {
        cityId: this.queryForm.cityId,
        areaId: this.queryForm.areaId,
        siteId: this.queryForm.siteId,
        roomName: val,
        pageNum: 1,
        pageSize: 999
      }
      this.$api.queryRoomInfo(params).then(res => {
        if (res.code == 200) {
          this.roomlist = res.data.records
        }
      })
    },
    queryFormReset() {
      this.areaList = []
      this.siteList = []
      this.roomlist = []
      this.queryForm = {
        areaId: '',
        cityId: '',
        roomId: '',
        siteId: '',
      }
      this.queryData()
      this.role = sessionStorage.getItem('role')
      this.queryForm.cityId = sessionStorage.getItem('cityId') ? sessionStorage.getItem('cityId') : ''
      this.queryForm.areaId = sessionStorage.getItem('areaId') ? sessionStorage.getItem('areaId') : ''

      if (this.role == '1') {
        this.cityDisabled = false
        this.areaDisabled = false
      }

      if (['94', '95'].includes(this.role)) {
        this.cityDisabled = true
      }
      this.queryCityList()
      this.getSite()
      this.getRoom()

      this.queryData()
      this.queryData(this.queryForm.current)

    },
    // 一级列表
    queryData(pageNum, pageSize) {
      this.tableLoading = true
      if (pageNum) this.currentPage = pageNum
      if (pageSize) this.pageSize = pageSize
      let params = {
        ...this.queryForm,
        current: pageNum || this.currentPage,
        size: pageSize || this.pageSize
      }
      this.$api.checkSum(params).then(res => {
        if (res.code == 200) {
          this.deviceTableData = res.data.records
          this.tableLoading = false
          this.total = res.data.total
        } else {
          this.tableLoading = false
          this.$message.error(res.msg)
        }
      })
    },
    handleSizeChange(val) {
      this.pageSize = val
      this.queryData()
    },
    handleCurrentChange(val) {
      this.currentPage = val
      this.queryData()
    },
    // 中资设备
    queryZZData(row) {
      this.currentRow = row
      this.ZZDeviceList = []
      this.ZZtotal = []
      this.showContent = 'ZZDevice'
      this.content = "中资设备明细"
      this.ZZtableLoading = true

      let params = {
        ...row,
        type: 'OSS',
        current: this.ZZcurrentPage,
        size: this.ZZpageSize
      }
      this.$api.checkDetail(params).then(res => {
        if (res.code == 200) {
          this.ZZDeviceList = res.data.records
          this.ZZtotal = res.data.total
          this.ZZtableLoading = false

        } else {
          this.ZZtableLoading = false
          this.$message.error(res.msg)
        }
      })
    },
    ZZhandleSizeChange(val) {
      this.ZZpageSize = val
      this.queryZZData(this.currentRow)
    },
    ZZhandleCurrentChange(val) {
      this.ZZcurrentPage = val
      this.queryZZData(this.currentRow)
    },
    // 中台设备
    queryZTData(row) {
      this.currentRow = row
      this.ZTDeviceList = []
      this.showContent = 'ZTDevice'
      this.content = "中台设备明细"
      this.ZTtableLoading = true
      let params = {
        ...row,
        type: 'DH',
        current: this.ZTcurrentPage,
        size: this.ZTpageSize
      }
      this.$api.checkDetail(params).then(res => {
        if (res.code == 200) {
          this.ZTDeviceList = res.data.records
          this.ZTtableLoading = false
          this.ZTtotal = res.data.total
        } else {
          this.ZTtableLoading = false
          this.$message.error(res.msg)
        }
      })
    },
    ZThandleSizeChange(val) {
      this.ZTpageSize = val
      this.queryZTData(this.currentRow)
    },
    ZThandleCurrentChange(val) {
      this.ZTcurrentPage = val
      this.queryZTData(this.currentRow)
    }
  }
}
</script>

<style lang="scss" scoped>
.ad {
  display: flex;
  gap: 6px;

  i {
    cursor: pointer;
  }
}

.pag {
  display: flex;
  flex-direction: row-reverse;
  margin-top: 12px;
}

.selectWrap {
  width: 600px;
  height: 60px;
  margin-left: 100px;
  overflow: scroll;
  margin-bottom: 16px;
  //box-shadow: 0 2px 4px rgba(0, 0, 0, .12), 0 0 6px rgba(0, 0, 0, .04)
  border: 1px solid #d7dae2;
}
</style>