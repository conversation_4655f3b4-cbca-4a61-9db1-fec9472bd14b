<template>

  <div class="login-wrap">
    <div class="logo-box">
      <img :src="logoImageUrl" alt="">
      <span>陕西动环采控模块</span>
    </div>
    <div class="login-box">
      <div class="login-box-left" :style="{ backgroundImage: `url(${leftBigImageUrl})` }"></div>
      <div class="login-box-right">
        <div class="login-form">

          <div class="login-form-title">
            <div class="login-form-title-tips">欢迎登陆</div>
            <div class="login-form-title-name">陕西动环采控模块</div>
            <!-- <div v-show="!isError" class="kck">{{productName}}</div> -->
          </div>
          <!-- <div v-if="isError" class="my_error">
          <div class="my_error_1">
            <span class="my_error_1_1">
              <i class="el-icon-close"></i>
            </span>
            <span class="my_error_1_2">{{ errMsg }}</span>
          </div>
          <div class="my_error_2" @click="errMsg = ''; isError = false">
            <i class="el-icon-close"></i>
          </div>
        </div> -->

          <el-form v-if="!showMessageLogin" size="small" ref="formData" class="login-form-el-form"
            :label-position="labelPosition" label-width="0" :model="formData" :rules="rules">

            <el-form-item prop="username">
              <el-input placeholder="请输入账号" class="my-input" v-model="formData.username">
                <i class="iconfont" slot="prefix">&#xe628;</i>
              </el-input>
            </el-form-item>
            <el-form-item prop="password">
              <el-input placeholder="请输入密码" class="my-input" v-model="formData.password" show-password>
                <div slot="prefix">
                  <i class="iconfont">&#xe629;</i>
                </div>
              </el-input>
            </el-form-item>
            <el-form-item prop="verify">
              <el-input placeholder="验证码" style="width: 176px;" class="my-input" v-model="formData.verify"
                @keyup.enter.native="loginSubmit">
                <i class="iconfont" slot="prefix">&#xe62a;</i>
              </el-input>
              <div class="verify-code-img">
                <SIdentify :identifyCode='identifyCode' @click.native.prevent="getCode" :contentWidth="130"></SIdentify>
                <!-- <img v-bind:src="verifySrc" v-bind:alt="verifyValue" @click="initQrcode"> -->
              </div>
            </el-form-item>
            <!-- <el-form-item prop="rember">
              <div class="rember">
               <el-checkbox  style="color: #fff;" v-model="checked">记住账号</el-checkbox>
                <span>忘记密码?</span>
              </div>
            </el-form-item> -->
            <el-form-item>
              <el-button class="login-form-submit" size="medium" type="primary" @click="loginSubmit"
                :disabled="logging">
                <span v-if="!logging">登 录</span>
                <span v-else>正在登录...</span>
              </el-button>
            </el-form-item>
          </el-form>

        </div>
      </div>
    </div>
    <div style="height: 64px"></div>
  </div>


</template>

<script>
import encryption from '@/utils/encryption'
import SIdentify from './../components/Sidentify/SIdentify.vue'
import { mapMutations, mapState } from "vuex";
export default {
  components: {
    SIdentify
  },
  data() {
    return {
      phone: "",
      showMessageLogin: false,
      countdown: 60,
      btnState: 1, // 1 发送中(灰色) 2 倒计时中  3 重新发送
      messageVerity: '', // 短信验证码
      isError: false,
      errMsg: '',
      labelPosition: 'right',
      logging: false, // 正在登录标记
      isSecret: true, // 登录是否加密
      formData: {
        username: '',
        password: '',
        verify: '',
      },
      rules: {
        username: [{ required: true, message: '帐号不能为空', trigger: "change" }],
        password: [{ required: true, message: '密码不能为空', trigger: "change" }],
        verify: [{ required: true, message: '验证码不能为空', trigger: "change" }]
      },
      verifySrc: '',
      verifyValue: '',
      leftBigImageUrl: require('@/assets/images/login.svg'),  // 左侧大图url
      logoImageUrl: require('@/assets/images/logo1.png'),  // 左侧大图url
      systemName: '陕西动环采控平台', // 系统名称
      productName: '',
      identifyCode: this.randomRange(4),//验证码名字
    };
  },
  created() {
    sessionStorage.clear()
  },
  mounted() {
  },
  methods: {
    getCode() {
      let that = this;
      this.formData.verify = null;
      that.identifyCode = that.randomRange(4);
    },
    randomRange(min, max) {
      let returnStr = "",
        range = (max ? Math.round(Math.random() * (max - min)) + min : min),
        arr = ['0', '1', '2', '3', '4', '5', '6', '7', '8', '9'];

      for (var i = 0; i < range; i++) {
        let index = Math.round(Math.random() * (arr.length - 1));
        returnStr += arr[index];
      }
      return returnStr;
    },
    loginSubmit: function () {
      // this.$router.push({
      //          path: '/changeThePassword'
      //         });

      if (this.formData.verify !== this.identifyCode) {
        this.$message({
          message: "验证码不正确",
          type: "warning"
        });
        this.formData.verify = '';
        this.getCode();
        return;
      }

      // this.setIsLogin(true)

      // this.$router.push("/home");
      this.$refs.formData.validate(async valid => {
        if (valid) {
          this.errMsg = "";
          this.logging = true;

          // 加密
          let newPwd = encryption.encrypt(this.formData.password.toString())

          // 调用登录接口
          try {

            let params = {
              username: this.formData.username,
              password: newPwd,
            }

            this.$store.dispatch('LoginByEmail', params).then(dat => {
              this.logging = false;
              //isUpdate 1:强制修改密码  0不强制修改密码
              sessionStorage.setItem('role', dat.role)
              sessionStorage.setItem('cityId', dat?.areaId || '')
              sessionStorage.setItem('areaId', dat?.city || '')
              if (dat.flag == '1') {
                // this.$message({
                //   message: "",
                //   type: "warning",
                //   offset: 20
                // });
                this.$notify({
                  title: '请修改密码',
                  type: 'warning'
                });
                this.$router.push({
                  path: '/changeThePassword'
                });
              } else if (dat.flag == '9') {
                this.$message({
                  message: "账号已冻结，请联系管理员",
                  type: "warning"
                });
              } else {

                this.$store.dispatch('GetInfo').then(info => { // 拉取user_info
                  // console.log(info)
                  setTimeout(() => {
                    this.$router.push({
                    path: '/home'
                  });
                  }, 500)
                  
                }).catch(() => {
                  this.$store.dispatch('FedLogOut').then(() => {
                    this.$router.push({ path: '/login' });
                    // removelocalStorage();//清缓存
                  })
                })

              }
              // this.showDialog = true;
            }).catch((err) => {
              that.getCode();
              this.logging = false;
            });
          } catch (e) {
            this.logging = false;
          } finally {
            this.logging = false;
          }
        } else {
          // 前端验证未通过刷新验证码
          this.getCode();
        }
      })
    },

  },
};
</script>

<style scoped lang="scss">
.rember {
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  color: #fff;
}

.el-checkbox__input.is-checked+.el-checkbox__label {
  color: #fff !important;
}

.my-input {
  height: 32px;
}

.login-wrap {
  display: flex;
  flex-direction: column;
  height: 100vh;
  min-height: 600px;

  .logo-box {
    height: 84px;
    display: flex;
    align-items: center;
    padding-left: 36px;

    img {
      height: 44px;
    }

    span {
      font-family: YouSheBiaoTiHei;
      font-size: 32px;
      color: #2D3E53;
      margin-left: 24px;
    }
  }

  .login-box {
    flex: 1
  }
}

.messsagev {
  width: 346px;
  display: flex;
  flex-direction: row;
  justify-content: space-between;
  margin-bottom: 12px;
  margin-top: 12px;
}

.login-form-submit {
  width: 100%;
  height: 40px;
  background-color: #169bfa;
  margin-top: 8px;

  span {
    font-size: 16px;
  }
}

.login-box {
  width: 100%;
  display: flex;
  position: relative;

  .login-form-logo {
    position: absolute;
    top: 54px;
    left: 48px;
    width: 200px;
    height: 66px;

    img {
      width: 100%;
      height: auto;
    }
  }

  .logo-title {
    position: absolute;
    top: 60px;
    left: 268px;
    width: 500px;
    height: 50px;
    font-size: 36px;
    color: #fff;
  }

  .login-box-left {
    width: 100%;
    //background: url("../../../assets/images/photo2.png") center center no-repeat;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .login-box-right {
    width: 428px;
    // height: 488px;
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    right: 65px;
    // background-color: #fff;
    background: linear-gradient(137deg, rgba(232, 238, 255, 0.1) 0%, rgba(162, 182, 243, 0.1) 100%);
    border-radius: 4px 4px 4px 4px;
    display: flex;
    min-width: 380px;
    justify-content: center;
    align-items: center;
    padding: 74px 54px;
    box-sizing: border-box;
    border-radius: 6px;

    .login-form {
      width: 100%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;

      .login-form-title {
        width: 100%;
        margin-bottom: 20px;
        color: #000;
        font-size: 20px;
        font-weight: bold;

        .kck {
          font-size: 14px;
          font-family: ArialMT;
        }

        .login-form-title-tips {

          font-size: 32px;
          font-weight: 500;
          color: #fff;
          margin-bottom: 4px;
        }

        .login-form-title-name {

          color: #A1C7F5;
          font-size: 24px;
          font-weight: bold;

        }
      }

      .login-form-el-form {
        width: 100%;

        .verify-code-img {
          border-radius: 4px;
          overflow: hidden;
          position: absolute;
          top: 0;
          right: 0;

          img {
            cursor: pointer;
            height: 24px;
            width: 100px;
          }
        }


        .apply-test {
          background: rgba(72, 133, 247, 0.12);
          border: 1px solid rgba(72, 133, 247, 0.12);
          color: #4885F7;

          &:hover {
            background: rgba(72, 133, 247, 0.22);
          }
        }
      }
    }
  }
}
</style>
