<template>
  <el-container class="container">
    <el-header height="56px" style="background-color: #10192E;color: #fff;">
      <div class="top-left">
        <div class="logo1">
          <img height="32px" :src="logoImageUrl" alt="">
          <span class="logo-title">陕西动环采控模块</span>
        </div>

        <!-- <div style="font-size: 14px;margin: 0 16px;" @click="handleSelect('/operation')">设备运维</div> -->
        <el-menu v-if="menuList.length" style="margin-left: 12px;" router :default-active="activeIndex"
          @select="handleSelect" mode="horizontal" text-color="#727C94" active-text-color="#fff"
          background-color="#10192E">
          <menutree :menuList="menuList"></menutree>
        </el-menu>
        <div class="top-right">
          <el-popover width="400" trigger="click">
            <div>
              <el-table size="mini" :data="tableData" :height="210" ref="multipleTable" style="width: 100%" :fit=true>
                <el-table-column v-for="(row, index) in columnDatas" :key="index" :prop="row.field" :label="row.title"
                  :min-width="row.columnWidth" show-overflow-tooltip>
                </el-table-column>
                <el-table-column align="center" label="" width="80">
                  <template slot-scope="scope">
                    <el-button type="text" size="small" @click.stop="handletovivew(scope.row)">详情</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <div class="pagination cleafix tablPaginat" style="margin-top: 15px;margin-bottom:0px; ">
                <el-pagination small @size-change="handleSizeChange" @current-change="handleCurrentChange"
                  :page-sizes="[10, 15, 20, 50]" :current-page.sync="pageIndex" :page-size.sync="pageSize"
                  layout="total,  prev, pager, next " :total="totalCount">
                </el-pagination>
              </div>
            </div>
            <i slot="reference" class="el-icon-bell" style="font-size: 16px;margin-right: 26px;"></i>
          </el-popover>

          <el-dropdown trigger="click" :hide-on-click="false">
            <div class="my_dropdown">
              <el-avatar icon="el-icon-user-solid" style="margin-right: 8px;" size="small"></el-avatar>
              <span class="el-dropdown-link" style="color: #fff;">
                {{ name }}<i class="el-icon-arrow-down el-icon--right"></i>
              </span>
            </div>

            <el-dropdown-menu slot="dropdown">
              <el-dropdown-item>
                <span @click="goChangePassword">修改密码</span>
              </el-dropdown-item>
              <el-dropdown-item>
                <el-popconfirm @confirm="logout" placement="left" confirm-button-text='确定' width="180"
                  cancel-button-text='关闭' :hide-icon="true" title="确定退出吗？">
                  <div slot="reference" @click="openFlag = true">
                    <span>退出账号</span>
                  </div>
                </el-popconfirm>
              </el-dropdown-item>

            </el-dropdown-menu>
          </el-dropdown>

          <!-- 
                    <el-avatar icon="el-icon-user-solid" size="small"></el-avatar>
                    <span >{{ name }}</span> -->



          <!-- <el-popconfirm @confirm="logout" confirm-button-text='确定' width="180" cancel-button-text='关闭'
                        :hide-icon="true" title="确定退出吗？">
                        <i @click="openFlag = true" slot="reference" class="el-icon-switch-button"
                            style="font-size: 18px;"></i>
                    </el-popconfirm> -->

          <!-- <i  class="el-icon-bell" style="font-size: 18px;margin-left: 4px;"></i> -->
        </div>
      </div>

      <el-dialog :title="dialogtitle" width="800px" :visible.sync="dialogEditVisible" top="5vh" class=""
        :append-to-body="false" :close-on-click-modal="false">

        <div class="noticeTitle">{{ this.noticedetial.notice_title }}</div>
        <div class="ql-container ql-snow" style="border: none;">
          <div class=" ql-editor">
            <div v-html="this.noticedetial.notice_info"></div>
          </div>
        </div>
        <div style="padding: 0 15px;display: flex;">
          <div>附件下载：</div>
          <a v-for="item in fileList" :key="item.id" :download="item.file_name"
            :href="`/upload/${item.file_id}.${item.file_name.split('.')[1]}`"
            style="text-decoration: underline; color: #409EFF; font-style: italic">
            {{ item.file_name }}</a>
        </div>
      </el-dialog>
    </el-header>
    <el-breadcrumb v-if="breadcrumb.length" class="myBread" separator-class="el-icon-arrow-right">
      <el-breadcrumb-item class="itemBread" v-for="(item, index) in breadcrumb" :key="index">{{ item
        }}</el-breadcrumb-item>
    </el-breadcrumb>
    <div class="wrap">
      <router-view></router-view>
    </div>
  </el-container>
</template>

<script>
import 'quill/dist/quill.core.css';
import 'quill/dist/quill.snow.css';
import 'quill/dist/quill.bubble.css';
import { mapGetters } from "vuex";
import menutree from '../components/menuItem.vue';
import dayjs from 'dayjs'
import { selNoticePage } from '@/api/admin/user/index';
import { getFilesTabObj } from '@/api/form/form/index';
export default {
  name: "Container",
  components: {
    menutree
  },
  data() {
    return {
      logoImageUrl: require('@/assets/images/logo2.png'),  // 左侧大图url
      menuList: [],
      activeIndex: '',
      pageIndex: 1,
      pageSize: 10,
      totalCount: 0,
      tablelist: {
        pageIndex: 1,
        pageSize: 10,
        totalCount: 0,
      },
      tableData: [],
      columnDatas: [
        { 'field': 'notice_title', title: '公告标题', 'columnWidth': '120' },
        // { 'field': 'name', title: '发布人', 'columnWidth': '100' },
        { 'field': 'create_time', title: '发布时间', 'columnWidth': '140' },
      ],
      dialogEditVisible: false,
      dialogtitle: '公告详情',
      noticedetial: {},
      fileList: '',
      breadcrumb: []
    };
  },
  computed: {
    ...mapGetters([
      'name'
    ])
  },
  created() {

    this.queryData()

  },
  mounted() {
    this.menuList = JSON.parse(sessionStorage.getItem('permissionMenus')) || []
    // console.log( this.menuList)
    this.activeIndex = sessionStorage.getItem('activeIndex') || ''
    if (!this.activeIndex) {
      this.showFirstMenu(this.menuList)
    }
    this.$router.push(this.activeIndex)

    this.breadcrumb = JSON.parse(sessionStorage.getItem('breadcrumb')) || []
  },
  watch: {
    '$route'(to, from) {
      // 路由变化时会触发这个函数
      // 'to' 和 'from' 是两个路由对象，可以用来获取即将进入和离开的路由信息
      this.breadcrumb = to?.meta?.title || []
      sessionStorage.setItem('breadcrumb', JSON.stringify(this.breadcrumb))
      //  console.log('路由变化了:',to.path, this.breadcrumb);

    }
  },
  methods: {
    goChangePassword() {
      this.$router.push({
        path: '/changeThePassword'
      });
    },
    async getFilesTabeData(noticeId) {//查询附件

    },
    // 分页pagesize
    handleSizeChange(val) {
      this.pageSize = val;
      this.queryData()
    },
    // 分页pageindex
    handleCurrentChange(val) {
      this.pageIndex = val;
      this.queryData()
    },
    // 查询数据
    queryData() {
      let params = {
        'page': this.pageIndex,
        'limit': this.pageSize,
      }

      selNoticePage(params).then((res) => {
        // console.log(res)
        if (res.status == 200) {
          let dat = res.data
          this.totalCount = dat.total;
          this.tableData = dat.rows.map(v => {
            v.create_time = dayjs(v.create_time).format("YYYY-MM-DD HH:mm:ss")
            return v
          });
        }

      }).catch((err) => {

      })
    },
    async handletovivew(row) {
      this.noticedetial = row
      let params = { noticeId: row.id }
      let res = await getFilesTabObj(params)
      if (res.code == 200) {
        this.fileList = res.data
      }
      this.dialogEditVisible = true
    },
    showFirstMenu(menuList) {
      if (this.activeIndex) return
      for (let index = 0; index < menuList.length; index++) {
        const item = menuList[index];
        if (item.children.length) {
          this.showFirstMenu(item.children)
        }
        if (!item.children.length) {
          this.activeIndex = item.href
        }
      }
      sessionStorage.setItem('activeIndex', this.activeIndex)
    },

    handleSelect(val) {
      console.log(val)
      sessionStorage.setItem('activeIndex', val)
      // this.$router.push(val)
    },
    logout() {
      this.$store.dispatch('FedLogOut').then(() => {
        this.$router.push('/login')
      })
    },
    toPath() {
      this.$router.push('/operation')
    }
  },
};
</script>

<style lang="scss" scoped>
.logo1 {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.logo-title {
  font-family: 'YouSheBiaoTiHei';
  font-size: 20px;
  margin-left: 14px;

}

.my_dropdown {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.noticeTitle {
  font-size: 18px;
  font-weight: bold;
  text-align: center;
}

.top-left {
  width: 100%;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;

}

.container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

.top-right {
  display: flex;
  flex-direction: row;
  align-items: center;
}

.el-header {
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: space-between;
  padding: 0 24px;
}

.wrap {
  background: #fff;
  width: 100%;
  flex: 1;
  // min-height: calc(100vh - 106px);

  overflow: scroll;
}

/* 如果使用SASS/SCSS */
.el-menu--horizontal::v-deep .el-submenu.is-active .el-submenu__title {
  border-bottom: 4px solid #169BFA !important;
  border-bottom-color: #169BFA !important;
}

.el-menu--horizontal::v-deep .el-menu-item.is-active {
  border-bottom: 4px solid #169BFA !important;
  border-bottom-color: #169BFA !important;

}
</style>
