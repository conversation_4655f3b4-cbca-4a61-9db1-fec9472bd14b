/**
 * @file 本文件为服务调用对象配置实现
 * <AUTHOR> TEAM.
 * * 配置项目说明:
 * commonConfig -- 默认服务调用对象配置
 */

let baseURL = ''
if (process.env.NODE_ENV === 'development') { // 开发环境
  baseURL = ''
} else if (process.env.NODE_ENV === 'production') { // 生产环境
  baseURL = '/gateway'
}

/**
 * 服务调用对象配置
 * @type {Object}
 */
const conf = {
  commonConfig: {
    baseURL: baseURL,
    //timeout: 60000,
  }
}

export default conf
