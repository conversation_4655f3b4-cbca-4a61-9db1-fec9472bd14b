import Cookies from 'js-cookie'

//const TokenKey = 'Admin-Token'
const TokenKey = 'towerNumber-Token'
const thirdUserId = 'thirdUserId';
const sysName = 'sysName';
const thirdProvCode = 'provinceCode';
const thirdCityCode = 'cityCode';
const thirdLevel ='thirdLevel'
const sysUserId = 'sysUserId'
const dlUserId = 'dlUserId'
const userIds = 'userIds'
const sysProvCode = 'sysProvCode';
const sysCityCode = 'sysCityCode';
const btnactive = "btnactive"
const btnactivepar = 'btnactivepar';
//permiss 资源登录后的权限  ROLE_NATION 集团 ROLE_PROVINCE 省分  ROLE_CITY 地市
const syspermiss = 'syspermiss';
const userpass = 'userpassword'
export function getToken() {
  return Cookies.get(TokenKey)
}

export function setToken(token) {
  return Cookies.set(TokenKey, token)
}
//第三方系统传过来的userId 省份 地市 信息
export function setThirdUserId(userId) {
  return Cookies.set(thirdUserId, userId)
}
export function getThirdUserId() {
  return Cookies.get(thirdUserId)
}

export function setThirdProvCode(provCode) {
  return Cookies.set(thirdProvCode, provCode)
}
export function getThirdProvCode() {
  return Cookies.get(thirdProvCode)
}

export function setThirdCityCode(cityCode) {
  return Cookies.set(thirdCityCode, cityCode)
}
export function getThirdCityCode() {
  return Cookies.get(thirdCityCode)
}
export function setThirdLevel(provCode,cityCode) {
  let level = ''
  if(provCode =='0' || provCode ==0 || provCode =='' || provCode ==undefined){//集团
      level = '1';
  }else{
     if(cityCode == 0 || cityCode == '0' || cityCode == undefined || cityCode ==''){//省级人员
      level = '2';
     }else{//地市
      level = '3';
     }
  };

  return Cookies.set(thirdLevel,level);
}
export function getThirdLevel() {
  return Cookies.get(thirdLevel);
}

export function setUserName(uesrName) {
  return Cookies.set(sysName, uesrName)
}
export function getUserName() {
  return Cookies.get(sysName)
}
export function getThirdUserName() {
  return Cookies.get(sysName)
}
export function getuserpass() {
  return localStorage.getItem(userpass);
}

export function setuserpass(pass) {
  return localStorage.setItem(userpass, pass)
}
// 本地系统的存入的省份 地市 userID
export function setSysUserId(userId) {
 // console.log(userId)
  return localStorage.setItem(sysUserId,userId)
}
//登录用户的ID
export function setUserIds(userId) {
  // console.log(userId)
  return localStorage.setItem(userIds, userId)
}
export function getUserIds() {
  return localStorage.getItem(userIds)
}
// 本地系统的存入的省份 地市 userID
export function setUserId(userId) {
  // console.log(userId)
  return localStorage.setItem(dlUserId, userId)
}
export function setBtnactive(active) {
  // console.log(active)
   return localStorage.setItem(btnactive,active);
 }
export function getBtnactive() {
  return localStorage.getItem(btnactive)
  //return Cookies.get(sysUserId)
}
export function setBtnactivepar(active) {
  // console.log(active)
   return localStorage.setItem(btnactivepar,active);
 }
export function getBtnactivepar() {
  return localStorage.getItem(btnactivepar)
  //return Cookies.get(sysUserId)
}
export function getSysUserId() {
  return localStorage.getItem(sysUserId)
  //return Cookies.get(sysUserId)
}
export function getUserId() {
  return localStorage.getItem(dlUserId)
}
export function setSysProvCode(provCode) {
  return localStorage.setItem(sysProvCode, provCode)
}
export function getSysProvCode() {
  return   localStorage.getItem(sysProvCode)       //Cookies.get(sysProvCode)
}

export function setSysCityCode(cityCode) {
  return localStorage.setItem(sysCityCode,cityCode)
}
export function getSysCityCode() {
  return localStorage.getItem(sysCityCode);       // Cookies.get(sysCityCode)
}
//保存 省 集团 地市 的级别
export function setSyspermiss(permiss) {
  return localStorage.setItem(syspermiss,permiss)
}
export function getSyspermiss() {
  return localStorage.getItem(syspermiss);       // Cookies.get(sysCityCode)
}
// localStorage.clear(); 清除所有的  localStorage 存储数据
export function removelocalStorage() {
  localStorage.clear();
  sessionStorage.clear()
}
// 清除 单个的localStorage 数据
export function removlocalSeparate(relocaName) {
  localStorage.removeItem(relocaName);
}

export function removeToken() {
  return Cookies.remove(TokenKey)
}
