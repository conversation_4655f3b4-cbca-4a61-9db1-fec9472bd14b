import http from '@/api/http'

export function getAllGroupTypes() {
  return http({
    url: '/api/admin/groupType/all',
    method: 'get'
  });
}

export function fetchTree(query) {
  return http({
    url: '/api/admin/group/tree',
    method: 'get',
    params: query
  });
}


export function addObj(obj) {
  return http({
    url: '/api/admin/group',
    method: 'post',
    data: obj
  });
}

export function getObj(id) {
  return http({
    url: '/api/admin/group/' + id,
    method: 'get'
  });
}

export function delObj(id) {
  return http({
    url: '/api/admin/group/' + id,
    method: 'delete'
  });
}

export function putObj(id, obj) {
  return http({
    url: '/api/admin/group/' + id,
    method: 'put',
    data: obj
  });
}

export function getUsers(id) {
  return http({
    url: '/api/admin/group/' + id + '/user',
    method: 'get'
  });
}

export function modifyUsers(id, data) {
  return http({
    url: '/api/admin/group/' + id + '/user',
    method: 'put',
    params: data
  });
}


export function removeElementAuthority(id, data) {
  return http({
    url: '/api/admin/group/' + id + '/authority/element/remove',
    method: 'put',
    params: data
  });
}

export function addElementAuthority(id, data) {
  return http({
    url: '/api/admin/group/' + id + '/authority/element/add',
    method: 'put',
    params: data
  });
}

export function getElementAuthority(id) {
  return http({
    url: '/api/admin/group/' + id + '/authority/element',
    method: 'get'
  });
}

export function modifyMenuAuthority(id, data) {
  return http({
    url: '/api/admin/group/' + id + '/authority/menu',
    method: 'put',
    params: data
  });
}


export function getMenuAuthority(id) {
  return http({
    url: '/api/admin/group/' + id + '/authority/menu',
    method: 'get'
  });
}
//
export function delGroup(obj) {
  return http({
    url: '/api/admin/group/delGroup',
    method: 'post',
    data: obj
  });
}
//查用户人员
export function getUserByNotGroup(obj) {
  return http({
    url: '/api/admin/user/getUserByNotGroup',
    method: 'post',
    data: obj
  });
}
//保存分组人员
export function addGroupUser(obj) {
  return http({
    url: '/api/admin/user/addGroupUser',
    method: 'post',
    data: obj
  });
}
//查看分组人员
export function getUserByGroup(obj) {
  return http({
    url: '/api/admin/user/getUserByGroup',
    method: 'post',
    data: obj
  });
}

//删除分配的人员
export function delGroupUser(obj) {
  return http({
    url: '/api/admin/user/delGroupUser',
    method: 'post',
    data: obj
  });
}
export function getqueryList(data) {
  return http({
    url: '/api/admin/tmTableDictController/queryList',
    method: 'get',
    params: data
  });
}
