<template>
  <div id="app">
    <router-view />
  </div>
</template>

<script>
// 注释
import { mapMutations } from 'vuex'
export default {
  name: 'App',
  data() {
    return {
      theme: '',
      
    }
  },
  methods: {
  
   
  },
  async mounted() {
  


  }
}
</script>

<style lang="scss">

#app {
  width: 100%;
  // height: 100%;
}
.test {
  width: 500px;
  height: 60px;
  line-height: 60px;
  background-color: rgba(0,0,0,.65);
  text-align: center;
  font-size: 40px;
  color: #fff;
  letter-spacing: 12px;
  font-weight: bold;
  text-shadow: 0 0 30px orange;
  text-align: center;
font-feature-settings: 'clig' off, 'liga' off;
text-shadow: 0px 2px 6px rgba(163, 86, 30, 0.80), 0px -3px 9px #A3561E, 0px -2px 4px rgba(163, 86, 30, 0.68), 0px -7px 4px rgba(163, 86, 30, 0.09);
font-family: PangMenZhengDao;
font-size: 24px;
font-style: normal;
font-weight: 400;
line-height: normal;
}
</style>
